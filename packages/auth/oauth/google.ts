import { Google, generateCodeVerifier, generateState } from 'arctic'
import { logger } from 'logs'
import { supabase } from '../../../apps/web/app/lib/supabaseClient'
import {
  createOauthCallbackHandler,
  createOauthRedirectHandler,
} from '../lib/oauth'

const CALLBACK_URL =
  process.env.NODE_ENV === 'production'
    ? 'https://www.imggen.org/api/oauth/google/callback'
    : 'http://localhost:3000/api/oauth/google/callback'

console.log(
  'faith=============process.env.GOOGLE_CLIENT_ID',
  process.env.GOOGLE_CLIENT_ID
)

export const googleAuth = new Google(
  process.env.GOOGLE_CLIENT_ID as string,
  process.env.GOOGLE_CLIENT_SECRET as string,
  CALLBACK_URL
)

const GOOGLE_PROIVDER_ID = 'google'

type GoogleUser = {
  sub: string
  email: string
  email_verified?: boolean
  picture?: string
  name: string
}
export const updateUser = async (
  username: string,
  email?: string,
  avatarUrl?: string
) => {
  const { data: existingUser, error: searchError } = await supabase
    .from('img4o_user')
    .select('*')
    .eq('email', email)
    .single()

  if (searchError && searchError.code !== 'PGRST116') {
    console.error('Error checking user:', searchError)
    throw searchError
  }
  console.log('existingUser', existingUser)

  if (existingUser) {
    const { data: updatedUser, error: updateError } = await supabase
      .from('img4o_user')
      .update({
        last_login_time: new Date().toISOString(),
        email: email || existingUser.email,
        avatar_url: avatarUrl || existingUser.avatar_url,
      })
      .eq('email', email)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating user:', updateError)
      throw updateError
    }

    return updatedUser
  }

  const { data: newUser, error: insertError } = await supabase
    .from('img4o_user')
    .insert([
      {
        username,
        email,
        avatar_url: avatarUrl,
        last_login_time: new Date().toISOString(),
        points: 8,
      },
    ])
    .select()
    .single()

  if (insertError) {
    console.error('Error creating user:', insertError)
    throw insertError
  }

  return newUser
}
export const googleRouteHandler = createOauthRedirectHandler(
  GOOGLE_PROIVDER_ID,
  () => {
    console.log('faith=============????-GOOGLE_PROIVDER_ID', GOOGLE_PROIVDER_ID)
    const state = generateState()
    const codeVerifier = generateCodeVerifier()
    const url = googleAuth.createAuthorizationURL(state, codeVerifier, [
      'profile',
      'email',
    ])
    return {
      state,
      url,
      codeVerifier,
    }
  }
)

export const googleCallbackRouteHandler = createOauthCallbackHandler(
  GOOGLE_PROIVDER_ID,
  async (code, verifier) => {
    // 更精确的环境判断
    const isLocal = process.env.NODE_ENV === 'development'

    console.log('faith=============isLocal-code-verifier', isLocal)

    let googleUser: GoogleUser

    if (isLocal) {
      // 本地环境：请求远程验证接口
      try {
        const response = await fetch(
          'https://www.imggen.org/api/oauth/google/validate',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              code,
              verifier,
            }),
          }
        )

        const { googleUser: remoteUser } = await response.json()
        console.log('faith=============remote user', remoteUser)
        googleUser = remoteUser
      } catch (error) {
        console.error('Remote validation failed:', error)
        throw new Error('Failed to validate with remote server')
      }
    } else {
      // 生产环境：使用本地验证逻辑
      try {
        const tokens = await googleAuth.validateAuthorizationCode(
          code,
          verifier as string
        )

        const googleUserResponse = await fetch(
          'https://openidconnect.googleapis.com/v1/userinfo',
          {
            headers: {
              Authorization: `Bearer ${tokens.accessToken()}`,
            },
          }
        )

        googleUser = await googleUserResponse.json()
      } catch (error) {
        console.error('Local validation failed:', error)
        throw new Error('Failed to validate locally')
      }
    }

    // 更新用户信息
    try {
      await updateUser(googleUser.name, googleUser.email, googleUser.picture)
    } catch (error) {
      logger.error('更新用户信息失败：', error)
    }

    // 返回用户信息
    return {
      id: googleUser.sub,
      email: googleUser.email,
      name: googleUser.name,
      avatar: googleUser.picture,
    }
  }
)
