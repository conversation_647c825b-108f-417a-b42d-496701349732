import { routing } from '@i18n/routing'
import createMiddleware from 'next-intl/middleware'
import type { NextRequest } from 'next/server'
import { handleGeoLocation } from './app/utils/geoLocation'

const intlMiddleware = createMiddleware(routing)

export default async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl
  const referer = req.headers.get('referer')
  const response = await intlMiddleware(req)
  // 如果是登录页面且有 referer

  if (pathname.includes('/auth/login') && referer) {
    try {
      const refererUrl = new URL(referer)

      // 确保 referer 来自同一网站且不是认证相关页面
      if (
        refererUrl.origin === req.nextUrl.origin &&
        !refererUrl.pathname.includes('/auth/') &&
        !refererUrl.pathname.includes('/api/')
      ) {
        console.log('保存返回路径:', refererUrl.pathname)

        // 获取响应并设置 cookie

        response.cookies.set('loginReturnPath', refererUrl.pathname, {
          path: '/',
          httpOnly: false, // 允许客户端 JavaScript 访问
          sameSite: 'strict',
          maxAge: 60 * 5, // 5分钟过期
        })

        return response
      }
    } catch (e) {
      console.error('处理 referer 失败:', e)
    }
  }

  // 使用封装的地理位置处理函数
  const {
    country,
    isEU,
    response: geoResponse,
  } = handleGeoLocation(req, response, {
    // 可以传入自定义选项
    cookieMaxAge: 60 * 60 * 24 * 30, // 比如改成30天
    cookieSameSite: 'lax',
  })

  console.log(country, 'country', isEU)

  return geoResponse
}
//

export const config = {
  matcher: ['/((?!api|_next|.*\\..*).*)'],
}
