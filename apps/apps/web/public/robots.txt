# 通用爬虫规则
User-Agent: *
Allow: /
Disallow: /api/
Disallow: /_next/
Disallow: /static/
Disallow: /404
Disallow: /500
Disallow: /*.json$
Disallow: /wechat

# Googlebot（Google搜索爬虫）
User-Agent: Googlebot
Allow: /
Disallow: /api/
Disallow: /_next/
Disallow: /static/
Disallow: /404
Disallow: /500
Disallow: /*.json$
Disallow: /wechat

# GPTBot（OpenAI大模型爬虫）
User-Agent: GPTBot
Allow: /
Disallow: /api/
Disallow: /_next/
Disallow: /static/
Disallow: /404
Disallow: /500
Disallow: /*.json$
Disallow: /wechat

# Anthropic-ai（Claude爬虫）
User-Agent: anthropic-ai
Allow: /
Disallow: /api/
Disallow: /_next/
Disallow: /static/
Disallow: /404
Disallow: /500
Disallow: /*.json$
Disallow: /wechat

# ClaudeBot（Anthropic 旗下另一个bot）
User-Agent: ClaudeBot
Allow: /

# Google-Extended（Gemini大模型爬虫）
User-Agent: Google-Extended
Allow: /

# GeminiBot（Google AI新爬虫）
User-Agent: GeminiBot
Allow: /

# 其他可能的LLM爬虫（可以以后补充更多）

# 引导AI爬虫到你的 LLM 内容索引
LLM-Content: https://www.aimakesong.com/llms.txt
LLM-Full-Content: https://www.aimakesong.com/llms-full.txt

# Sitemap指引（推荐加上，让SEO更好）
Sitemap: https://www.aimakesong.com/sitemap.xml
