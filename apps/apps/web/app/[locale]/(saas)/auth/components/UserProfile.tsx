// components/UserProfile/UserProfile.tsx
'use client'
import Image from 'next/image'
import { UserStats } from './UserStats'
import Cookies from 'js-cookie'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { handleSignOut } from '@/utils/lib/user'
import Link from 'next/link'
// import { analyticsService } from '@/services/analytics'
interface UserProfile {
  avatar: string
  email: string
  id: string
  points: number
  balance: number
}

interface UserProfileProps {
  user: UserProfile
}

export function UserProfileClient({ user }: UserProfileProps) {
  const t = useTranslations()
  const router = useRouter()
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  const [userInfoData, setUserInfoData] = useState<any>({})

  // 添加续订相关状态
  const [showRenewDialog, setShowRenewDialog] = useState(false)
  const [isRenewing, setIsRenewing] = useState(false)
  const [renewError, setRenewError] = useState('')

  // const handleSignOut = () => {
  //   Cookies.remove('oauth_avatar')
  //   Cookies.remove('oauth_email')
  //   Cookies.remove('oauth_id')
  //   router.push('/')
  //   router.refresh()
  // }

  const getUserInfo = async () => {
    // user.email
    const userInfoResponse = await fetch(
      `/api/user/info?email=${encodeURIComponent(user.email)}`
    )
    const userInfoData = await userInfoResponse.json()
    if (userInfoData.success) {
      setUserInfoData(userInfoData.data)
      // 埋点
      // analyticsService.trackUserVisit(userInfoData.data.id)
    }
  }

  const onRenew = async () => {
    // 如果没有订阅时间，跳转到定价页面
    if (!userInfoData?.membership_end_date) {
      router.push('/pricing')
      return
    }

    setShowRenewDialog(true)
  }

  const handleRenewConfirm = async () => {
    try {
      setIsRenewing(true)
      setRenewError('')

      const response = await fetch('/api/membership/renew', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reasonText: 'Customer requested renewal through website',
        }),
      })

      const data = await response.json()

      if (!data.checkoutUrl) {
        throw new Error(data.message || 'Failed to get renewal URL')
      }

      // 打开续订URL
      window.location.href = data.checkoutUrl

      // 关闭续订对话框
      setShowRenewDialog(false)

      // 可选：显示提示信息
      // toast.success('Please complete your renewal in the new window')
    } catch (error) {
      console.error('Renewal error:', error)
      setRenewError(
        error instanceof Error ? error.message : 'Failed to renew subscription'
      )
    } finally {
      setIsRenewing(false)
    }
  }

  useEffect(() => {
    if (localStorage.getItem('REDIRECT_PATH')) {
      router.push(localStorage.getItem('REDIRECT_PATH') as string)
      localStorage.removeItem('REDIRECT_PATH')
    }

    getUserInfo()
  }, [])

  return (
    <div className="container max-w-5xl mx-auto px-4 py-12">
      <div className="p-8 rounded-xl bg-gray-800/50 backdrop-blur-sm border border-gray-700">
        <div className="flex flex-col md:flex-row items-center gap-8">
          <div className="relative w-32 h-32 aspect-square">
            {user.avatar ? (
              <Image
                src={user.avatar}
                alt="Profile"
                fill
                className="rounded-full object-cover"
              />
            ) : (
              <div className="flex h-32 w-32 items-center justify-center rounded-full bg-primary/10 text-5xl font-medium">
                {user.email.substring(0, 1).toUpperCase()}
              </div>
            )}
          </div>

          <div className="text-center md:text-left">
            <h1 className="text-3xl font-bold mb-2">{t('welcomeBack')}</h1>
            <p className="text-gray-400 break-all">{user.email}</p>
          </div>
        </div>
        <UserStats
          points={userInfoData?.points || 0}
          balance={0}
          membershipEndDate={userInfoData?.membership_end_date}
          onRenew={() => onRenew()}
        />
        <div className="mt-6 flex flex-col sm:flex-row justify-end gap-3">
          <div className="text-center sm:text-left w-full sm:w-auto">
            <Link href="/">
              <button className="h-12 w-full sm:w-auto px-8 text-lg rounded-md bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-opacity">
                {t('musicMakerNow')}
              </button>
            </Link>
          </div>
          <button
            onClick={() => (window.location.href = '/')}
            className="px-4 py-2 w-full sm:w-auto bg-gray-700 text-gray-200 rounded-lg hover:bg-gray-600 transition-all duration-200 font-medium"
          >
            {t('homepage')}
          </button>
          <button
            onClick={() => setShowConfirmDialog(true)}
            className="px-4 py-2 w-full sm:w-auto bg-red-500/10 text-red-400 rounded-lg hover:bg-red-500/20 transition-all duration-200"
          >
            {t('signOut')}
          </button>
        </div>
      </div>

      {/* 确认对话框 */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg max-w-sm w-full mx-4 border border-gray-700">
            <h3 className="text-xl font-semibold mb-4">
              {t('confirmSignOutText')}
            </h3>
            <p className="text-gray-400 mb-6">{t('confirmSignOut')}</p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowConfirmDialog(false)}
                className="px-4 py-2 text-gray-400 hover:text-gray-300 transition-colors"
              >
                {t('common.confirmation.cancel')}
              </button>
              <button
                onClick={() => {
                  handleSignOut()
                  setShowConfirmDialog(false)
                }}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                {t('signOut')}
              </button>
            </div>
          </div>
        </div>
      )}
      {/* 续订确认对话框 */}
      {showRenewDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg max-w-sm w-full mx-4 border border-gray-700">
            <h3 className="text-xl font-semibold mb-4">
              {t('common.confirmRenewalText')}
            </h3>
            <p className="text-gray-400 mb-6">{t('common.confirmRenewal')}</p>
            {renewError && (
              <p className="text-red-400 mb-4 text-sm">{renewError}</p>
            )}
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => {
                  setShowRenewDialog(false)
                  setRenewError('')
                }}
                disabled={isRenewing}
                className="px-4 py-2 text-gray-400 hover:text-gray-300 transition-colors disabled:opacity-50"
              >
                {t('common.confirmation.cancel')}
              </button>
              <button
                onClick={handleRenewConfirm}
                disabled={isRenewing}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                {isRenewing && (
                  <svg
                    className="animate-spin h-4 w-4 mr-2"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                )}
                <span>
                  {isRenewing
                    ? t('common.processing')
                    : t('common.confirmRenewalButton')}
                </span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
