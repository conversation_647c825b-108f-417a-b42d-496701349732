'use client'

import { useState } from 'react'

export default function WeChatPage() {
  const [users, setUsers] = useState<string[]>([])
  const [openid, setOpenid] = useState('')
  const [message, setMessage] = useState('')
  const [status, setStatus] = useState('')

  // 获取用户列表
  const handleGetUsers = async () => {
    try {
      setStatus('获取用户列表中...')

      const response = await fetch('/api/wechat/users')
      const data = await response.json()

      if (response.ok && data.data?.openid) {
        setUsers(data.data.openid)
        setStatus('获取用户列表成功')
      } else {
        setStatus('获取用户列表失败')
      }
    } catch (error) {
      setStatus('获取用户列表出错')
      console.error('Error:', error)
    }
  }

  // 发送消息
  const handleSendMessage = async () => {
    if (!openid || !message.trim()) {
      setStatus('请选择用户并输入消息')
      return
    }

    try {
      setStatus('发送消息中...')

      const response = await fetch('/api/wechat/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          openid,
          content: message.trim(),
        }),
      })

      const data = await response.json()

      if (response.ok && !data.errcode) {
        setStatus('发送成功')
        setMessage('') // 清空消息输入框
      } else {
        setStatus(`发送失败: ${data.errmsg || '未知错误'}`)
      }
    } catch (error) {
      setStatus('发送消息出错')
      console.error('Error:', error)
    }
  }

  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6">微信消息发送</h2>

      <button
        onClick={handleGetUsers}
        className="w-full bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 mb-4"
      >
        获取用户列表
      </button>

      {users.length > 0 && (
        <div className="mb-4">
          <label className="block text-gray-700 mb-2">选择用户:</label>
          <select
            value={openid}
            onChange={(e) => setOpenid(e.target.value)}
            className="w-full px-3 py-2 border rounded-lg"
          >
            <option value="">请选择用户</option>
            {users.map((id) => (
              <option key={id} value={id}>
                {id}
              </option>
            ))}
          </select>
        </div>
      )}

      <div className="mb-4">
        <label className="block text-gray-700 mb-2">消息内容:</label>
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          className="w-full px-3 py-2 border rounded-lg"
          rows={4}
          placeholder="请输入要发送的消息"
        />
      </div>

      <button
        onClick={handleSendMessage}
        disabled={!openid || !message.trim()}
        className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
      >
        发送消息
      </button>

      {status && (
        <p
          className={`mt-4 text-center text-sm ${
            status.includes('成功') ? 'text-green-600' : 'text-red-600'
          }`}
        >
          {status}
        </p>
      )}
    </div>
  )
}
