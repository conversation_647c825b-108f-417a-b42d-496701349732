'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { cn } from '@ui/lib'
import { ChevronDown, ChevronUp, Mic } from 'lucide-react'

interface LyricsDisplayProps {
  lyrics: string
}

export default function LyricsDisplay({ lyrics }: LyricsDisplayProps) {
  const t = useTranslations()
  const [isExpanded, setIsExpanded] = useState(false)

  // 格式化歌词
  const formatLyrics = () => {
    if (!lyrics) return []

    // 尝试检测是否包含歌词标记如[Verse], [Chorus]等
    const hasLyricMarkers =
      /\[(Verse|Chorus|Bridge|Intro|Outro|Pre-Chorus|Hook|Refrain)\s?\d*\]/i.test(
        lyrics
      )

    // 分割歌词为段落
    let paragraphs = lyrics.split(/\n\s*\n/)

    // 如果没有明确的段落分隔，按行分割后再组合
    if (paragraphs.length <= 1) {
      const lines = lyrics.split('\n')
      paragraphs = []
      let currentParagraph = ''

      for (const line of lines) {
        // 检测是否是段落标记
        if (hasLyricMarkers && /\[.*\]/.test(line)) {
          // 如果已有内容，保存当前段落
          if (currentParagraph) {
            paragraphs.push(currentParagraph.trim())
          }
          // 开始新段落
          currentParagraph = line + '\n'
        } else {
          currentParagraph += line + '\n'

          // 每4行或空行形成一个段落（如果没有标记）
          if (
            !hasLyricMarkers &&
            (line.trim() === '' || currentParagraph.split('\n').length > 4)
          ) {
            if (currentParagraph.trim()) {
              paragraphs.push(currentParagraph.trim())
            }
            currentParagraph = ''
          }
        }
      }

      // 添加最后一个段落
      if (currentParagraph.trim()) {
        paragraphs.push(currentParagraph.trim())
      }
    }

    return paragraphs
  }

  const paragraphs = formatLyrics()

  // 显示的段落数量
  const displayParagraphs = isExpanded ? paragraphs : paragraphs.slice(0, 4)

  // 是否需要"显示更多"按钮
  const needsExpansion = paragraphs.length > 4

  if (!lyrics) {
    return (
      <div className="backdrop-blur-lg backdrop-saturate-150 bg-black/5 rounded-3xl p-8 shadow-lg relative overflow-hidden flex flex-col items-center justify-center h-48">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-800/10 via-transparent to-indigo-800/10 mix-blend-overlay"></div>
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-pink-500/10 rounded-full blur-3xl mix-blend-normal"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl mix-blend-normal"></div>

        <div className="relative z-10 flex flex-col items-center">
          <Mic className="w-10 h-10 text-gray-500/70 mb-3" />
          <p className="text-gray-400/80">{t('noLyrics')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="backdrop-blur-lg backdrop-saturate-150 bg-black/5 rounded-3xl p-8 shadow-lg relative overflow-hidden">
      {/* 微妙的渐变光效 */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-800/10 via-transparent to-indigo-800/10 mix-blend-overlay"></div>
      <div className="absolute -top-40 -right-40 w-80 h-80 bg-pink-500/10 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>

      {/* 内容区域 */}
      <div className="relative z-10 h-full flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white/90 flex items-center">
            <Mic className="w-5 h-5 mr-2 text-purple-300/90" />
            {t('lyrics')}
          </h2>
        </div>

        <div
          className={cn(
            'overflow-y-auto pr-2 max-h-[450px]',
            'scrollbar-thin scrollbar-thumb-purple-500/30 scrollbar-track-transparent'
          )}
        >
          <div className="space-y-6">
            {displayParagraphs.map((paragraph, index) => {
              // 检测是否包含[标记]
              const hasMarker = /\[(.*?)\]/.test(paragraph)

              if (hasMarker) {
                // 提取并处理带标记的段落
                const markerMatch = paragraph.match(/\[(.*?)\]/)
                const marker = markerMatch ? markerMatch[0] : ''
                const content = paragraph.replace(/\[(.*?)\]/, '').trim()

                return (
                  <div key={index} className="lyrics-paragraph">
                    <div className="text-purple-300/90 font-semibold mb-2 backdrop-blur-sm bg-purple-500/5 inline-block px-2 py-0.5 rounded">
                      {marker}
                    </div>
                    <div className="text-gray-200/90 whitespace-pre-line leading-relaxed">
                      {content}
                    </div>
                  </div>
                )
              }

              return (
                <div key={index} className="lyrics-paragraph">
                  <div className="text-gray-200/90 whitespace-pre-line leading-relaxed">
                    {paragraph}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {needsExpansion && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="mx-auto mt-6 px-5 py-2 bg-purple-800/30 hover:bg-purple-700/30 text-purple-200/90 rounded-full text-sm transition-colors flex items-center backdrop-blur-sm"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="w-4 h-4 mr-1" />
                {t('showLess')}
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4 mr-1" />
                {t('showMore')}
              </>
            )}
          </button>
        )}
      </div>
    </div>
  )
}
