'use client'

import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import VinylPlayer from './VinylPlayer'
import LyricsDisplay from './LyricsDisplay'
import ActionButtons from './ActionButtons'
import { useRouter } from '@i18n/routing'

// 音乐类型定义
interface MusicData {
  id: string
  inputType: string
  prompt: string
  title: string
  tags: string
  clipId: string
  progress: number
  continueClipId: string
  status: number
  cld2AudioUrl: string
  cld2VideoUrl: string
  progressMsg: string
  cld2ImageUrl: string
  user_id?: string
  created_at?: string
}

interface MusicSharePageProps {
  id: string
}

export default function MusicSharePage({ id }: MusicSharePageProps) {
  const t = useTranslations()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [musicData, setMusicData] = useState<MusicData | null>(null)
  const [showLyrics, setShowLyrics] = useState(true)

  async function fetchMusicData() {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/music/getById?id=${id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch music data')
      }

      const data = await response.json()
      console.log('👋 ~ fetchMusicData data:', data)
      setMusicData(data?.data || null)
    } catch (error) {
      console.error('Error fetching music data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理创建音乐按钮点击
  const handleCreateMusic = () => {
    router.push('/ai-music-generator')
  }

  // 客户端加载音乐数据
  useEffect(() => {
    if (id) {
      fetchMusicData()
    }
  }, [id])

  // 音乐未找到或加载中显示
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-purple-900/50 flex flex-col items-center justify-center p-4">
        <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 text-gray-300">{t('loading')}</p>
      </div>
    )
  }

  if (!musicData) {
    return (
      <div className="pt-24 min-h-screen bg-gradient-to-br from-gray-900 to-purple-900/50 flex flex-col items-center justify-center p-6 text-center">
        <h1 className="text-3xl font-bold text-white mb-4">
          {t('musicNotFound')}
        </h1>
        <p className="text-gray-300 mb-8">{t('musicNotFoundDesc')}</p>
        <button
          onClick={handleCreateMusic}
          className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-300"
        >
          {t('createMusic')}
        </button>
      </div>
    )
  }

  if (musicData.status === 40) {
    // 生成失败的音乐
    return (
      <div className="pt-24 min-h-screen bg-gradient-to-br from-gray-900 to-purple-900/50 flex flex-col items-center justify-center p-6 text-center">
        <div className="w-24 h-24 rounded-full bg-red-500/20 flex items-center justify-center mb-6">
          <svg
            className="w-12 h-12 text-red-500"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M9 18V5l12-2v13" />
            <path d="m9 9 12-2" />
            <circle cx="6" cy="18" r="3" />
            <circle cx="18" cy="16" r="3" />
            <path d="M3 3l18 18" />
          </svg>
        </div>
        <h1 className="text-3xl font-bold text-white mb-4">
          {t('generateFailed')}
        </h1>
        <p className="text-gray-300 mb-8">{t('lyricsViolation')}</p>
        <button
          onClick={handleCreateMusic}
          className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-300"
        >
          {t('createMusic')}
        </button>
      </div>
    )
  }

  return (
    <div className="pt-24 min-h-screen bg-gradient-to-br from-gray-900 via-purple-900/40 to-indigo-900/30 flex flex-col">
      {/* 背景装饰效果 */}
      <div className="fixed inset-0 z-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-60 -right-60 w-[600px] h-[600px] bg-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/4 left-1/3 w-[500px] h-[500px] bg-blue-600/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-[400px] h-[400px] bg-indigo-600/10 rounded-full blur-3xl"></div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 container mx-auto px-4 py-6 relative z-10">
        <div className="mb-10 max-w-5xl mx-auto">
          <ActionButtons music={musicData} onCreateMusic={handleCreateMusic} />
        </div>

        <div className="flex flex-col gap-10 max-w-5xl mx-auto">
          {/* 黑胶播放器 */}
          <div className="transition-all duration-300">
            <VinylPlayer
              audioUrl={musicData.cld2AudioUrl}
              coverUrl={musicData.cld2ImageUrl}
              title={musicData.title}
              onToggleLyrics={() => setShowLyrics(!showLyrics)}
              showLyrics={showLyrics}
              musicInfo={musicData}
            />
          </div>

          {/* 歌词区域 */}
          {showLyrics && (
            <div className="transition-opacity duration-500">
              <LyricsDisplay lyrics={musicData.prompt} />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
