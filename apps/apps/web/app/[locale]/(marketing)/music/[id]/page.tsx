import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import MusicSharePage from './components/MusicSharePage'

export async function generateMetadata({
  params,
}: {
  params: { id: string; locale: string }
}): Promise<Metadata> {
  const t = await getTranslations({
    locale: params.locale,
    namespace: 'share-metadata',
  })

  return {
    title: t('shareMusic.title'),
    description: t('shareMusic.description'),
    keywords: t('shareMusic.keywords'),
    openGraph: {
      title: t('shareMusic.title'),
      description: t('shareMusic.description'),
      images: ['/images/aimakesong-removebg-preview.png'],
      type: 'music.song',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('shareMusic.title'),
      description: t('shareMusic.description'),
      images: ['/images/aimakesong-removebg-preview.png'],
    },
  }
}

export default async function MusicPage({
  params,
}: {
  params: { id: string }
}) {
  return <MusicSharePage id={params.id} />
}
