'use client'

import { useEffect, useRef, useState } from 'react'
import {
  Pause,
  Play,
  Volume2,
  Music,
  Calendar,
  User,
  ListMusic,
  Rewind,
  FastForward,
} from 'lucide-react'
import { cn } from '@ui/lib'
import { useTranslations } from 'next-intl'

// 定义粒子动画样式
const ParticleStyles = () => {
  return (
    <style jsx global>{`
      @keyframes float-up-1 {
        0%,
        100% {
          transform: translateY(-50%) translateX(0);
          opacity: 0.7;
        }
        50% {
          transform: translateY(-80%) translateX(3px);
          opacity: 0.5;
        }
      }
      @keyframes float-up-2 {
        0%,
        100% {
          transform: translateY(-50%) translateX(0);
          opacity: 0.6;
        }
        50% {
          transform: translateY(-110%) translateX(-5px);
          opacity: 0.4;
        }
      }
      @keyframes float-up-3 {
        0%,
        100% {
          transform: translateY(-50%) translateX(0);
          opacity: 0.8;
        }
        50% {
          transform: translateY(-90%) translateX(7px);
          opacity: 0.6;
        }
      }
      @keyframes float-up-4 {
        0%,
        100% {
          transform: translateY(-50%) translateX(0);
          opacity: 0.55;
        }
        50% {
          transform: translateY(-70%) translateX(-4px);
          opacity: 0.35;
        }
      }
      @keyframes float-up-5 {
        0%,
        100% {
          transform: translateY(-50%) translateX(0);
          opacity: 0.65;
        }
        50% {
          transform: translateY(-60%) translateX(5px);
          opacity: 0.45;
        }
      }

      /* 定义光点移动动画 */
      @keyframes float-spot-1 {
        0% {
          transform: translate(0, 0);
        }
        25% {
          transform: translate(-3px, 3px);
        }
        50% {
          transform: translate(0, 5px);
        }
        75% {
          transform: translate(4px, 2px);
        }
        100% {
          transform: translate(0, 0);
        }
      }

      @keyframes float-spot-2 {
        0% {
          transform: translate(0, 0);
        }
        33% {
          transform: translate(5px, -2px);
        }
        66% {
          transform: translate(-4px, -3px);
        }
        100% {
          transform: translate(0, 0);
        }
      }

      @keyframes float-spot-3 {
        0% {
          transform: translate(0, 0);
        }
        20% {
          transform: translate(-2px, -4px);
        }
        40% {
          transform: translate(3px, -2px);
        }
        60% {
          transform: translate(4px, 3px);
        }
        80% {
          transform: translate(-3px, 2px);
        }
        100% {
          transform: translate(0, 0);
        }
      }

      @keyframes shimmer-spot {
        0%,
        100% {
          opacity: 0.3;
        }
        50% {
          opacity: 0.8;
        }
      }

      /* 定义光晕漂浮动画 */
      @keyframes float-glow {
        0% {
          transform: translate(0, 0) scale(1.1);
        }
        25% {
          transform: translate(-5px, 5px) scale(1.12);
        }
        50% {
          transform: translate(0, 8px) scale(1.13);
        }
        75% {
          transform: translate(7px, 3px) scale(1.12);
        }
        100% {
          transform: translate(0, 0) scale(1.1);
        }
      }

      .particle-0 {
        animation: float-up-1 5s infinite;
      }
      .particle-1 {
        animation: float-up-2 7s infinite;
      }
      .particle-2 {
        animation: float-up-3 6.5s infinite;
      }
      .particle-3 {
        animation: float-up-4 5.8s infinite;
      }
      .particle-4 {
        animation: float-up-5 6.2s infinite;
      }

      .float-spot-1 {
        animation: float-spot-1 10s ease-in-out infinite,
          shimmer-spot 5s ease-in-out infinite;
      }

      .float-spot-2 {
        animation: float-spot-2 14s ease-in-out infinite,
          shimmer-spot 7s ease-in-out infinite;
      }

      .float-spot-3 {
        animation: float-spot-3 18s ease-in-out infinite,
          shimmer-spot 6s ease-in-out infinite;
      }

      .float-glow {
        animation: float-glow 15s ease-in-out infinite;
      }

      .float-glow-reverse {
        animation: float-glow 20s ease-in-out infinite reverse;
      }

      /* 添加额外的动画 */
      @keyframes pulse-slow {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.6;
        }
      }

      .animate-pulse-slow {
        animation: pulse-slow 8s infinite;
      }

      .animation-delay-1000 {
        animation-delay: 1s;
      }

      .animation-delay-1500 {
        animation-delay: 1.5s;
      }

      .animation-delay-2000 {
        animation-delay: 2s;
      }

      .animation-delay-3000 {
        animation-delay: 3s;
      }

      .animate-spin-very-slow {
        animation: spin 30s linear infinite;
      }

      .animate-spin-slow {
        animation: spin 8s linear infinite;
      }

      .animation-reverse {
        animation-direction: reverse;
      }

      /* 定义径向渐变 */
      .bg-gradient-radial {
        background-image: radial-gradient(var(--tw-gradient-stops));
      }
    `}</style>
  )
}

// 增加音乐信息类型
interface MusicInfoType {
  id: string
  title: string
  tags?: string
  created_at?: string
  user_id?: string
  user_name?: string
  duration?: number // 添加时长属性
  view_count?: number // 添加观看次数属性
  // 其他可能的属性
}

interface VinylPlayerProps {
  audioUrl?: string
  coverUrl?: string
  title?: string
  onToggleLyrics: () => void
  showLyrics: boolean
  musicInfo?: MusicInfoType // 添加音乐信息属性
}

export default function VinylPlayer({
  audioUrl,
  coverUrl,
  title = 'Unknown Title',
  onToggleLyrics,
  showLyrics,
  musicInfo,
}: VinylPlayerProps) {
  const t = useTranslations()
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(0.7)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [isRotating, setIsRotating] = useState(false)

  // 当 audioUrl 改变时重置播放状态
  useEffect(() => {
    setIsPlaying(false)
    setCurrentTime(0)
    setIsRotating(false)
    if (audioRef.current) {
      audioRef.current.currentTime = 0
    }
  }, [audioUrl])

  // 格式化日期
  const formatDate = (dateString?: string) => {
    if (!dateString) return t('unknown')

    try {
      const date = new Date(dateString)
      return new Intl.DateTimeFormat(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      }).format(date)
    } catch (e) {
      return t('unknown')
    }
  }

  // 移除邮箱后缀
  const removeEmailSuffix = (email?: string) => {
    if (!email) return ''
    return email.split('@')[0]
  }

  // 隐藏用户名部分字符
  const maskUsername = (username?: string) => {
    if (!username) return ''

    // 首先移除邮箱后缀
    const name = removeEmailSuffix(username)

    // 如果用户名长度小于等于4，则直接返回
    if (name.length <= 4) return name

    // 保留前两个和后两个字符，中间用星号替换
    const prefix = name.substring(0, 2)
    const suffix = name.substring(name.length - 2)
    const hiddenPart = '*'.repeat(Math.min(name.length - 4, 3))

    return `${prefix}${hiddenPart}${suffix}`
  }

  // 格式化标签
  const formatTags = (tags?: string) => {
    if (!tags) return [t('noTags')]
    return tags
      .split(',')
      .map((tag) => tag.trim())
      .filter(Boolean)
  }

  const tagsList = musicInfo?.tags ? formatTags(musicInfo.tags) : []

  const togglePlay = () => {
    if (!audioRef.current || !audioUrl) return

    if (isPlaying) {
      audioRef.current.pause()
      setIsRotating(false)
    } else {
      audioRef.current.play()
      setIsRotating(true)
    }
    setIsPlaying(!isPlaying)
  }

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration)
    }
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = Number(e.target.value)
    setCurrentTime(time)
    if (audioRef.current) {
      audioRef.current.currentTime = time
    }
  }

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value)
    setVolume(value)
    if (audioRef.current) {
      audioRef.current.volume = value
    }
  }

  const formatTime = (time: number) => {
    if (!time || isNaN(time) || !isFinite(time) || time < 0) return '0:00'
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // 音频播放结束时的处理
  const handleEnded = () => {
    setIsPlaying(false)
    setCurrentTime(0)
    setIsRotating(false)
    if (audioRef.current) {
      audioRef.current.currentTime = 0
    }
  }

  // 跳转10秒
  const skipBackward = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = Math.max(
        0,
        audioRef.current.currentTime - 10
      )
    }
  }

  const skipForward = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = Math.min(
        duration,
        audioRef.current.currentTime + 10
      )
    }
  }

  return (
    <div className="backdrop-blur-md backdrop-saturate-150 bg-transparent relative overflow-hidden p-4 sm:p-6 md:p-8 rounded-3xl border border-white/5 shadow-lg">
      {/* 全局样式 */}
      <ParticleStyles />

      {/* 微妙的光影效果，更加融入背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/30 via-transparent to-indigo-900/20 rounded-3xl opacity-60"></div>
      <div className="absolute -top-40 -right-40 w-80 h-80 bg-pink-500/10 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-40 bg-blue-500/5 rounded-full blur-3xl"></div>

      {/* 内容区域 */}
      <div className="relative z-10">
        {/* 主要内容区域 - 改为上下布局 */}
        <div className="flex flex-col gap-5 sm:gap-8 md:gap-10 mt-2 sm:mt-4 md:mt-6">
          {/* 上方区域 - 左侧歌曲信息，右侧黑胶封面 */}
          <div className="flex flex-col lg:flex-row gap-5 sm:gap-8 md:gap-10">
            {/* 左侧 - 音乐信息区域 */}
            <div className="flex-1 flex flex-col">
              {/* 音乐标题和信息区域 */}
              <div className="mb-4 sm:mb-6 md:mb-8">
                <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-purple-300 mb-3 sm:mb-5 text-pretty">
                  {musicInfo?.title || title}
                </h1>

                {musicInfo && (
                  <div className="flex flex-col gap-3 text-sm md:text-base text-gray-200 mb-3 sm:mb-4">
                    {/* 作者信息 - 降低突出程度 */}
                    <div className="flex items-center gap-1.5 text-gray-300 opacity-80">
                      <User size={14} className="text-gray-400" />
                      <span>
                        {musicInfo.user_name
                          ? `${t('createdBy')} ${maskUsername(
                              musicInfo.user_name
                            )}`
                          : t('createdBy')}
                      </span>
                    </div>

                    {/* 创建日期 - 添加特殊效果 */}
                    <div className="flex items-center gap-1.5 bg-purple-500/10 backdrop-blur-sm px-2.5 py-1.5 rounded-full w-fit">
                      <Calendar size={15} className="text-purple-300" />
                      <span className="text-purple-100">
                        {formatDate(musicInfo.created_at)}
                      </span>
                    </div>

                    {/* 标签信息 - 增强标签样式 */}
                    {tagsList.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-1">
                        {tagsList.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="px-3 py-1.5 bg-purple-500/10 rounded-full text-sm backdrop-blur-sm hover:bg-purple-500/15 transition-colors text-purple-100"
                          >
                            {tag}
                          </span>
                        ))}
                        {tagsList.length > 3 && (
                          <span className="text-sm bg-purple-500/10 hover:bg-purple-500/15 px-3 py-1.5 rounded-full transition-colors text-purple-100">
                            +{tagsList.length - 3}
                          </span>
                        )}
                      </div>
                    )}

                    {/* 歌曲时长和观看次数 - 放在同一行展示 */}
                    <div className="flex items-center gap-3 mt-1">
                      {(musicInfo.duration || duration > 0) && (
                        <div className="flex items-center gap-1.5 bg-purple-500/10 backdrop-blur-sm px-2.5 py-1.5 rounded-full">
                          <Music size={15} className="text-purple-300" />
                          <span className="text-purple-100 font-medium">
                            {formatTime(musicInfo.duration || duration)}
                          </span>
                        </div>
                      )}

                      {musicInfo.view_count && (
                        <div className="flex items-center gap-1.5 bg-purple-500/10 backdrop-blur-sm px-2.5 py-1.5 rounded-full">
                          <svg
                            width="15"
                            height="15"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-purple-300"
                          >
                            <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
                            <circle cx="12" cy="12" r="3" />
                          </svg>
                          <span className="text-purple-100 font-medium">
                            {musicInfo.view_count.toLocaleString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 右侧 - 黑胶唱片区域 */}
            <div className="flex-1 flex items-center justify-center py-2 sm:py-4">
              <div
                className={cn(
                  'relative w-36 h-36 sm:w-44 sm:h-44 md:w-56 md:h-56 lg:w-64 lg:h-64 transition-all duration-700',
                  isRotating && 'scale-105'
                )}
              >
                {/* 增强唱片光晕效果 - 添加位置变化动画 */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-purple-500/15 via-transparent to-pink-500/25 blur-xl transform -translate-x-2 -translate-y-2 scale-110 float-glow"></div>
                <div className="absolute inset-0 rounded-full bg-gradient-to-bl from-indigo-500/10 via-transparent to-blue-500/15 blur-xl transform translate-x-2 translate-y-2 scale-110 float-glow-reverse animation-delay-2000"></div>

                {/* 增加更多光晕层 */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-pink-500/10 to-purple-500/15 blur-xl scale-125 float-glow animation-delay-1000"></div>

                {/* 加强内部发光效果 - 仅在播放时显示 */}
                {isRotating && (
                  <>
                    <div className="absolute inset-0 rounded-full bg-gradient-radial from-purple-400/10 to-transparent blur-xl scale-105 float-glow"></div>
                    <div className="absolute inset-0 rounded-full bg-gradient-radial from-pink-300/15 to-transparent blur-lg scale-110 float-glow-reverse animation-delay-1500"></div>
                    <div className="absolute inset-[-10px] rounded-full border border-purple-400/20 animate-spin-slow animation-delay-1500"></div>
                  </>
                )}

                {/* 唱片底座 - 阴影效果 */}
                <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-3/4 h-3 sm:h-4 bg-black/40 blur-md rounded-full"></div>

                {/* 黑胶唱片 */}
                <div
                  className="absolute inset-0 rounded-full bg-gray-950 shadow-lg 
                             before:absolute before:inset-2 before:rounded-full before:bg-gray-900
                             before:content-[''] before:shadow-inner"
                >
                  {/* 装饰性光环 - 添加旋转效果 */}
                  <div className="absolute inset-[-5px] rounded-full border border-purple-500/30 animate-spin-very-slow"></div>
                  <div className="absolute inset-[-10px] rounded-full border border-indigo-500/20 animate-spin-very-slow animation-reverse animation-delay-1000"></div>
                  <div className="absolute inset-[-15px] rounded-full border border-pink-500/10 animate-spin-very-slow animation-delay-2000"></div>

                  {/* 加入光点点缀 - 添加位置变化动画 */}
                  <div className="absolute top-[-2px] left-1/2 w-1.5 h-1.5 rounded-full bg-purple-400/50 blur-[1px] float-spot-1"></div>
                  <div className="absolute bottom-[5px] right-[20%] w-1 h-1 rounded-full bg-pink-300/40 blur-[1px] float-spot-2 animation-delay-1000"></div>
                  <div className="absolute top-[10%] right-[-1px] w-2 h-2 rounded-full bg-indigo-400/30 blur-[1.5px] float-spot-3 animation-delay-2000"></div>

                  {/* 增加更多光点，分布在唱片周围 */}
                  <div className="absolute top-[30%] left-[-1px] w-1 h-1 rounded-full bg-blue-300/40 blur-[1px] float-spot-2 animation-delay-3000"></div>
                  <div className="absolute bottom-[20%] left-[10%] w-1.5 h-1.5 rounded-full bg-purple-300/30 blur-[1.5px] float-spot-3 animation-delay-1500"></div>
                  <div className="absolute top-[80%] right-[15%] w-1 h-1 rounded-full bg-pink-400/35 blur-[1px] float-spot-1 animation-delay-2000"></div>

                  {/* 黑胶纹路 - 根据屏幕尺寸调整 */}
                  <div className="absolute inset-3 sm:inset-4 rounded-full border border-gray-800"></div>
                  <div className="absolute inset-6 sm:inset-8 rounded-full border border-gray-800"></div>
                  <div className="absolute inset-9 sm:inset-12 rounded-full border border-gray-800"></div>
                  <div className="absolute inset-12 sm:inset-16 rounded-full border border-gray-800"></div>

                  {/* 唱片中心标签 */}
                  <div className="absolute inset-0 m-auto w-10 h-10 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center shadow-inner">
                    <div className="w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-gray-700"></div>
                  </div>

                  {/* 封面图片 */}
                  {coverUrl && (
                    <div
                      className={cn(
                        'absolute inset-0 m-auto w-24 h-24 sm:w-28 sm:h-28 md:w-36 md:h-36 lg:w-40 lg:h-40 rounded-full overflow-hidden',
                        'border-2 sm:border-3 md:border-4 border-gray-900 shadow-lg cursor-pointer group',
                        isRotating && 'animate-spin-slow'
                      )}
                      onClick={togglePlay}
                      title={isPlaying ? t('hideLyrics') : t('showLyrics')}
                    >
                      {/* 渐变叠加层 */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-purple-900/30 via-transparent to-indigo-600/20 z-10 mix-blend-overlay"></div>
                      <div className="absolute inset-0 bg-gradient-to-bl from-pink-900/20 via-transparent to-indigo-900/30 z-10 mix-blend-color"></div>

                      {/* 增强发光效果 */}
                      <div
                        className={cn(
                          'absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 z-10 opacity-0 transition-opacity duration-700',
                          isRotating && 'opacity-70'
                        )}
                      ></div>

                      {/* 边缘光晕效果 */}
                      <div
                        className={cn(
                          'absolute inset-[-4px] rounded-full bg-gradient-radial from-transparent via-transparent to-purple-500/25 z-5 opacity-0 blur-sm transition-opacity duration-500',
                          isRotating && 'opacity-70'
                        )}
                      ></div>

                      <img
                        src={coverUrl}
                        alt={title}
                        className="w-full h-full object-cover"
                      />

                      {/* 播放/暂停指示层 */}
                      <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
                        {isPlaying ? (
                          <Pause className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-white" />
                        ) : (
                          <Play className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-white ml-1 sm:ml-1.5 md:ml-2" />
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* 唱臂 */}
                <div
                  className={cn(
                    'absolute -top-3 -right-3 sm:-top-4 sm:-right-4 w-20 sm:w-24 md:w-28 h-3 sm:h-4',
                    'origin-right transition-transform duration-1000',
                    isPlaying ? 'rotate-12' : '-rotate-12'
                  )}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-gray-700 to-gray-800 rounded-full"></div>
                  <div className="absolute left-0 top-1/2 -translate-y-1/2 w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-gray-300"></div>
                </div>
              </div>
            </div>
          </div>

          {/* 底部控制区域 - 更加紧凑的设计，为小屏幕进一步优化 */}
          <div className="mt-2 sm:mt-4 bg-gray-900/20 backdrop-blur-sm rounded-xl p-2 sm:p-3 relative">
            {/* 控制按钮 - 水平居中展示，不再使用自己的背景 */}
            <div className="flex items-center justify-center mb-0 sm:mb-3">
              <div className="flex items-center gap-4 sm:gap-5 py-1.5">
                {/* 后退10秒按钮 - 使用Rewind图标 */}
                <button
                  className="text-gray-300 hover:text-white transition-colors"
                  onClick={skipBackward}
                  title="后退 10 秒"
                >
                  <Rewind className="w-4 h-4 sm:w-5 sm:h-5" />
                </button>

                {/* 播放/暂停按钮 */}
                <button
                  onClick={togglePlay}
                  disabled={!audioUrl}
                  className={cn(
                    'w-10 h-10 sm:w-11 sm:h-11 md:w-12 md:h-12 rounded-full flex items-center justify-center transition-all backdrop-blur-sm',
                    audioUrl
                      ? 'bg-purple-600/80 hover:bg-purple-700/80'
                      : 'bg-gray-700/40 cursor-not-allowed'
                  )}
                >
                  {isPlaying ? (
                    <Pause className="w-5 h-5 sm:w-5.5 sm:h-5.5 md:w-6 md:h-6" />
                  ) : (
                    <Play className="w-5 h-5 sm:w-5.5 sm:h-5.5 md:w-6 md:h-6 ml-0.5" />
                  )}
                </button>

                {/* 前进10秒按钮 - 使用FastForward图标 */}
                <button
                  className="text-gray-300 hover:text-white transition-colors"
                  onClick={skipForward}
                  title="前进 10 秒"
                >
                  <FastForward className="w-4 h-4 sm:w-5 sm:h-5" />
                </button>
              </div>
            </div>

            {/* 小屏幕下的歌词控制按钮组 - 绝对定位在右下角 */}
            <div className="sm:hidden absolute bottom-2 right-2 flex items-center gap-2 bg-gray-500/30 backdrop-blur-sm rounded-full px-2 py-1">
              {/* 歌词控制按钮 */}
              <button
                onClick={onToggleLyrics}
                className="text-gray-300 hover:text-white transition-colors"
                title={showLyrics ? t('hideLyrics') : t('showLyrics')}
              >
                <ListMusic
                  className={`w-4 h-4 ${
                    showLyrics ? 'text-purple-300' : 'text-gray-300'
                  }`}
                />
              </button>
            </div>

            {/* 进度条和控制区域 - 小屏下隐藏，只在sm及以上屏幕显示 */}
            <div className="hidden sm:flex flex-row items-center gap-3 mt-3">
              {/* 进度条和时间显示部分 */}
              <div className="flex items-center gap-2 flex-1">
                {/* 进度时间 - 左侧 */}
                <span className="text-xs text-gray-300 w-8">
                  {formatTime(currentTime)}
                </span>

                {/* 进度条 */}
                <div className="flex-1 relative">
                  <input
                    type="range"
                    min={0}
                    max={duration || 100}
                    value={currentTime}
                    onChange={handleSeek}
                    className="w-full h-1.5 bg-gray-500/30 rounded-lg appearance-none cursor-pointer"
                    style={{
                      backgroundImage: `linear-gradient(to right, rgb(168 85 247 / 0.8) ${
                        (currentTime / (duration || 1)) * 100
                      }%, rgb(31 41 55 / 0.3) ${
                        (currentTime / (duration || 1)) * 100
                      }%)`,
                    }}
                  />

                  {/* 粒子动画效果 - 只在播放时显示 */}
                  {isPlaying && (
                    <div
                      className="absolute top-0 left-0 h-full pointer-events-none overflow-hidden"
                      style={{
                        width: `${(currentTime / (duration || 1)) * 100}%`,
                      }}
                    >
                      {/* 减少为10个粒子 */}
                      {[...Array(10)].map((_, i) => (
                        <div
                          key={i}
                          className={`absolute top-1/2 rounded-full bg-purple-300 
                                      opacity-60 particle-${i % 5}`}
                          style={{
                            left: `${Math.random() * 100}%`,
                            width: `${1.5 + Math.random() * 1.5}px`,
                            height: `${1.5 + Math.random() * 1.5}px`,
                            animationDelay: `${i * 0.5}s`,
                            boxShadow: '0 0 3px 1px rgba(168, 85, 247, 0.4)',
                          }}
                        />
                      ))}
                    </div>
                  )}
                </div>

                {/* 总时长 - 右侧 */}
                <span className="text-xs text-gray-300 w-8 text-right">
                  {duration ? formatTime(duration) : '0:00'}
                </span>
              </div>

              {/* 音量控制和歌词控制 */}
              <div className="flex items-center gap-3 bg-gray-500/30 backdrop-blur-sm rounded-full px-3 py-1">
                {/* 音量控制 */}
                <div className="flex items-center gap-2">
                  <Volume2 className="w-3.5 h-3.5 text-gray-300" />
                  <input
                    type="range"
                    min={0}
                    max={1}
                    step={0.01}
                    value={volume}
                    onChange={handleVolumeChange}
                    className="w-16 h-1 bg-gray-800/30 rounded-lg appearance-none cursor-pointer"
                    style={{
                      backgroundImage: `linear-gradient(to right, rgb(168 85 247 / 0.7) ${
                        volume * 100
                      }%, rgb(31 41 55 / 0.3) ${volume * 100}%)`,
                    }}
                  />
                </div>

                {/* 分隔线 */}
                <div className="h-4 w-px bg-gray-700/50"></div>

                {/* 歌词控制按钮 */}
                <button
                  onClick={onToggleLyrics}
                  className="flex items-center justify-center transition-colors"
                  title={showLyrics ? t('hideLyrics') : t('showLyrics')}
                >
                  <ListMusic
                    className={`w-3.5 h-3.5 ${
                      showLyrics
                        ? 'text-purple-300'
                        : 'text-gray-400 hover:text-gray-200'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 音频元素 */}
      {audioUrl && (
        <audio
          ref={audioRef}
          src={audioUrl}
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
          onEnded={handleEnded}
          className="hidden"
        />
      )}
    </div>
  )
}
