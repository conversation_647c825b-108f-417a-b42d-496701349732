'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { Download, Share2, Sparkles } from 'lucide-react'

interface ActionButtonsProps {
  music: {
    id: string
    title: string
    cld2AudioUrl: string
  }
  onCreateMusic: () => void
}

export default function ActionButtons({
  music,
  onCreateMusic,
}: ActionButtonsProps) {
  const t = useTranslations()
  const [isDownloading, setIsDownloading] = useState(false)
  const [isCopied, setIsCopied] = useState(false)

  // 处理下载
  const handleDownload = async () => {
    // 确认有URL可下载
    if (!music.cld2AudioUrl) return

    setIsDownloading(true)

    try {
      const response = await fetch('/api/music/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: music.cld2AudioUrl }),
      })

      if (!response.ok) {
        throw new Error('Download failed')
      }

      const now = new Date()
      const timestamp = `${now.getFullYear()}${String(
        now.getMonth() + 1
      ).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(
        now.getHours()
      ).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`

      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = `${music.title}_${timestamp}.mp3`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Download error:', error)
    } finally {
      setIsDownloading(false)
    }
  }

  // 处理分享
  const handleShare = async () => {
    const shareUrl = `${window.location.origin}/music/${music.id}`

    // 使用Web Share API（如果支持）
    if (navigator.share) {
      try {
        await navigator.share({
          title: music.title,
          text: t('checkOutThisMusic'),
          url: shareUrl,
        })
      } catch (err) {
        console.error('Share error:', err)
        // 回退到复制链接
        copyToClipboard(shareUrl)
      }
    } else {
      // 不支持Share API，复制链接
      copyToClipboard(shareUrl)
    }
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setIsCopied(true)
        setTimeout(() => setIsCopied(false), 2000)
      })
      .catch((err) => {
        console.error('Copy failed:', err)
      })
  }

  return (
    <div className="flex flex-col-reverse sm:flex-row justify-between items-center max-w-5xl mx-auto w-full gap-4">
      {/* 左侧 - 下载和分享按钮 */}
      <div className="flex gap-3 w-full sm:w-auto">
        {/* 分享按钮 - 突出显示 */}
        <button
          onClick={handleShare}
          className="relative flex items-center justify-center gap-2 px-3 sm:px-5 py-3 bg-gradient-to-r from-blue-600 via-sky-600 to-cyan-600 text-white rounded-lg shadow-md transition-all duration-300 hover:shadow-blue-500/30 hover:shadow-lg group h-12 flex-1 sm:flex-none"
        >
          <span className="absolute -inset-1 rounded-lg bg-gradient-to-r from-blue-600/20 to-cyan-600/20 blur-xl opacity-60 group-hover:opacity-100 transition-opacity"></span>
          <Share2 size={18} className="text-blue-200" />
          <span className="relative z-10 font-medium text-sm sm:text-base">
            {isCopied ? t('linkCopied') : t('share.share')}
          </span>
          <span className="absolute inset-0 rounded-lg border border-white/20"></span>
        </button>
      </div>

      {/* 右侧 - 创建音乐按钮 */}
      <button
        onClick={onCreateMusic}
        className="relative flex items-center justify-center gap-3 px-8 py-3 bg-gradient-to-r from-purple-600 via-fuchsia-600 to-pink-600 text-white rounded-xl shadow-lg transition-all duration-300 hover:shadow-purple-500/30 hover:scale-105 hover:shadow-xl group h-12 w-full sm:w-auto"
      >
        <span className="absolute -inset-1 rounded-xl bg-gradient-to-r from-purple-600/20 to-pink-600/20 blur-xl opacity-60 group-hover:opacity-100 transition-opacity"></span>
        <Sparkles className="w-5 h-5 text-purple-200" />
        <span className="font-semibold relative z-10">{t('createMusic')}</span>
        <span className="absolute inset-0 rounded-xl border border-white/20"></span>
      </button>
    </div>
  )
}
