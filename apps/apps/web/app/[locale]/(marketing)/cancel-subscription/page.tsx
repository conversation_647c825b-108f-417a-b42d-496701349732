'use client'

import { useState } from 'react'
import { Button } from '@ui/components/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@ui/components/card'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@ui/components/alert-dialog'
import { useToast } from '@ui/hooks/use-toast'

export default function CancelSubscriptionPage() {
  const { toast } = useToast()
  const [isCancelling, setIsCancelling] = useState(false)
  const [isCancelled, setIsCancelled] = useState(false)

  const mockSubscriptionData = {
    id: 'sub_1234567890',
    planName: 'Love Song Pro Premium',
    nextBillingDate: '2024-08-24',
    amount: '$19.99/month',
    status: 'active',
  }

  const handleCancelSubscription = async () => {
    setIsCancelling(true)

    setTimeout(() => {
      setIsCancelling(false)
      setIsCancelled(true)

      toast({
        title: 'Subscription Cancelled Successfully',
        description:
          'Your subscription has been cancelled. You can still use the service until the end of the current billing period.',
        variant: 'default',
      })
    }, 2000)
  }

  if (isCancelled) {
    return (
      <div className="min-h-screen  flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12  rounded-full flex items-center justify-center mb-4">
              <svg
                className="w-6 h-6 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <CardTitle className="text-green-600">
              Subscription Cancelled Successfully
            </CardTitle>
            <CardDescription>
              Your subscription has been cancelled successfully. You can
              continue using the service until{' '}
              {mockSubscriptionData.nextBillingDate}.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button
              className="w-full"
              onClick={() => (window.location.href = '/')}
            >
              Back to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className=" flex items-center justify-center pt-32 ">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Cancel Subscription</CardTitle>
          <CardDescription>
            We're sorry to see you go. Please confirm that you want to cancel
            your subscription.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className=" p-4 rounded-lg">
            <h3 className="font-semibold text-sm mb-2">Current Subscription</h3>
            <div className="space-y-1 text-sm text-gray-600">
              <p>
                <span className="font-medium">Plan: </span>
                {mockSubscriptionData.planName}
              </p>
              <p>
                <span className="font-medium">Price: </span>
                {mockSubscriptionData.amount}
              </p>
              <p>
                <span className="font-medium">Next Billing: </span>
                {mockSubscriptionData.nextBillingDate}
              </p>
              <p>
                <span className="font-medium">Status: </span>
                <span className="text-green-600 font-medium">
                  {mockSubscriptionData.status === 'active'
                    ? 'Active'
                    : 'Inactive'}
                </span>
              </p>
            </div>
          </div>

          <div className=" p-4 rounded-lg">
            <h3 className="font-semibold text-sm mb-2 text-yellow-800">
              Important Information
            </h3>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>
                • After cancellation, you can still use the service until the
                end of the current billing period
              </li>
              <li>
                • No additional charges will be incurred after cancellation
              </li>
              <li>• You can resubscribe at any time</li>
            </ul>
          </div>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="destructive"
                className="w-full"
                disabled={isCancelling}
              >
                {isCancelling ? 'Cancelling...' : 'Confirm Cancellation'}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>
                  Confirm Subscription Cancellation?
                </AlertDialogTitle>
                <AlertDialogDescription>
                  This action will cancel your {mockSubscriptionData.planName}{' '}
                  subscription. You will lose access after{' '}
                  {mockSubscriptionData.nextBillingDate}.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleCancelSubscription}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Confirm Cancellation
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <Button
            variant="outline"
            className="w-full"
            onClick={() => (window.location.href = '/')}
          >
            Keep Subscription & Go Back
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
