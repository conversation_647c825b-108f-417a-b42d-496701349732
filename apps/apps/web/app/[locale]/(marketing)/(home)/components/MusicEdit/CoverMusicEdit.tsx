'use client'
import { useEffect, useRef, useState } from 'react'
import { Maximize2, Minimize2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useLocale, useTranslations } from 'next-intl'
import { cn } from '@ui/lib'
import { enExamples, zhExamples } from './config'

interface IndexProps {
  batchId?: string

  onValuesChange?: (values: {
    generationMode: 'default' | 'custom'
    isInstrumentalOnly: boolean
    selectedStyles: string[]
    title: string
    prompt: string
  }) => void
}

const StyleTag = ({
  text,
  onRemove,
}: {
  text: string
  onRemove: () => void
}) => (
  <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-purple-500/20 text-purple-300 rounded-md text-xs">
    {text}
    <button onClick={onRemove} className="hover:text-purple-200">
      ×
    </button>
  </span>
)

const CoverMusicEdit = ({ batchId, onValuesChange }: IndexProps) => {
  const t = useTranslations()
  const router = useRouter()
  const [generationMode, setGenerationMode] = useState<'default' | 'custom'>(
    'default'
  )
  const [selectedStyles, setSelectedStyles] = useState<string[]>([])
  const [title, setTitle] = useState('')
  const [prompt, setPrompt] = useState('')
  const [errorMessage, setErrorMessage] = useState('')
  const [isInstrumentalOnly, setIsInstrumentalOnly] = useState(false)
  const [activeCategory, setActiveCategory] = useState<string>('Genre')
  const [isExpanded, setIsExpanded] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [textareaSize, setTextareaSize] = useState('normal')
  const [isEditing, setIsEditing] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const inputRef = useRef<any>(null)
  const inputRefTitle = useRef(null)
  const [isMobile, setIsMobile] = useState(false)

  const locale = useLocale()

  // 当值变化时通知父组件
  useEffect(() => {
    // 防止初始化时不必要的更新
    const timer = setTimeout(() => {
      if (onValuesChange) {
        onValuesChange({
          generationMode,
          isInstrumentalOnly,
          selectedStyles,
          title,
          prompt,
        })
      }
    }, 0)

    return () => clearTimeout(timer)
  }, [
    generationMode,
    isInstrumentalOnly,
    selectedStyles,
    title,
    prompt,
    onValuesChange,
  ])

  useEffect(() => {
    // 在客户端运行时检测设备
    const detectMobileDevice = () => {
      const userAgent =
        navigator.userAgent || navigator.vendor || (window as any).opera
      return {
        isMobile:
          /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
            userAgent.toLowerCase()
          ),
      }
    }
    setIsMobile(detectMobileDevice().isMobile)
  }, [])

  // 切换模式时的处理
  const handleModeChange = (mode: 'default' | 'custom') => {
    setPrompt('')
    setGenerationMode(mode)
  }

  // 获取高度类名
  const getHeightClassName = () => {
    return textareaSize === 'large' ? 'h-96 sm:h-80' : 'h-48 sm:h-40'
  }

  // 切换文本区域大小
  const toggleTextareaSize = () => {
    setTextareaSize(textareaSize === 'normal' ? 'large' : 'normal')
  }

  const toggleStyle = (style: string) => {
    if (selectedStyles.includes(style)) {
      setSelectedStyles(selectedStyles.filter((s) => s !== style))
    } else {
      setSelectedStyles([...selectedStyles, style])
    }
  }

  const handleInputKeyDown = (e: any) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      if (!selectedStyles.includes(inputValue.trim())) {
        setSelectedStyles([...selectedStyles, inputValue.trim()])
      }
      setInputValue('')
      setIsEditing(false)
    } else if (e.key === 'Escape') {
      setInputValue('')
      setIsEditing(false)
    }
  }

  const removeStyle = (styleToRemove: string) => {
    setSelectedStyles(selectedStyles.filter((style) => style !== styleToRemove))
  }

  const generateRandomStyles = () => {
    const allStyles = musicCategories.flatMap((category) => category.styles)
    const shuffled = [...allStyles].sort(() => 0.5 - Math.random())
    const randomCount = Math.floor(Math.random() * (5 - 2 + 1)) + 2
    setSelectedStyles(shuffled.slice(0, randomCount))
  }

  const maxLength = 3000
  const minLength = 3
  const currentLength = prompt.length

  const styles = locale === 'zh' ? zhExamples : enExamples

  interface MusicCategory {
    label: string
    type: 'Style' | 'Moods' | 'Voices' | 'Tempos'
    styles: string[]
  }

  const musicCategories: MusicCategory[] =
    locale === 'zh'
      ? [
          {
            label: '风格（70+）',
            type: 'Style',
            styles: zhExamples.map((item) => item.name),
          },
          {
            label: '情绪',
            type: 'Moods',
            styles: [
              '欢快',
              '悲伤',
              '愤怒',
              '恐惧',
              '惊喜',
              '期待',
              '平静',
              '浪漫',
              '怀旧',
              '神秘',
              '胜利',
              '绝望',
            ],
          },
          {
            label: '声音',
            type: 'Voices',
            styles: [
              '女高音',
              '女中音',
              '男高音',
              '男低音',
              '童声',
              '男声',
              '女声',
            ],
          },
          {
            label: '速度',
            type: 'Tempos',
            styles: ['快板', '中板', '慢板', '80-120 BPM', '120-160 BPM'],
          },
        ]
      : [
          {
            label: `${t('style')}（70+）`,
            type: 'Style',
            styles: enExamples.map((item) => item.name),
          },
          {
            label: t('moods'),
            type: 'Moods',
            styles: [
              'Joy',
              'Sadness',
              'Anger',
              'Fear',
              'Surprise',
              'Anticipation',
              'Calmness',
              'Romantic',
              'Nostalgia',
              'Mystery',
              'Triumph',
              'Despair',
            ],
          },
          {
            label: t('voices'),
            type: 'Voices',
            styles: [
              t('soprano'),
              t('alto'),
              t('tenor'),
              t('bass'),
              t('childrensVoice'),
              t('maleVoice'),
              t('femaleVoice'),
            ],
          },
          {
            label: t('tempos'),
            type: 'Tempos',
            styles: ['Fast', 'Medium', 'Slow', '80-120 BPM', '120-160 BPM'],
          },
        ]

  return (
    <div className="container mx-auto px-0 box-border  mt-10">
      <div className="max-w-4xl mx-auto bg-gray-800/80 rounded-2xl p-4 md:p-6">
        {/* Mode Selection */}
        <div className="flex gap-4 mb-3 sm:mb-6">
          <button
            type="button"
            onClick={() => handleModeChange('default')}
            className={`flex-1 py-3 rounded-lg transition-all ${
              generationMode === 'default'
                ? 'bg-purple-500'
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
          >
            {t('defaultMode')}
          </button>
          <button
            type="button"
            onClick={() => handleModeChange('custom')}
            className={`flex-1 py-3 rounded-lg transition-all ${
              generationMode === 'custom'
                ? 'bg-purple-500'
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
          >
            {t('customMode')}
          </button>
        </div>

        {/* 只在custom模式时显示isInstrumentalOnly按钮 */}
        {generationMode === 'custom' && (
          <div className="flex items-center justify-between w-full gap-4 px-1 mb-3">
            <div className="flex items-center justify-center">
              <div className="relative inline-flex items-center gap-3 px-5 py-2 bg-gray-700/30 rounded-full backdrop-blur-sm">
                <button
                  onClick={() => setIsInstrumentalOnly(!isInstrumentalOnly)}
                  className="relative w-11 h-5 rounded-full transition-colors duration-200"
                  style={{
                    backgroundColor: isInstrumentalOnly ? '#8B5CF6' : '#374151',
                  }}
                  role="switch"
                  aria-checked={isInstrumentalOnly}
                  aria-label="Toggle instrumental mode"
                >
                  <div
                    className={`
                      absolute w-4 h-4 bg-white rounded-full top-0.5
                      transition-transform duration-200 ease-out
                      ${
                        isInstrumentalOnly ? 'translate-x-6' : 'translate-x-0.5'
                      }
                    `}
                  >
                    <div className="absolute inset-0 rounded-full bg-white/80 blur-[1px]" />
                  </div>
                </button>
                <span
                  onClick={() => setIsInstrumentalOnly(!isInstrumentalOnly)}
                  className={`text-sm transition-colors duration-200 ${
                    isInstrumentalOnly ? 'text-purple-400' : 'text-gray-400'
                  }`}
                >
                  {t('instrumentalOnly')}
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="relative mb-3">
          {/* 条件修改：只有当custom模式且isInstrumentalOnly为true时隐藏textarea */}
          {!(generationMode === 'custom' && isInstrumentalOnly) && (
            <>
              <textarea
                ref={textareaRef}
                onChange={(e) => setPrompt(e.target.value)}
                value={prompt}
                maxLength={generationMode === 'custom' ? 3000 : maxLength}
                className={`pb-8 [&::-webkit-scrollbar]:h-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-700 [&::-webkit-scrollbar-thumb]:rounded-full w-full transition-all duration-300 rounded-lg p-4 text-white resize-none bg-gray-800/40 backdrop-blur-sm border border-gray-600/30 focus:outline-none focus:border-purple-500/30 ${
                  generationMode === 'custom' ? 'bg-opacity-50' : ''
                } ${getHeightClassName()}`}
                placeholder={
                  generationMode === 'custom'
                    ? t('describeDesiredSong2')
                    : t('describeDesiredSong2')
                }
              />

              {/* 右上角放大/缩小按钮 */}
              <button
                onClick={toggleTextareaSize}
                className="absolute top-2 right-2 p-1.5 bg-gray-700/60 hover:bg-gray-600/80 rounded-md text-gray-300 hover:text-white transition-colors"
                aria-label={textareaSize === 'large' ? 'Collapse' : 'Expand'}
              >
                {textareaSize === 'large' ? (
                  <Minimize2 size={12} />
                ) : (
                  <Maximize2 size={12} />
                )}
              </button>

              {/* 右下角字数限制 */}
              <div
                className={`absolute bottom-4 right-3 text-xs ${
                  currentLength >= maxLength
                    ? 'text-red-400/90'
                    : currentLength < minLength
                    ? 'text-amber-400/90'
                    : 'text-gray-400/90'
                }`}
              >
                {currentLength}/{generationMode === 'default' ? 200 : maxLength}
              </div>
            </>
          )}
        </div>

        {/* Style Selection - 只在custom模式显示 */}
        {generationMode === 'custom' && (
          <div className="mb-6 mt-2 p-4 rounded-xl border border-gray-700/50 bg-gray-800/20 backdrop-blur-sm">
            <div className="flex items-center justify-between mb-4 text-[14px]">
              <div className="text-lg font-medium text-purple-500">
                {t('styles')}
              </div>
              <div className="flex gap-2">
                <button
                  onClick={generateRandomStyles}
                  className="flex items-center gap-2 px-4 py-1.5 bg-gray-700/50 rounded-lg hover:bg-gray-600/50 transition-colors"
                >
                  ✨ {t('random')}
                </button>
              </div>
            </div>
            <div className="mb-2 mt-2 rounded-xl border-gray-700/50 bg-gray-800/20 backdrop-blur-sm">
              {/* 风格输入框和标签展示区域 */}
              <div className="mb-4 p-2 min-h-[40px] rounded-lg bg-gray-900/50 border border-gray-700/50 focus-within:border-purple-500/50 transition-colors">
                <div className="flex flex-wrap gap-1.5 items-center">
                  {selectedStyles.map((style) => (
                    <StyleTag
                      key={style}
                      text={style}
                      onRemove={() => removeStyle(style)}
                    />
                  ))}
                  {isEditing ? (
                    <input
                      ref={inputRef}
                      type="text"
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      onKeyDown={handleInputKeyDown}
                      onBlur={() => setIsEditing(false)}
                      className="w-[100px] bg-transparent text-xs text-white outline-none placeholder:text-gray-500 focus:ring-purple-500"
                      placeholder={t('enterStyle')}
                      autoFocus
                    />
                  ) : (
                    <>
                      <button
                        onClick={() => setIsEditing(true)}
                        className="px-2 py-0.5 text-xs text-gray-400 hover:text-purple-400 transition-colors"
                      >
                        + {t('add')}
                      </button>
                      {selectedStyles.length === 0 && (
                        <span className="text-xs text-gray-500 ml-2 max-sm:text-left">
                          {t('enterSongStyle')}
                        </span>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* 分类标签 */}
              <div className="flex justify-between items-center">
                <div className="flex flex-nowrap gap-1.5 mb-3 overflow-x-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
                  {musicCategories.map((category) => (
                    <button
                      key={category.type}
                      onClick={() => setActiveCategory(category.type)}
                      className={`px-2 py-0.5 text-xs font-medium rounded-md transition-all whitespace-nowrap flex-shrink-0 ${
                        activeCategory === category.type
                          ? 'bg-gray-900 text-white border border-gray-700'
                          : 'bg-transparent text-gray-400 border border-gray-700 hover:bg-gray-800'
                      }`}
                    >
                      # {category.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* 风格选择器 */}
              <div className="flex flex-wrap gap-1.5">
                {activeCategory === 'Style'
                  ? styles.map((style, index) => {
                      const isHidden = !isExpanded && index >= 10
                      return (
                        <button
                          key={style.name}
                          onClick={() => toggleStyle(style.name)}
                          className={cn(
                            'px-2.5 py-0.5 text-xs rounded-full transition-all border',
                            selectedStyles.includes(style.name)
                              ? 'bg-purple-500/90 text-white border-purple-400'
                              : 'bg-gray-800/40 text-gray-400 hover:bg-gray-700/40 border-gray-600 hover:border-gray-500',
                            isHidden && 'hidden'
                          )}
                        >
                          {style.name}
                        </button>
                      )
                    })
                  : musicCategories
                      .find((c) => c.type === activeCategory)
                      ?.styles.map((style, index) => {
                        const isHidden = !isExpanded && index >= 10
                        return (
                          <button
                            key={style}
                            onClick={() => toggleStyle(style)}
                            className={cn(
                              'px-2.5 py-0.5 text-xs rounded-full transition-all',
                              selectedStyles.includes(style)
                                ? 'bg-purple-500/90 text-white'
                                : 'bg-gray-800/40 text-gray-400 hover:bg-gray-700/40',
                              isHidden && 'hidden'
                            )}
                          >
                            {style}
                          </button>
                        )
                      })}

                {/* 显示更多按钮 */}
                {(activeCategory === 'Style'
                  ? styles.length
                  : musicCategories.find((c) => c.type === activeCategory)
                      ?.styles.length || 0) > 10 && (
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="px-2.5 py-0.5 text-xs rounded-full bg-purple-500/10 text-purple-400 hover:bg-purple-500/20 transition-all"
                  >
                    {isExpanded ? `${t('showLess')} ↑` : `${t('showMore')} ↓`}
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Title Input - 只在custom模式显示 */}
        {generationMode === 'custom' && (
          <div className="mb-6 space-y-4">
            <div className="flex items-center gap-4">
              <label className="text-white text-sm font-medium whitespace-nowrap">
                {t('title')}
              </label>
              <div className="relative flex-1">
                <input
                  ref={inputRefTitle}
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value.slice(0, 80))}
                  placeholder={t('enterSongTitle')}
                  className="w-full px-4 py-2 bg-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 pr-16"
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-400">
                  {title.length}/80
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 错误提示 */}
        {errorMessage && (
          <div className="p-4 bg-red-500/20 border border-red-500 rounded-lg text-red-400 text-center mb-4">
            {errorMessage}
          </div>
        )}

        {/* Generate Button */}
      </div>
    </div>
  )
}

export default CoverMusicEdit
