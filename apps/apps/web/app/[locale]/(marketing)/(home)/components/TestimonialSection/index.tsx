import Link from 'next/link'
import { FC } from 'react'
import { useTranslations } from 'next-intl'

const TestimonialSection: FC = () => {
  const t = useTranslations()
  
  const testimonials = [
    {
      name: t('testimonials.1.name'),
      role: t('testimonials.1.role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah',
      content: t('testimonials.1.content'),
      tags: [t('testimonials.1.tag0'), t('testimonials.1.tag1')],
    },
    {
      name: t('testimonials.2.name'),
      role: t('testimonials.2.role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Marcus',
      content: t('testimonials.2.content'),
    },
    {
      name: t('testimonials.3.name'),
      role: t('testimonials.3.role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Emily',
      content: t('testimonials.3.content'),
      tags: [t('testimonials.3.tag0')],
    },
    {
      name: t('testimonials.4.name'),
      role: t('testimonials.4.role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Lisa',
      content: t('testimonials.4.content'),
    },
    {
      name: t('testimonials.5.name'),
      role: t('testimonials.5.role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Tom',
      content: t('testimonials.5.content'),
      tags: [t('testimonials.5.tag0')],
    },
    {
      name: t('testimonials.6.name'),
      role: t('testimonials.6.role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Rachel',
      content: t('testimonials.6.content'),
    },
    {
      name: t('testimonials.7.name'),
      role: t('testimonials.7.role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Rachel',
      content: t('testimonials.7.content'),
    },
  ]
  return (
    <section className="relative py-20 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4">
        <h2 className="text-4xl md:text-5xl font-bold text-center text-purple-500 h-28">
          {t('inspiringMusicStories')}
        </h2>
        <p className="text-gray-400 max-w-3xl mx-auto mb-16 text-center text-lg md:text-xl">
          {t('inspiringMusicStoriesDesc')}
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="p-6 bg-gray-800/50 rounded-xl backdrop-blur-sm border border-gray-700 hover:border-gray-600 transition-all hover:shadow-lg hover:shadow-purple-500/10"
            >
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  {/* 头像区域 */}
                  <div className="relative">
                    <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-r from-purple-500 to-pink-500 p-[2px]">
                      <div className="w-full h-full rounded-full overflow-hidden bg-gray-800">
                        <img
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                    {/* 在线状态指示器 */}
                    <div className="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-green-400 border-2 border-gray-800"></div>
                  </div>
                  {/* 用户信息 */}
                  <div>
                    <div className="font-semibold text-purple-400">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-400">
                      {testimonial.role}
                    </div>
                  </div>
                </div>

                {/* 评价内容 */}
                <p className="text-gray-400">{testimonial.content}</p>

                {/* 标签 */}
                {testimonial.tags && (
                  <div className="flex flex-wrap gap-2">
                    {testimonial.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="text-xs px-2 py-1 rounded-full bg-purple-500/20 text-purple-400 border border-purple-500/20"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* <div className="mt-16 text-center space-y-8">
          <h3 className="text-2xl md:text-3xl font-bold whitespace-pre-line">
            {t('makeMusicalDreamsTrue')}
          </h3>
          <Link href="/ai-music-generator" target="_blank">
            <button className="h-12 px-8  mt-3 rounded-md bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-opacity">
              {t('songMakerAI')}
            </button>
          </Link>
        </div> */}
      </div>
    </section>
  )
}

export default TestimonialSection
