// components/ClientMusicPlayer.jsx (客户端组件)
'use client'

import React, { useState, useRef } from 'react'
import {
  X,
  Play,
  Pause,
  Volume2,
  VolumeX,
  ChevronUp,
  ChevronDown,
} from 'lucide-react'
import Atropos from 'atropos/react'

// 定义音乐数据类型
interface MusicItem {
  title: string
  genre: string
  plays: string
  duration: string
  image: string
  audioUrl: string
}

// 定义MusicCard组件的属性类型
interface MusicCardProps {
  title: string
  genre: string
  plays: string
  duration: string
  image: string
  index: number
  onPlay: (index: number) => void
  isPlaying: boolean
  currentPlayingIndex: number | null
}

const MusicCard = ({
  title,
  genre,
  plays,
  duration,
  image,
  index,
  onPlay,
  isPlaying,
  currentPlayingIndex,
}: MusicCardProps) => {
  const isThisPlaying = isPlaying && currentPlayingIndex === index

  return (
    <div className="block bg-[#212936] rounded-lg overflow-hidden transition-transform duration-300 border border-gray-800 hover:scale-105 hover:shadow-xl hover:shadow-purple-500/10 group cursor-pointer">
      <div className="h-32 relative overflow-hidden">
        <div className="absolute inset-0">
          <img src={image} alt={title} className="w-full h-full object-cover" />
        </div>
        <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <button
            onClick={() => onPlay(index)}
            className="bg-white/10 backdrop-blur-sm p-3 rounded-full"
          >
            {isThisPlaying ? (
              <Pause className="w-6 h-6 text-white" />
            ) : (
              <Play className="w-6 h-6 text-white" />
            )}
          </button>
        </div>
      </div>
      <div className="p-3">
        <h4 className="font-medium text-white truncate">{title}</h4>
        <p className="text-sm text-gray-400 truncate">{genre}</p>
        <div className="flex items-center mt-2 text-xs text-gray-400">
          <span>{plays}</span>
          <span className="mx-2">•</span>
          <span>{duration}</span>
        </div>
      </div>
    </div>
  )
}

// 定义PlayerBar组件的属性类型
interface PlayerBarProps {
  currentSong: MusicItem | null
  isPlaying: boolean
  onClose: () => void
  onPlayPause: () => void
  progress: number
  onProgressChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  volume: number
  onVolumeChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  isMuted: boolean
  onMuteToggle: () => void
  currentTime: number
  duration: number
  isInMusicGenerator?: boolean // 新增属性，表示是否在音乐生成器页面中使用
}

const PlayerBar = ({
  currentSong,
  isPlaying,
  onClose,
  onPlayPause,
  progress,
  onProgressChange,
  volume,
  onVolumeChange,
  isMuted,
  onMuteToggle,
  currentTime,
  duration,
  isInMusicGenerator = false, // 默认值为false
}: PlayerBarProps) => {
  const [isExpanded, setIsExpanded] = useState(true)

  // 时间格式化函数
  const formatTime = (seconds: number): string => {
    if (!seconds) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (!currentSong) return null

  // 根据是否在音乐生成器页面调整底部位置
  const bottomPosition = isInMusicGenerator ? 'bottom-14' : 'bottom-0'
  const zIndexValue = isInMusicGenerator ? 'z-20' : 'z-[9999]'

  return (
    <div
      className={`fixed left-0 right-0 ${bottomPosition} ${zIndexValue} bg-[#212936] border-t border-gray-800 transition-all duration-300 shadow-lg`}
    >
      {/* 简化版播放器（收起状态） */}
      {!isExpanded && (
        <div className="p-2 flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1">
            <img
              src={currentSong.image}
              alt={currentSong.title}
              className="w-8 h-8 rounded object-cover"
            />
            <div className="truncate flex-1 mr-4">
              <div className="text-white font-medium truncate text-sm">
                {currentSong.title}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={onPlayPause}
              className="bg-white/10 p-1.5 rounded-full hover:bg-white/20"
            >
              {isPlaying ? (
                <Pause className="w-4 h-4 text-white" />
              ) : (
                <Play className="w-4 h-4 text-white" />
              )}
            </button>

            <button
              onClick={() => setIsExpanded(true)}
              className="text-gray-400 hover:text-white"
            >
              <ChevronUp className="w-4 h-4" />
            </button>

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* 完整播放器（展开状态） */}
      {isExpanded && (
        <div className="p-3 md:p-4">
          <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-center gap-3 md:gap-4">
            {/* 歌曲信息 */}
            <div className="flex items-center w-full md:w-auto md:min-w-[240px] px-1 md:px-0">
              <img
                src={currentSong.image}
                alt={currentSong.title}
                className="w-10 h-10 md:w-12 md:h-12 rounded object-cover"
              />
              <div className="ml-3 truncate flex-1">
                <div className="text-white font-medium truncate">
                  {currentSong.title}
                </div>
                <div className="text-sm text-gray-400 truncate">
                  {currentSong.genre}
                </div>
              </div>
              {/* 在小屏幕上显示控制按钮（移动至右侧） */}
              <div className="md:hidden flex items-center gap-2">
                <button
                  onClick={() => setIsExpanded(false)}
                  className="text-gray-400 hover:text-white px-1"
                >
                  <ChevronDown className="w-5 h-5" />
                </button>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-white px-1"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* 播放控制和进度条 */}
            <div className="flex-1 w-full px-1 py-2 md:py-0">
              <div className="flex flex-row items-center gap-3 md:gap-4 w-full">
                <button
                  onClick={onPlayPause}
                  className="bg-white/10 p-2 rounded-full hover:bg-white/20 flex-shrink-0"
                >
                  {isPlaying ? (
                    <Pause className="w-5 h-5 text-white" />
                  ) : (
                    <Play className="w-5 h-5 text-white" />
                  )}
                </button>

                <div className="flex-1">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={progress}
                    onChange={onProgressChange}
                    className="w-full accent-[#A159E8]"
                  />
                </div>
              </div>

              {/* 时间显示 */}
              <div className="flex justify-between text-xs text-gray-400 pl-10 md:pl-14 mt-1 px-1">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>

            {/* 音量控制 - 在小屏幕上隐藏或简化 */}
            <div className="hidden md:flex items-center gap-2">
              <button
                onClick={onMuteToggle}
                className="text-white hover:text-[#A159E8]"
              >
                {isMuted ? (
                  <VolumeX className="w-5 h-5" />
                ) : (
                  <Volume2 className="w-5 h-5" />
                )}
              </button>
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={onVolumeChange}
                className="w-24 accent-[#A159E8]"
              />
            </div>

            {/* 大屏幕上的控制按钮 */}
            <div className="hidden md:flex items-center gap-2">
              <button
                onClick={() => setIsExpanded(false)}
                className="text-gray-400 hover:text-white"
              >
                <ChevronDown className="w-5 h-5" />
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

interface ClientMusicPlayerProps {
  musicData: MusicItem[]
  isInMusicGenerator?: boolean // 新增属性，表示是否在音乐生成器页面中使用
}

const ClientMusicPlayer = ({
  musicData,
  isInMusicGenerator = false,
}: ClientMusicPlayerProps) => {
  const [currentPlayingIndex, setCurrentPlayingIndex] = useState<number | null>(
    null
  )
  const [isPlaying, setIsPlaying] = useState(false)
  const [progress, setProgress] = useState(0)
  const [volume, setVolume] = useState(100)
  const [isMuted, setIsMuted] = useState(false)
  const audioRef = useRef<HTMLAudioElement>(null)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)

  const handlePlay = (index: number) => {
    if (currentPlayingIndex === index) {
      // 切换播放/暂停状态
      setIsPlaying(!isPlaying)
      if (isPlaying) {
        audioRef.current?.pause()
      } else {
        audioRef.current?.play()
      }
    } else {
      // 播放新的歌曲
      setCurrentPlayingIndex(index)
      setIsPlaying(true)
      if (audioRef.current) {
        // audioRef.current.src = musicData[index].audioUrl;
        audioRef.current.src = `https://knowledge-embedding-sg.oss-ap-southeast-1.aliyuncs.com/${musicData[index].audioUrl}.mp3`

        audioRef.current.play()
      }
    }
  }

  const handleClose = () => {
    setCurrentPlayingIndex(null)
    setIsPlaying(false)
    audioRef.current?.pause()
  }

  const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newProgress = parseInt(e.target.value)
    setProgress(newProgress)
    if (audioRef.current) {
      const time = (newProgress / 100) * audioRef.current.duration
      audioRef.current.currentTime = time
    }
  }

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseInt(e.target.value)
    setVolume(newVolume)
    if (audioRef.current) {
      audioRef.current.volume = newVolume / 100
    }
  }

  const handleMuteToggle = () => {
    setIsMuted(!isMuted)
    if (audioRef.current) {
      audioRef.current.muted = !isMuted
    }
  }

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {musicData.map((item, index) => (
          <MusicCard
            key={index}
            {...item}
            index={index}
            onPlay={handlePlay}
            isPlaying={isPlaying}
            currentPlayingIndex={currentPlayingIndex}
          />
        ))}
      </div>

      <PlayerBar
        currentSong={
          currentPlayingIndex !== null ? musicData[currentPlayingIndex] : null
        }
        isPlaying={isPlaying}
        onClose={handleClose}
        onPlayPause={() => handlePlay(currentPlayingIndex as number)}
        progress={progress}
        onProgressChange={handleProgressChange}
        volume={volume}
        onVolumeChange={handleVolumeChange}
        isMuted={isMuted}
        onMuteToggle={handleMuteToggle}
        currentTime={currentTime}
        duration={duration}
        isInMusicGenerator={isInMusicGenerator} // 传递属性给PlayerBar
      />

      <audio
        ref={audioRef}
        onTimeUpdate={() => {
          if (audioRef.current) {
            const current = audioRef.current.currentTime
            const duration = audioRef.current.duration
            setCurrentTime(current)
            setProgress((current / duration) * 100)
          }
        }}
        onEnded={() => {
          setIsPlaying(false)
          setProgress(0)
          setCurrentTime(0)
        }}
        onLoadedMetadata={() => {
          if (audioRef.current) {
            setDuration(audioRef.current.duration)
            audioRef.current.volume = volume / 100
            audioRef.current.muted = isMuted
          }
        }}
        onError={(e) => {
          // 处理音频加载错误
          console.error('Audio loading error:', e)
          setIsPlaying(false)
        }}
      />
    </>
  )
}

export default ClientMusicPlayer
