'use client'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
// import { getUserFromClientCookies } from '../utils/cookies' // 确保这个路径是正确的

const LabubuPromo = () => {
  const t = useTranslations()
  const [isVisible, setIsVisible] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('android') // Default to iOS tab
  const [actionMessage, setActionMessage] = useState('')
  const [showActionMessage, setShowActionMessage] = useState(false)

  const MediaPreview = ({ wallpaper, isDesktop }) => {
    if (wallpaper.type === 'video') {
      return (
        <video
          autoPlay
          loop
          muted
          playsInline
          className={`
          ${isDesktop ? 'w-full h-28' : 'w-full h-44'} 
          object-cover
          hover:scale-110 
          transition-transform duration-300
        `}
        >
          <source src={wallpaper.media} type="video/mp4" />
        </video>
      )
    }

    return (
      <img
        src={wallpaper.media}
        alt={wallpaper.title}
        className={`
        ${isDesktop ? 'w-20' : 'w-24'} 
        h-auto object-contain 
        transform hover:scale-110 
        transition-transform duration-300
        animate-float
      `}
      />
    )
  }

  useEffect(() => {
    // 延迟显示Labubu图标，让用户先看到页面内容
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  const openModal = () => {
    setIsModalOpen(true)
    // 重置消息状态
    setShowActionMessage(false)
    setActionMessage('')
  }

  const closeModal = () => {
    setIsModalOpen(false)
  }

  // 壁纸示例数据
  // 修改数据结构，添加图片和渐变背景
  const wallpaperExamples = {
    android: [
      {
        id: 1,
        title: t('labubu.androidDynamic'),
        gradient: 'from-orange-500 to-red-500',
        image: '/labubu/4.jpg',
        video: '/labubu/1.mp4',
      },
      {
        id: 2,
        title: t('labubu.interactiveEffects'),
        gradient: 'from-yellow-400 to-orange-500',
        image: '/labubu/5.jpg',
        video: '/labubu/2.mp4',
      },
      {
        id: 3,
        title: t('labubu.musicRhythm'),
        gradient: 'from-purple-600 to-blue-500',
        image: '/labubu-6.png',
        video: '/labubu/3.mp4',
      },
    ],
    ios: [
      {
        id: 1,
        title: t('labubu.iosDynamic'),
        gradient: 'from-purple-500 to-pink-500',
        image: '/labubu/1.jpg',
      },
      {
        id: 2,
        title: t('labubu.nightLight'),
        gradient: 'from-blue-600 to-indigo-600',
        image: '/labubu/2.jpg',
      },
      {
        id: 3,
        title: t('labubu.seasonal'),
        gradient: 'from-green-400 to-teal-500',
        image: '/labubu/3.jpg',
      },
    ],

    desktop: [
      {
        id: 1,
        title: t('labubu.desktopDynamic'),
        gradient: 'from-indigo-500 via-purple-500 to-pink-500',
        video: '/labubu/12.mp4',
      },
      {
        id: 2,
        title: t('labubu.screensaver'),
        gradient: 'from-cyan-500 to-blue-500',
        image: '/labubu/pc1.jpg',
      },
      {
        id: 3,
        title: t('labubu.hdScene'),
        gradient: 'from-emerald-500 to-teal-500',
        video: '/labubu/8.mp4',
      },
    ],
  }

  // 处理"立即获取"按钮点击
  const handleGetWallpaper = () => {
    const user = getUserFromClientCookies()

    if (!user || !user.email) {
      // User not logged in
      setActionMessage(t('labubu.pleaseLoginToGetExclusiveWallpapers'))
      setShowActionMessage(true)
    } else if (user.membershipStatus !== 'active') {
      // User logged in but not a member
      setActionMessage(t('labubu.subscriptionRequiredToGetExclusiveWallpapers'))
      setShowActionMessage(true)
    } else {
      // User logged in and is a member, download directly
      downloadWallpapers()
    }
  }

  // 下载壁纸资源包
  const downloadWallpapers = () => {
    // Download logic can be implemented based on actual situation
    // For example, create a download link and click it
    setActionMessage(t('labubu.preparingDownloadPleaseWait'))
    setShowActionMessage(true)

    // Simulate download delay
    setTimeout(() => {
      setActionMessage(
        t('labubu.downloadSuccessfulPleaseCheckYourDownloadsFolder')
      )

      // Actual download logic
      const link = document.createElement('a')
      link.href =
        'https://jrgocbhuubiqyvrbsqut.supabase.co/storage/v1/object/sign/labubu/labubu%20dynamic%20wallpaper.zip?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV85NTljNzk2NS00MjkxLTQ3YmQtOTY1MS01NThiNWIyZWVkODAiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJsYWJ1YnUvbGFidWJ1IGR5bmFtaWMgd2FsbHBhcGVyLnppcCIsImlhdCI6MTc0OTg3NDY1MCwiZXhwIjoxNzUyNDY2NjUwfQ.2kSQVMggwagc-6OPCQ9QMaxEzRwVYgvaCydIk2lRz_k' // Assumed resource pack path
      link.download = 'labubu-wallpapers.zip'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }, 1500)
  }

  return (
    <>
      {/* Labubu浮动图标 */}
      {isVisible && (
        <div
          className="fixed z-50 cursor-pointer transition-all duration-500 bottom-[50px] right-[10px] md:bottom-[100px] md:right-[30px]"
          style={{
            opacity: isVisible ? 1 : 0,
            transform: isVisible ? 'translateY(0)' : 'translateY(20px)',
          }}
          onClick={openModal}
        >
          <div
            onClick={openModal}
            className="relative w-24 h-16 md:w-48 md:h-24 labubu-float"
          >
            <img
              src="/labubu.png"
              alt="Labubu Character"
              className="w-full h-full object-contain drop-shadow-xl hover:scale-110 transition-transform duration-300"
            />
            <div className="absolute -top-6 md:-top-8 left-1/2 transform -translate-x-1/2 bg-indigo-600 text-white px-2 md:px-3 py-0.5 md:py-1 rounded-full text-[10px] md:text-xs font-medium animate-pulse whitespace-nowrap">
              {t('labubu.gift')}
            </div>
          </div>
        </div>
      )}

      {/* Labubu弹窗 */}
      {isModalOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              closeModal()
            }
          }}
        >
          <div className="relative bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500 rounded-2xl p-6 max-w-xl w-full mx-4 shadow-2xl transform transition-all animate-modal-in overflow-y-auto max-h-[85vh]">
            <button
              className="absolute top-3 right-3 text-white/70 hover:text-white transition-colors"
              onClick={closeModal}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            <div className="text-center">
              <div className="inline-block mb-4 relative">
                <div className="absolute inset-0 rounded-full bg-white/30 animate-ping"></div>
                <img
                  src="/labubu.png"
                  alt="Labubu Character"
                  className="w-28 h-28 object-contain mx-auto relative z-10"
                />
              </div>

              <h3 className="text-2xl font-bold text-white mb-2">
                {t('labubu.labubuDynamicWallpapers')}
              </h3>
              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 mb-4">
                <p className="text-white text-lg font-medium mb-3">
                  {t('labubu.subscribeNowForFreeWallpapers')}
                </p>

                {/* Horizontal feature list */}
                <div className="flex flex-wrap justify-center gap-2">
                  <div className="bg-white/10 rounded-lg px-3 py-2 flex items-center">
                    <span className="mr-2 text-yellow-300">✨</span>
                    <span className="text-white text-sm">
                      {t('labubu.hdDynamicWallpapers')}
                    </span>
                  </div>
                  <div className="bg-white/10 rounded-lg px-3 py-2 flex items-center">
                    <span className="mr-2 text-yellow-300">✨</span>
                    <span className="text-white text-sm">
                      {t('labubu.exclusiveSoundEffects')}
                    </span>
                  </div>
                  <div className="bg-white/10 rounded-lg px-3 py-2 flex items-center">
                    <span className="mr-2 text-yellow-300">✨</span>
                    <span className="text-white text-sm">
                      {t('labubu.tenPlusThemeScenes')}
                    </span>
                  </div>
                </div>
              </div>

              {/* Wallpaper preview area - Simplified version */}
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 mb-4">
                <h4 className="text-white font-medium mb-3">
                  {t('labubu.wallpaperPreview')}
                </h4>

                {/* Platform selection tabs */}
                <div className="flex justify-center mb-4 border-b border-white/20">
                  <button
                    className={`px-4 py-2 text-sm font-medium transition-colors ${
                      activeTab === 'ios'
                        ? 'text-white border-b-2 border-white'
                        : 'text-white/60 hover:text-white'
                    }`}
                    onClick={() => setActiveTab('ios')}
                  >
                    {t('labubu.mobileWallpapers')}
                  </button>
                  <button
                    className={`px-4 py-2 text-sm font-medium transition-colors ${
                      activeTab === 'android'
                        ? 'text-white border-b-2 border-white'
                        : 'text-white/60 hover:text-white'
                    }`}
                    onClick={() => setActiveTab('android')}
                  >
                    {t('labubu.dynamicWallpapers')}
                  </button>
                  <button
                    className={`px-4 py-2 text-sm font-medium transition-colors ${
                      activeTab === 'desktop'
                        ? 'text-white border-b-2 border-white'
                        : 'text-white/60 hover:text-white'
                    }`}
                    onClick={() => setActiveTab('desktop')}
                  >
                    {t('labubu.desktopWallpapers')}
                  </button>
                </div>

                {/* Wallpaper preview - Simplified to one row of three columns */}
                <div className={`grid grid-cols-3 gap-2`}>
                  {wallpaperExamples[activeTab].map((wallpaper) => (
                    <div key={wallpaper.id} className="relative group">
                      <div
                        className={`relative overflow-hidden rounded-lg border border-white/30 h-28`}
                      >
                        {/* Gradient background */}
                        <div
                          className={`absolute inset-0 bg-gradient-to-br ${wallpaper.gradient}`}
                        ></div>

                        {/* Media content */}
                        <div className="absolute inset-0 flex items-center justify-center">
                          {wallpaper.video ? (
                            <video
                              autoPlay
                              loop
                              muted
                              playsInline
                              className="w-full h-full object-cover"
                            >
                              <source src={wallpaper.video} type="video/mp4" />
                              {/* If video fails to load, show image as fallback */}
                              <img
                                src={wallpaper.image}
                                alt={wallpaper.title}
                                className={`
                  w-20
                  h-auto object-contain 
                  transform hover:scale-110 
                  transition-transform duration-300
                  animate-float
                `}
                              />
                            </video>
                          ) : (
                            <img
                              src={wallpaper.image}
                              alt={wallpaper.title}
                              className={`
                w-26
                h-auto object-contain 
                transform hover:scale-110 
                transition-transform duration-300
                animate-float
              `}
                            />
                          )}
                        </div>

                        {/* Mask gradient */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10"></div>

                        {/* Video play icon */}
                        {wallpaper.video && (
                          <div className="absolute top-2 right-2 z-20">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4 text-white/70"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                              />
                            </svg>
                          </div>
                        )}

                        {/* Title */}
                        <div className="absolute bottom-1 left-0 right-0 text-white text-xs text-center font-medium z-20 px-1">
                          {wallpaper.title}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Operation feedback message */}
              {showActionMessage && (
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 mb-4 text-white animate-fade-in flex items-center justify-center gap-3 flex-wrap">
                  <span>{actionMessage}</span>

                  {/* If not logged in prompt, show login link */}
                  {actionMessage.includes('Please login') && (
                    <a
                      href="/auth/login"
                      className="bg-white/30 hover:bg-white/40 text-white px-3 py-1 rounded-lg text-sm font-medium transition-colors whitespace-nowrap"
                      onClick={(e) => {
                        e.preventDefault()
                        closeModal()
                        window.location.href = '/auth/login'
                      }}
                    >
                      {t('freeTrail.loginNow')}
                    </a>
                  )}

                  {/* If non-member prompt, show subscription link */}
                  {actionMessage.includes('Subscription required') && (
                    <a
                      href="/pricing"
                      className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all hover:shadow-lg hover:scale-105 whitespace-nowrap"
                      onClick={(e) => {
                        e.preventDefault()
                        closeModal()
                        window.location.href = '/pricing'
                      }}
                    >
                      {t('price.subscribeButton')}
                    </a>
                  )}
                </div>
              )}

              <div className="space-y-3">
                <button
                  className="w-full bg-white text-indigo-600 font-bold py-3 px-6 rounded-xl hover:bg-indigo-50 transition-colors duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center"
                  onClick={handleGetWallpaper}
                >
                  <span>{t('labubu.getNow')}</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 ml-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>

                <button
                  className="text-white/80 hover:text-white text-sm underline transition-colors"
                  onClick={closeModal}
                >
                  {t('labubu.maybeLater')}
                </button>
              </div>

              <div className="mt-4 flex items-center justify-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-yellow-300 animate-pulse"></div>
                <p className="text-white/70 text-xs">
                  {t('labubu.limitedTimeOfferOnlySevenDaysLeft')}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add CSS animations */}
      <style jsx>{`
        .labubu-float {
          animation: floating 3s ease-in-out infinite;
          filter: drop-shadow(0 10px 15px rgba(99, 102, 241, 0.5));
        }

        @keyframes floating {
          0%,
          100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-15px);
          }
        }

        @keyframes modal-in {
          0% {
            opacity: 0;
            transform: scale(0.9);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }

        .animate-modal-in {
          animation: modal-in 0.3s ease-out forwards;
        }

        .animate-pulse {
          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .animate-pulse-slow {
          animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .animate-fade-in {
          animation: fade-in 0.3s ease-out forwards;
        }

        @keyframes fade-in {
          0% {
            opacity: 0;
          }
          100% {
            opacity: 1;
          }
        }

        @keyframes pulse {
          0%,
          100% {
            opacity: 1;
          }
          50% {
            opacity: 0.5;
          }
        }
      `}</style>
    </>
  )
}

export default LabubuPromo
