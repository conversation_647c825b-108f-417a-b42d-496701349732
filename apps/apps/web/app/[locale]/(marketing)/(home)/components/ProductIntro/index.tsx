import { useTranslations } from 'next-intl'

export default function ProductIntro() {
  const t = useTranslations()
  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-16">
      <div className="grid md:grid-cols-2 gap-12 items-center">
        <div className="space-y-8">
          <div>
            <div className="flex items-center gap-2 text-purple-400 mb-2">
              <span className="text-2xl">✦</span>
              <span>AI Make Song</span>
            </div>
            <h2 className="text-3xl font-bold text-purple-500 !leading-tight mb-6">
              {t('intro.mainTitle')}
            </h2>
            <p className="text-gray-400 whitespace-pre-line text-lg">
              {/* {t('intro.description')} */}
              {t('aiMakeSong.description')}
            </p>
          </div>

          <div>
            <h2 className="text-3xl font-bold text-purple-500 !leading-tight mb-6">
              {t('uniqueness.title')}
            </h2>
            <p className="text-gray-400 text-lg">{t('uniqueness.content')}</p>
          </div>

          <div>
            <h2 className="text-3xl font-bold text-purple-500 !leading-tight mb-6">
              {t('keyFeatures.title')}
            </h2>
            <ul className="text-gray-400 text-lg space-y-2">
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 flex-shrink-0 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"></span>
                <span>{t('keyFeatures.item1')}</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 flex-shrink-0 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"></span>
                <span>{t('keyFeatures.item2')}</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 flex-shrink-0 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"></span>
                <span>{t('keyFeatures.item3')}</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 flex-shrink-0 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"></span>
                <span>{t('aiRapperModeDesc')}</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="relative">
          <div className="w-full aspect-[1/1.2]">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/30 via-pink-500/30 to-purple-600/30 rounded-3xl blur-3xl"></div>
            <div className="relative w-full h-full rounded-3xl overflow-hidden">
              <img
                src="/images/singer.png"
                alt="Product showcase"
                className="w-full h-full object-cover object-center"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
