// components/FaqSection.tsx
import { FC } from 'react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@ui/components/accordion'
import { cn } from '@ui/lib'
import { getTranslations } from 'next-intl/server'

const FaqSectionPro: FC = async () => {
  const t = await getTranslations()
  const faqData = [
    {
      question: t('FaqSectionPro.faqData.a.question'),
      answer: t('FaqSectionPro.faqData.a.answer'),
      feature: [
        t('FaqSectionPro.faqData.a.feature1'),
        t('FaqSectionPro.faqData.a.feature2'),
        t('FaqSectionPro.faqData.a.feature3'),
      ],
      conclusion: t('FaqSectionPro.faqData.a.conclusion'),
    },
    {
      question: t('FaqSectionPro.faqData.b.question'),
      answer: [t('FaqSectionPro.faqData.b.answer')],
      feature: [
        t('FaqSectionPro.faqData.b.feature1'),
        t('FaqSectionPro.faqData.b.feature2'),
        t('FaqSectionPro.faqData.b.feature3'),
        t('FaqSectionPro.faqData.b.feature4'),
        t('FaqSectionPro.faqData.b.feature5'),
      ],
      conclusion: t('FaqSectionPro.faqData.b.conclusion'),
    },
    {
      question: t('FaqSectionPro.faqData.c.question'),
      answer: [t('FaqSectionPro.faqData.c.answer')],
      feature: [
        t('FaqSectionPro.faqData.c.feature1'),
        t('FaqSectionPro.faqData.c.feature2'),
        t('FaqSectionPro.faqData.c.feature3'),
      ],
      conclusion: t('FaqSectionPro.faqData.c.conclusion'),
    },
    {
      question: t('FaqSectionPro.faqData.d.question'),
      answer: [t('FaqSectionPro.faqData.d.answer')],
      feature: [
        t('FaqSectionPro.faqData.d.feature1'),
        t('FaqSectionPro.faqData.d.feature2'),
        t('FaqSectionPro.faqData.d.feature3'),
        t('FaqSectionPro.faqData.d.feature4'),
      ],
      conclusion: t('FaqSectionPro.faqData.d.conclusion'),
    },
    {
      question: t('FaqSectionPro.faqData.e.question'),
      answer: [t('FaqSectionPro.faqData.e.answer')],
      feature: [
        t('FaqSectionPro.faqData.e.feature1'),
        t('FaqSectionPro.faqData.e.feature2'),
        t('FaqSectionPro.faqData.e.feature3'),
        t('FaqSectionPro.faqData.e.feature4'),
      ],
      conclusion: t('FaqSectionPro.faqData.e.conclusion'),
    },
    {
      question: t('FaqSectionPro.faqData.h.question'),
      answer: [t('FaqSectionPro.faqData.h.answer')],
    },
    {
      question: t('FaqSectionPro.faqData.i.question'),
      answer: [t('FaqSectionPro.faqData.i.answer')],
    },
    {
      question: t('FaqSectionPro.faqData.j.question'),
      answer: [t('FaqSectionPro.faqData.j.answer')],
    },
    {
      question: t('FaqSectionPro.faqData.f.question'),
      answer: [t('FaqSectionPro.faqData.f.answer')],
      feature: [
        t('FaqSectionPro.faqData.f.feature1'),
        t('FaqSectionPro.faqData.f.feature2'),
        t('FaqSectionPro.faqData.f.feature3'),
        t('FaqSectionPro.faqData.f.feature4'),
        t('FaqSectionPro.faqData.f.feature5'),
      ],
      conclusion: t('FaqSectionPro.faqData.f.conclusion'),
    },
    {
      question: t('FaqSectionPro.faqData.g.question'),
      answer: [t('FaqSectionPro.faqData.g.answer')],
      feature: [
        t('FaqSectionPro.faqData.g.feature1'),
        t('FaqSectionPro.faqData.g.feature2'),
        t('FaqSectionPro.faqData.g.feature3'),
      ],
      conclusion: t('FaqSectionPro.faqData.g.conclusion'),
    },
    {
      question: t('FaqSectionPro.faqData.r.question'),
      answer: t('FaqSectionPro.faqData.r.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.s.question'),
      answer: t('FaqSectionPro.faqData.s.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.t.question'),
      answer: t('FaqSectionPro.faqData.t.answer'),
    },
    {
      question: t('FaqSectionPro.faqData.u.question'),
      answer: t('FaqSectionPro.faqData.u.answer'),
    },
  ]
  return (
    <section className=" mx-auto">
      <section className={cn('py-16 lg:py-24')} id="faq">
        <div className="container max-w-3xl">
          <div className="mb-12 text-center">
            <h2 className="mb-1 font-bold text-5xl text-purple-500 !leading-normal">
              {t('faq.title')}
            </h2>
            <p className="text-lg opacity-50">{t('faq.description')}</p>
          </div>
          <Accordion type="single" collapsible className="flex flex-col gap-3">
            {faqData.map((item, i) => (
              <AccordionItem
                key={i}
                value={`faq-item-${i}`}
                className="rounded-xl border bg-card px-6 py-4"
              >
                <AccordionTrigger className="py-2 text-lg text-left">
                  {item.question}
                </AccordionTrigger>
                <AccordionContent className="">
                  <div className="space-y-3 leading-6">
                    <p className="whitespace-pre-line">{item.answer}</p>
                    {item.feature?.length! > 0 && (
                      <ul>
                        {item.feature?.map((subItem, subIndex) => (
                          <li
                            className={`flex gap-2 ${
                              i === 0 ? 'items-center' : 'items-start'
                            }`}
                            key={subIndex}
                          >
                            {i === 0 ? (
                              <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0"></span>
                            ) : (
                              <span className="w-6 h-6 rounded-full text-purple-500 flex-shrink-0 flex items-center justify-center text-sm font-medium">
                                {subIndex + 1}
                              </span>
                            )}
                            <span>{subItem}</span>
                          </li>
                        ))}
                      </ul>
                    )}
                    {item.conclusion && <p>{item.conclusion}</p>}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>

        {/* <div className="text-center mt-12">
          <Link href="/ai-music-generator" target="_blank">
            <span className="inline-block h-12 px-8 text-lg rounded-md bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-opacity leading-[48px]">
              {t('freeOnlineMakeSong')}
            </span>
          </Link>
        </div> */}
      </section>
    </section>
  )
}

export default FaqSectionPro
