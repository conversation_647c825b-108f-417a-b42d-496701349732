// components/AiSongGenerator.tsx
import { Music, Mic, Radio, Speaker, FolderPen, Headphones } from 'lucide-react'
import Link from 'next/link'
import { useTranslations } from 'next-intl'

export function AiSongGenerator() {
  const t = useTranslations()

  const features = [
    {
      icon: FolderPen,
      title: t('makeSongForSocialCreators'),
      content: [
        t('unleashMusicPossibilities'),
        t('createBgMusic'),
        t('randomSoundMaker'),
      ],
    },
    {
      icon: Radio,
      title: t('makeSongForIndieMusicians'),
      content: [
        t('trueRoyaltyFree'),
        t('generateProQualityTracks'),
        t('podcastMusicMaker'),
      ],
    },
    {
      icon: Music,
      title: t('generateGameMusic'),
      content: [
        t('makeSongGameStyles'),
        t('gameMusicEnhanceExp'),
        t('freeDownloadGameAudio'),
      ],
    },
    {
      icon: Mic,
      title: t('getPodcastMusic'),
      content: [t('solveCopyrightIssues'), t('podcastMusicGen')],
    },
    {
      icon: Headphones,
      title: t('musicForAdvertising'),
      content: [
        t('makeSongForBrands'),
        t('generateBrandEarworms'),
        t('usersCanEasily'),
      ],
    },
    {
      icon: Speaker,
      title: t('themedSongCreate'),
      content: [
        t('aiGenThemeMusic'),
        t('rapSongMakerDesc'),
        t('aiRemixThemes'),
      ],
    },
  ]

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-3 mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-purple-500 mb-4">
            {t('aiSongGenAnyMoment')}
          </h2>
          <p className="text-lg md:text-xl text-gray-400">
            {t('makeSongsForMedia')}
          </p>
        </div>

        <div className="space-y-6">
          {features.map((feature, index) => (
            <div
              key={index}
              className="rounded-xl border border-gray-800 bg-gray-800/50 hover:bg-gray-800/70 transition-all duration-300"
            >
              <div className="p-6 border-b border-gray-800">
                <h3 className="flex items-center gap-3 font-semibold text-lg">
                  <feature.icon className="w-6 h-6 text-purple-400" />
                  <span className="text-purple-400">{feature.title}</span>
                </h3>
              </div>
              <div className="p-6 space-y-3 text-gray-400">
                {feature.content.map((text, idx) => (
                  <p key={idx} className="leading-relaxed">
                    {text}
                  </p>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link href="/ai-music-generator" target="_blank">
            <button className="h-12 px-8 text-lg rounded-md bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-opacity">
              {t('musicMakerNow')}
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default AiSongGenerator
