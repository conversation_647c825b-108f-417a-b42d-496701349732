'use client'
import { use<PERSON><PERSON>back, useEffect, useRef, useState } from 'react'
import {
  Brush,
  Loader2,
  Maximize2,
  Minimize2,
  Pause,
  Play,
  Volume2,
  Wand2,
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { usePathname, useRouter } from 'next/navigation'
import mofabang from '../../../../../../public/images/mofabang.png'
import ProgressBar from './components/ProgressBar'
import { useLocale, useTranslations } from 'next-intl'
import { getUserFromClientCookies } from '../../../../../utils/client-cookies'
import { createPortal } from 'react-dom'
import ShowLoginModal from '@shared/components/ShowLoginModal'
import { validateUserForGeneration } from '../../../../../utils/userValidation'
import { POINTS_CONFIG } from '../../../../../../constants'
import { consumeUserPoints } from '../../../../../utils/pointsService'
import {
  detectMobileDevice,
  getUserId,
  saveObj,
} from '../../../../../utils/lib'
import toast from 'react-hot-toast'
import { cn } from '@ui/lib'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import PricingSection from '../PricingSection'
import { enExamples, zhExamples } from './config'
import * as Tooltip from '@radix-ui/react-tooltip'
import QueueModal from './components/QueueModal'
import Cookies from 'js-cookie'
import TooltipButton from './components/TooltipButton'
import { songExamples } from './data/songExamples'
import ModelSelector from './components/ModelSelector'
import { encryptVerification } from '@/utils'

// 标题魔法生成
const COOLDOWN_TIME = {
  FREE: 2 * 60000, // 2分钟 = 60000ms
  PREMIUM: 5000, // 5秒 = 5000ms
}

const Toast = ({ message }: { message: string }) => {
  if (typeof window === 'undefined') return null

  return createPortal(
    <div
      style={{
        position: 'fixed',
        left: '50%',
        top: '10%',
        transform: 'translateX(-50%)',
        zIndex: 9999,
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
      >
        <div className="w-[400px] px-6 py-3 rounded-lg bg-gradient-to-r from-purple-500/90 to-pink-500/90 backdrop-blur-md text-white shadow-lg border border-white/10 text-center">
          {message}
        </div>
      </motion.div>
    </div>,
    document.body
  )
}

interface TaskItem {
  id: string
  progress: number
  status: number
  cld2AudioUrl: string
  progressMsg: string
  cld2ImageUrl: string
  inputType?: string
}

export interface TaskResponse {
  code: number
  msg: string
  data?: {
    taskBatchId: string
    items: TaskItem[]
  }
}

interface IndexProps {
  batchId?: string
  locale?: string
}

const StyleTag = ({
  text,
  onRemove,
}: {
  text: string
  onRemove: () => void
}) => (
  <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-purple-500/20 text-purple-300 rounded-md text-xs">
    {text}
    <button onClick={onRemove} className="hover:text-purple-200">
      ×
    </button>
  </span>
)

const Index = ({ batchId }: IndexProps) => {
  const t = useTranslations()
  const router = useRouter() // 添加路由器
  const [generationMode, setGenerationMode] = useState<
    'custom' | 'description'
  >('custom')
  const [selectedStyles, setSelectedStyles] = useState<string[]>([])
  const [title, setTitle] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [taskBatchId, setTaskBatchId] = useState<string>(batchId || '')
  const providerIdRef = useRef<string>('')
  const [progress, setProgress] = useState(0)
  const [progressMessage, setProgressMessage] = useState('')
  const [lyrics, setLyrics] = useState('')
  const [errorMessage, setErrorMessage] = useState('') // 新增错误消息 state
  const user: any = getUserFromClientCookies()
  const membershipStatus = Cookies.get('membershipStatus')
  const [activeCategory, setActiveCategory] = useState<string>('Genre')
  const [isExpanded, setIsExpanded] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [textareaSize, setTextareaSize] = useState('normal') // 使用 'normal' 或 'large' 来表示文本区大小
  const [isEditing, setIsEditing] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const inputRef = useRef<any>(null)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [userInfo, setUserInfo] = useState<any>(null)
  const [hasUsedFreeGen, setHasUsedFreeGen] = useState(false)

  const [songModel, setSongModel] = useState('aimakesong1.0')

  const [showQueueModal, setShowQueueModal] = useState(false)

  const inputRefTitle = useRef(null)

  // 在组件顶部定义 ref
  const waitCountRef = useRef(0)
  const targetWaitRef = useRef(0)

  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    if (localStorage.getItem('REDIRECT_PATH')) {
      router.push(localStorage.getItem('REDIRECT_PATH') as string)
      localStorage.removeItem('REDIRECT_PATH')
    }
  }, [])

  useEffect(() => {
    // 在客户端运行时检测设备
    console.log(
      'faith=============detectMobileDevice().isMobile',
      detectMobileDevice().isMobile
    )
    setIsMobile(detectMobileDevice().isMobile)
  }, [])

  // 检查是否已使用过免费次数
  useEffect(() => {
    const usedFreeGen = localStorage.getItem('hasUsedFreeGen')
    if (usedFreeGen) {
      setHasUsedFreeGen(true)
    }
  }, [])

  // 在你的主组件中添加状态
  const [showToast, setShowToast] = useState(false)
  const [toastMessage, setToastMessage] = useState('')

  const [isGeneratingLyrics, setIsGeneratingLyrics] = useState(false)
  const [hasGeneratedLyrics, setHasGeneratedLyrics] = useState(false)
  const [originalDescription, setOriginalDescription] = useState('')

  const [isInstrumentalOnly, setIsInstrumentalOnly] = useState(false)
  const [payDialogTitle, setPayDialogTitle] = useState('')

  const pathname = usePathname()

  const userInfoRef = useRef(null)

  const locale = useLocale()

  useEffect(() => {
    const fetchUser = async () => {
      if (user && user.email) {
        const userInfoResponse = await fetch(
          `/api/user/info?email=${encodeURIComponent(user.email)}`
        )
        const userInfoData = await userInfoResponse.json()
        if (userInfoData.success) {
          setUserInfo({
            ...userInfoData.data,
            membershipStatus: userInfoData.data.membership_status,
          })
        }
      }
    }
    fetchUser()
  }, [pathname, user?.email])

  useEffect(() => {
    const isPremium = userInfo?.membershipStatus === 'active'
    console.log('🚀 ~ isPremium:', userInfo, isPremium)
    if (isPremium) {
      setSongModel('aimakesong2.0')
    } else {
      setSongModel('aimakesong1.0')
    }
  }, [userInfo])

  const fetchMusicData = async (innerBatchId: string) => {
    const response = await fetch(
      `/api/music/status?taskBatchId=${innerBatchId}&user_id=${getUserId()}`
    )
    const data: TaskResponse = await response.json()

    const {
      inputType,
      prompt,
      title,
      tags,
      cld2AudioUrl,
      cld2VideoUrl,
      cld2ImageUrl,
    }: any = data.data?.items[0] || {}

    if (inputType === '20') {
      setGenerationMode('custom')
    }

    if (tags.length > 0) {
      const tagsArray = tags.split(',').filter((tag: any) => tag.trim() !== '')
      setSelectedStyles(tagsArray)
    }

    setTitle(title)
    console.log(prompt, 'prompt----prompt')

    setLyrics(prompt)
  }

  useEffect(() => {
    if (batchId) {
      fetchMusicData(batchId)
    }

    // 处理来自歌词生成器的数据
    const lyricsToMusicData = localStorage.getItem('LYRICS_TO_MUSIC_DATA')
    if (lyricsToMusicData) {
      try {
        const musicData = JSON.parse(lyricsToMusicData)
        setTitle(musicData.title || '')
        setLyrics(musicData.lyrics || '')
        setSelectedStyles(musicData.selectedStyles || [])
        setGenerationMode(musicData.generationMode || 'custom')

        // 显示成功提示
        setToastMessage('Lyrics imported successfully!')
        setShowToast(true)
        setTimeout(() => setShowToast(false), 3000)

        // 清除本地存储的数据
        localStorage.removeItem('LYRICS_TO_MUSIC_DATA')

        // 滚动到歌词区域
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
            })
          }
        }, 100)
      } catch (error) {
        console.error('Error parsing lyrics to music data:', error)
        localStorage.removeItem('LYRICS_TO_MUSIC_DATA')
      }
    }

    // 处理登录前的缓存数据
    const userBeforeLoginCache = localStorage.getItem('USER_BEFORE_LOGIN_CACHE')
    if (userBeforeLoginCache) {
      const cacheInfo = JSON.parse(userBeforeLoginCache)
      setTitle(cacheInfo.title)
      setLyrics(cacheInfo.lyrics)
      setSelectedStyles(cacheInfo.selectedStyles)
      localStorage.removeItem('USER_BEFORE_LOGIN_CACHE')
    }
  }, []) // 空依赖数组，只在组件挂载时执行

  // 在生成音乐时重置模拟进度
  // 生成音乐
  const generateMusic = async () => {
    // 生成音乐按钮
    if (
      !lyrics.trim() &&
      !(isInstrumentalOnly && generationMode === 'description')
    ) {
      setToastMessage(
        generationMode === 'custom' ? t('tipwith50') : t('descriptionPrompt')
      )
      setShowToast(true)
      // @ts-ignore
      textareaRef.current?.focus()
      setTimeout(() => setShowToast(false), 4000)
      return
    }

    // 未登录情况下
    if (!user) {
      setShowLoginModal(true)
      const cacheInfo = {
        title,
        lyrics,
        selectedStyles,
      }
      localStorage.setItem('USER_BEFORE_LOGIN_CACHE', JSON.stringify(cacheInfo))
      return
      // // 用户没有生成过音乐，让他免费生成一次
      // if (!hasUsedFreeGen) {
      //   // 未登录且未使用过免费次数
      //   localStorage.setItem('hasUsedFreeGen', 'true')
      //   setHasUsedFreeGen(true)
      //   // router.push('/ai-music-generator')
      // } else {
      //   // 用户已经生成过音乐，提示用户登录
      //   setShowLoginModal(true)
      //   return
      // }
    }

    if (isGenerating) {
      return
    }

    // 2025-04-03 去掉 start
    // 用户是否要有一个等待的弹窗
    if (
      user.membershipStatus !== 'active' &&
      !sessionStorage.getItem('queue_played')
    ) {
      setShowQueueModal(true)
      return
    }

    if (sessionStorage.getItem('queue_played')) {
      sessionStorage.removeItem('queue_played')
    }
    // 2025-04-03 去掉 end

    // 增加支付门槛的策略，如果不是订阅用户
    // 给假的提示，就是提示用户，

    setIsGenerating(true)
    setErrorMessage('')
    setProgress(5)

    try {
      // 根据 generationMode 构建请求体
      const requestBody: any = {
        inputType: generationMode === 'description' ? '10' : '20',
        makeInstrumental: isInstrumentalOnly.toString(),
        title: title,
        continueClipId: '',
        continueAt: '',
        mvVersion: songModel.includes('2.0') ? 'chirp-v4-5' : 'chirp-v4',
        callbackUrl: 'https://www.aimakesong.com/api/music/success-callback',
      }

      // 根据不同模式添加不同参数
      if (generationMode === 'description') {
        if (locale === 'zh') {
          requestBody.gptDescriptionPrompt = `来一首关于：${lyrics}的歌曲，我对歌曲的风格要求是${selectedStyles.join(
            ','
          )}`
        } else {
          requestBody.gptDescriptionPrompt = ` ${lyrics}
          Musical Style: ${selectedStyles.join(', ')}`
        }
      } else {
        requestBody.prompt = lyrics
        requestBody.tags = selectedStyles.join(',')
      }

      // 使用封装的验证函数
      if (user) {
        const validationResult: any = await validateUserForGeneration(
          user,
          POINTS_CONFIG.MUSIC_GENERATION
        )
        console.log('🚀 ~ validationResult:', validationResult)
        if (!validationResult.success) {
          setIsGenerating(false)
          setProgress(0)

          if (isMobile) {
            showMobileSubscriptionPrompt(validationResult.message)
          } else {
            setPayDialogTitle(validationResult.message)
          }

          return
        }

        userInfoRef.current = validationResult.userInfo
        saveObj(validationResult.userInfo)
        requestBody.user_id = validationResult.userInfo.email
      }

      // 加密prompt并添加到请求头
      const encryptedPrompt = await encryptVerification(requestBody.prompt)

      const response = await fetch('/api/music/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Prompt': encryptedPrompt,
        },
        body: JSON.stringify(requestBody),
      })

      if (response.status === 403) {
        const errorData = await response.json()
        // 处理403错误
        console.error('访问受限:', errorData.msg)

        // 显示错误消息给用户
        alert(errorData.msg || 'error')

        return // 终止后续处理
      }

      const data = await response.json()

      if (data.code === 200) {
        // 登录情况下才消耗积分
        if (user && userInfoRef.current) {
          // @ts-ignore
          user.id = userInfoRef.current.id || -1
          const consumeResult = await consumeUserPoints(
            {
              ...user,
              // @ts-ignore
              id: userInfoRef.current.id,
            },
            POINTS_CONFIG.MUSIC_GENERATION
          )
          console.log('faith=============消耗积分成功', consumeResult)
        }

        setTaskBatchId(data.data.taskBatchId)
        providerIdRef.current = data.provider.id
        pollTaskStatus(data.data.taskBatchId)
      } else {
        setIsGenerating(false)
        setProgress(0)
        setErrorMessage(data.msg || t('script.error'))
      }
    } catch (error) {
      console.error('Generate music error:', error)
      setIsGenerating(false)
      setProgress(0)
      setErrorMessage(t('script.error'))
    }
  }
  const isPremium = userInfo?.membershipStatus === 'active'
  const cooldownTime = isPremium ? COOLDOWN_TIME.PREMIUM : COOLDOWN_TIME.FREE

  const [lastCallTime2, setLastCallTime2] = useState(0)
  const [cooldownRemaining2, setCooldownRemaining2] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      const now = Date.now()
      const timeSinceLastCall = now - lastCallTime2
      if (timeSinceLastCall < cooldownTime) {
        setCooldownRemaining2(
          Math.ceil((cooldownTime - timeSinceLastCall) / 1000)
        )
      } else {
        setCooldownRemaining2(0)
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [lastCallTime2, cooldownTime])

  // 获取高度类名
  const getHeightClassName = () => {
    // 基础高度
    let baseHeight = ''

    if (generationMode === 'custom' || hasGeneratedLyrics) {
      baseHeight = textareaSize === 'large' ? 'h-96 sm:h-80' : 'h-48 sm:h-40'
    } else {
      baseHeight = textareaSize === 'large' ? 'h-96 sm:h-80' : 'h-40 sm:h-32'
    }

    return baseHeight
  }

  // 切换文本区域大小
  const toggleTextareaSize = () => {
    setTextareaSize(textareaSize === 'normal' ? 'large' : 'normal')
  }

  // 移动端简洁提示函数
  const showMobileSubscriptionPrompt = (message) => {
    // 创建模态框元素
    const modalContainer = document.createElement('div')
    modalContainer.className =
      'fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4'
    modalContainer.style.animation = 'fadeIn 0.3s ease-out'

    // 模态框内容
    modalContainer.innerHTML = `
    <div class="bg-[#1a1d24] border border-gray-800 rounded-xl p-6 max-w-md w-full shadow-xl" 
         style="animation: scaleIn 0.3s ease-out">
      <div class="flex flex-col">
        <div className="w-12 h-12 mx-auto mb-4 bg-purple-500/20 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-purple-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-white text-center mb-2">Need to upgrade</h3>
        <p class="text-gray-300 text-center mb-5">${message}</p>
        <button id="subscribeButton" 
                class="w-full py-3 px-4 rounded-lg font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-all">
          Subscribe immediately
        </button>
        <button id="closeButton"
                class="w-full py-3 px-4 rounded-lg font-medium bg-transparent text-gray-400 hover:text-white mt-3">
          Close
        </button>
      </div>
    </div>
  `

    // 添加样式
    const style = document.createElement('style')
    style.textContent = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    @keyframes scaleIn {
      from { transform: scale(0.95); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
  `
    document.head.appendChild(style)

    // 添加到DOM
    document.body.appendChild(modalContainer)
    document.body.style.overflow = 'hidden' // 防止背景滚动

    // 添加事件监听
    setTimeout(() => {
      const subscribeButton = document.getElementById('subscribeButton')
      const closeButton = document.getElementById('closeButton')

      subscribeButton.addEventListener('click', () => {
        window.open('/pricing', '_blank')
        document.body.removeChild(modalContainer)
        document.body.style.overflow = ''
      })

      closeButton.addEventListener('click', () => {
        document.body.removeChild(modalContainer)
        document.body.style.overflow = ''
      })

      // 点击背景关闭
      modalContainer.addEventListener('click', (e) => {
        if (e.target === modalContainer) {
          document.body.removeChild(modalContainer)
          document.body.style.overflow = ''
        }
      })
    }, 100)
  }

  // 生成歌词的函数
  const generateLyrics = async (userPrompt?: string) => {
    const promptToUse = userPrompt || lyrics
    if (!promptToUse.trim()) {
      setErrorMessage('Please enter the music description')
      return
    }

    if (!lyrics.trim()) {
      setErrorMessage('Please enter the music description')
      return
    }

    if (isGeneratingLyrics) {
      return
    }

    setIsGeneratingLyrics(true)

    const now = Date.now()
    if (now - lastCallTime2 >= cooldownTime) {
      setLastCallTime2(now)
    }

    try {
      const response = await fetch('/api/music/generate-song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description: lyrics,
          lang: locale,
        }),
      })

      setIsGeneratingLyrics(false)

      if (!response.ok) {
        throw new Error('Failed to generate song')
      }

      const data = await response.json()
      if (data.success === true) {
        setLyrics(data.song.lyrics)
        // @ts-ignore
        // textAreaRef.current?.focus()
      }

      return data
    } catch (error) {
      console.error('Error generating song:', error)
      setIsGeneratingLyrics(false)

      throw error
    }
  }

  // 重新生成歌词
  const regenerateLyrics = () => {
    generateLyrics(originalDescription)
  }

  // 轮询任务状态
  const pollTaskStatus = async (batchId: string) => {
    const checkStatus = async () => {
      try {
        const response = await fetch(
          `/api/music/status?taskBatchId=${batchId}&user_id=${getUserId()}&providerId=${
            providerIdRef.current
          }`
        )
        const data: TaskResponse = await response.json()

        if (data.code === 200 && data.data?.items[0]) {
          const item = data.data.items[0]

          setProgress((prevProgress) => {
            const increment = Math.floor(Math.random() * 5) + 2 // 5到10的随机数
            // 限制最大进度为80
            return Math.min(prevProgress + increment, 80)
          })

          if (item.progressMsg.includes('提交创作信息')) {
            setProgressMessage('Submit authoring information...')
          } else if (item.progressMsg.includes('正在生成歌曲')) {
            setProgressMessage('Submit authoring information')
          } else if (item.progressMsg.includes('创作音乐中')) {
            setProgressMessage('In creating music')
          } else {
            setProgressMessage(item.progressMsg)
          }

          if (item.cld2AudioUrl || item.status === 40) {
            //

            const blockUsers = [
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              // '<EMAIL>',
              '<EMAIL>',
              // '<EMAIL>',
            ]

            if (blockUsers.includes(user['email'])) {
              // 80% 的概率进入延迟逻辑
              // if (Math.random() < 0.8) {
              console.log('延迟成功')
              // 首次设置目标等待值
              if (targetWaitRef.current === 0) {
                targetWaitRef.current =
                  Math.floor(Math.random() * (900 - 100 + 1)) + 300
              }

              // 随机增加等待计数
              waitCountRef.current += Math.floor(Math.random() * 5) + 1

              // 如果未达到目标等待值，继续轮询
              if (waitCountRef.current < targetWaitRef.current) {
                return false // 继续轮询
              }

              // 重置计数器，为下次做准备
              waitCountRef.current = 0
              targetWaitRef.current = 0
            }
            // }

            setProgress(100)
            setIsGenerating(false)
            if (detectMobileDevice().isMobile) {
              window.location.href = `/ai-music-generator/${batchId}?myWorks`
            } else {
              router.push(`/ai-music-generator/${batchId}`)
            }

            return true
          }
        }
        return false
      } catch (error) {
        console.error('Poll status error:', error)
        return false
      }
    }

    const poll = async () => {
      if (await checkStatus()) return
      setTimeout(poll, 4000)
    }

    poll()
  }

  interface MusicCategory {
    label: string
    type: 'Style' | 'Moods' | 'Voices' | 'Tempos'
    styles: string[]
  }

  const musicCategories: MusicCategory[] =
    locale === 'zh'
      ? [
          {
            label: '风格（70+）',
            type: 'Style',
            styles: zhExamples.map((item) => item.name),
          },
          {
            label: '情绪',
            type: 'Moods',
            styles: [
              '欢快',
              '悲伤',
              '愤怒',
              '恐惧',
              '惊喜',
              '期待',
              '平静',
              '浪漫',
              '怀旧',
              '神秘',
              '胜利',
              '绝望',
            ],
          },
          {
            label: '声音',
            type: 'Voices',
            styles: [
              '女高音',
              '女中音',
              '男高音',
              '男低音',
              '童声',
              '男声',
              '女声',
            ],
          },
          {
            label: '速度',
            type: 'Tempos',
            styles: ['快板', '中板', '慢板', '80-120 BPM', '120-160 BPM'],
          },
        ]
      : [
          {
            label: `${t('style')}（70+）`,
            type: 'Style',
            styles: enExamples.map((item) => item.name),
          },
          {
            label: t('moods'),
            type: 'Moods',
            styles: [
              'Joy',
              'Sadness',
              'Anger',
              'Fear',
              'Surprise',
              'Anticipation',
              'Calmness',
              'Romantic',
              'Nostalgia',
              'Mystery',
              'Triumph',
              'Despair',
            ],
          },
          {
            label: t('voices'),
            type: 'Voices',
            styles: [
              t('soprano'),
              t('alto'),
              t('tenor'),
              t('bass'),
              t('childrensVoice'),
              t('maleVoice'),
              t('femaleVoice'),
            ],
          },
          {
            label: t('tempos'),
            type: 'Tempos',
            styles: ['Fast', 'Medium', 'Slow', '80-120 BPM', '120-160 BPM'],
          },
        ]

  const toggleStyle = (style: string) => {
    if (selectedStyles.includes(style)) {
      setSelectedStyles(selectedStyles.filter((s) => s !== style))
    } else {
      setSelectedStyles([...selectedStyles, style])
    }
  }

  const handleInputKeyDown = (e: any) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      if (!selectedStyles.includes(inputValue.trim())) {
        setSelectedStyles([...selectedStyles, inputValue.trim()])
      }
      setInputValue('')
      setIsEditing(false)
    } else if (e.key === 'Escape') {
      setInputValue('')
      setIsEditing(false)
    }
  }

  const removeStyle = (styleToRemove: string) => {
    setSelectedStyles(selectedStyles.filter((style) => style !== styleToRemove))
  }

  const generateRandomStyles = () => {
    const allStyles = musicCategories.flatMap((category) => category.styles)
    const shuffled = [...allStyles].sort(() => 0.5 - Math.random())
    const randomCount = Math.floor(Math.random() * (5 - 2 + 1)) + 2
    setSelectedStyles(shuffled.slice(0, randomCount))
  }

  // 切换模式时的处理
  const handleModeChange = (mode: 'custom' | 'description') => {
    setLyrics('')
    setGenerationMode(mode)
    setHasGeneratedLyrics(false)
    setOriginalDescription('')
  }

  const maxLength = 4000
  const minLength = 3
  const currentLength = lyrics.length

  const styles = locale === 'zh' ? zhExamples : enExamples
  const [audioPlaying, setAudioPlaying] = useState<string | null>(null)
  const [audioLoading, setAudioLoading] = useState<string | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const currentPathRef = useRef<string | null>(null)
  const pausedTimeRef = useRef<number>(0)

  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause()
      pausedTimeRef.current = audioRef.current.currentTime
    }
    setAudioPlaying(null)
  }

  // 新增一个重置函数
  const resetAudioOnTooltipClose = () => {
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
      currentPathRef.current = null
      pausedTimeRef.current = 0
      setAudioPlaying(null)
    }
  }

  const handlePlayPreview = async (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    path: string
  ) => {
    e.stopPropagation()

    // 如果点击当前正在播放的音频，则暂停
    if (audioPlaying === path) {
      stopAudio()
      return
    }

    // 如果是同一个音频的续播
    if (currentPathRef.current === path && audioRef.current) {
      audioRef.current.play()
      audioRef.current.currentTime = pausedTimeRef.current
      setAudioPlaying(path)
      return
    }

    try {
      // 停止当前播放的音频
      if (audioRef.current) {
        audioRef.current.pause()
        setAudioPlaying(null)
      }

      setAudioLoading(path)

      // 创建新的音频实例
      const audio = new Audio(path)
      audioRef.current = audio
      currentPathRef.current = path
      pausedTimeRef.current = 0

      const handleCanPlayThrough = () => {
        if (currentPathRef.current === path) {
          setAudioLoading(null)
          setAudioPlaying(path)
          audio.play().catch(console.error)
        }
      }

      const handleEnded = () => {
        if (currentPathRef.current === path) {
          setAudioPlaying(null)
          pausedTimeRef.current = 0
          currentPathRef.current = null
        }
      }

      const handleError = () => {
        setAudioLoading(null)
        setAudioPlaying(null)
        currentPathRef.current = null
        console.error('Audio failed to load')
      }

      audio.addEventListener('canplaythrough', handleCanPlayThrough, {
        once: true,
      })
      audio.addEventListener('ended', handleEnded)
      audio.addEventListener('error', handleError, { once: true })

      audio.load()
    } catch (error) {
      setAudioLoading(null)
      console.error('Error playing audio:', error)
    }
  }

  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current.src = ''
      }
      currentPathRef.current = null
      pausedTimeRef.current = 0
    }
  }, [])

  const isPaused = useCallback(
    (path: string) => {
      return (
        audioPlaying &&
        currentPathRef.current === path &&
        pausedTimeRef.current >= 0
      )
    },
    [audioPlaying]
  )

  const [suggestions, setSuggestions] = useState([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isGeneratingTitle, setIsGeneratingTitle] = useState(false)

  const [lastCallTime, setLastCallTime] = useState(0)
  const [cooldownRemaining, setCooldownRemaining] = useState(0)

  useEffect(() => {
    setSuggestions([])
    return () => {}
  }, [lyrics])

  useEffect(() => {
    const timer = setInterval(() => {
      const now = Date.now()
      const timeSinceLastCall = now - lastCallTime
      if (timeSinceLastCall < cooldownTime) {
        setCooldownRemaining(
          Math.ceil((cooldownTime - timeSinceLastCall) / 1000)
        )
      } else {
        setCooldownRemaining(0)
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [lastCallTime, cooldownTime])

  const generateSongTitle = async () => {
    // if (!lyrics || isGenerating) return
    setShowSuggestions(false)
    setIsGeneratingTitle(true)

    // 否则请求接口生成新标题
    const now = Date.now()
    if (now - lastCallTime >= cooldownTime) {
      setLastCallTime(now)
    }

    // 如果已有建议标题，直接从中随机选择
    if (suggestions && suggestions.length > 0) {
      const randomIndex = Math.floor(Math.random() * suggestions.length)
      setTitle(suggestions[randomIndex])
      setIsGeneratingTitle(false)

      // @ts-ignore
      inputRefTitle.current?.focus()

      toast.success(
        <div className="flex flex-col items-center gap-2">
          <span>✨ {t('generatedTitle')} </span>
          <span className="font-medium text-amber-500">
            {suggestions[randomIndex]}
          </span>
        </div>,
        {
          duration: 3000,
          position: 'top-center',
          icon: '🎵',
          style: {
            borderRadius: '10px',
            background: '#333',
            color: '#fff',
          },
        }
      )

      return
    }

    try {
      const response = await fetch('/api/music/generate-title', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: lyrics,
          lang: locale,
        }),
      })

      setIsGeneratingTitle(false)
      if (!response.ok) {
        throw new Error('Failed to generate song title')
      }

      const data = await response.json()
      if (data.success === true) {
        const titles = JSON.parse(data.title)
        setSuggestions(titles)
        const randomIndex = Math.floor(Math.random() * titles.length)
        setTitle(titles[randomIndex])
        // @ts-ignore
        inputRefTitle.current?.focus()

        toast.success(
          <div className="flex flex-col items-center gap-2">
            <span>✨ {t('generatedTitle')} </span>
            <span className="font-medium text-amber-500">
              {titles[randomIndex]}
            </span>
          </div>,
          {
            duration: 3000,
            position: 'top-center',
            icon: '🎵',
            style: {
              borderRadius: '10px',
              background: '#333',
              color: '#fff',
            },
          }
        )
      }

      return data
    } catch (error) {
      console.error('Error generating song title:', error)
      throw error
    }
  }

  const [isGeneratingDesc, setIsGeneratingDesc] = useState(false)
  const inputRefDesc = useRef(null)

  const [lastCallTime3, setLastCallTime3] = useState(0)
  const [cooldownRemaining3, setCooldownRemaining3] = useState(0)

  // 处理模型变更
  const handleModelChange = (model: string) => {
    console.log('Selected model:', model)
    setSongModel(model)

    // 在这里更新您的应用状态或执行其他操作
  }

  useEffect(() => {
    const timer = setInterval(() => {
      const now = Date.now()
      const timeSinceLastCall = now - lastCallTime3
      if (timeSinceLastCall < cooldownTime) {
        setCooldownRemaining3(
          Math.ceil((cooldownTime - timeSinceLastCall) / 1000)
        )
      } else {
        setCooldownRemaining3(0)
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [lastCallTime3, cooldownTime])

  const generateSongDesc = async () => {
    if (isGeneratingDesc) return

    setIsGeneratingDesc(true)

    // 冷却时间检查
    const now = Date.now()
    if (now - lastCallTime3 >= cooldownTime) {
      setLastCallTime3(now)
    }

    try {
      const response = await fetch('/api/music/generate-description', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: lyrics,
          lang: locale,
        }),
      })

      setIsGeneratingDesc(false)

      if (!response.ok) {
        throw new Error('Failed to generate description')
      }

      const data = await response.json()
      if (data.success === true) {
        console.log('faith=============data', data)
        setLyrics(data.description)
        // @ts-ignore
        inputRefDesc.current?.focus()

        toast.success(
          <div className="flex flex-col items-center gap-2">
            <span>✨ {t('aiGenerate')} </span>
            <span className="font-medium text-amber-500">
              {data.description}
            </span>
          </div>,
          {
            duration: 3000,
            position: 'top-center',
            icon: '🎼',
            style: {
              borderRadius: '10px',
              background: '#333',
              color: '#fff',
            },
          }
        )
      }

      return data
    } catch (error) {
      console.error('Error generating description:', error)
      setIsGeneratingDesc(false)

      throw error
    }
  }

  const handleApplyExample = (example) => {
    if (generationMode == 'description') {
      setLyrics(example.desc)
    } else {
      setLyrics(example.lyrics)
    }

    setSelectedStyles([example.style])

    setTitle(example.title)
  }

  return (
    <div className="container mx-auto px-0 box-border sm:px-4 pb-8">
      <div className="max-w-4xl mx-auto bg-gray-800/80 rounded-2xl p-4 md:p-6">
        {/* Mode Selection */}
        <div className="flex gap-4 mb-3 sm:mb-6">
          <button
            type="button"
            onClick={() => handleModeChange('custom')}
            className={`flex-1 py-3 rounded-lg transition-all ${
              generationMode === 'custom'
                ? 'bg-purple-500'
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
          >
            {t('customLyrics')}
          </button>
          <button
            type="button"
            onClick={() => handleModeChange('description')}
            className={`flex-1 py-3 rounded-lg transition-all ${
              generationMode === 'description'
                ? 'bg-purple-500'
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
          >
            {t('descriptionMode')}
          </button>
        </div>

        <div className="flex items-center justify-between w-full gap-4 px-1 mb-3">
          <div className="flex items-center justify-center">
            <div className="relative inline-flex items-center gap-3 px-5 py-2 bg-gray-700/30 rounded-full backdrop-blur-sm">
              <button
                onClick={() => setIsInstrumentalOnly(!isInstrumentalOnly)}
                className="relative w-11 h-5 rounded-full transition-colors duration-200"
                style={{
                  backgroundColor: isInstrumentalOnly ? '#8B5CF6' : '#374151',
                }}
                role="switch"
                aria-checked={isInstrumentalOnly}
                aria-label="Toggle instrumental mode"
              >
                <div
                  className={`
            absolute w-4 h-4 bg-white rounded-full top-0.5
            transition-transform duration-200 ease-out
            ${isInstrumentalOnly ? 'translate-x-6' : 'translate-x-0.5'}
          `}
                >
                  <div className="absolute inset-0 rounded-full bg-white/80 blur-[1px]" />
                </div>
              </button>
              <span
                onClick={() => setIsInstrumentalOnly(!isInstrumentalOnly)}
                className={`text-sm transition-colors duration-200 ${
                  isInstrumentalOnly ? 'text-purple-400' : 'text-gray-400'
                }`}
              >
                {t('instrumentalOnly')}
              </span>
            </div>
          </div>
        </div>

        <div className="relative mb-3">
          {!(isInstrumentalOnly && generationMode === 'description') && (
            <>
              <textarea
                ref={textareaRef}
                onChange={(e) => setLyrics(e.target.value)}
                value={lyrics}
                maxLength={generationMode === 'description' ? 200 : maxLength}
                className={`pb-8 [&::-webkit-scrollbar]:h-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-700 [&::-webkit-scrollbar-thumb]:rounded-full w-full transition-all duration-300 rounded-lg p-4 text-white resize-none bg-gray-800/40 backdrop-blur-sm border border-gray-600/30 focus:outline-none focus:border-purple-500/30 ${
                  generationMode === 'description' && hasGeneratedLyrics
                    ? 'bg-opacity-50'
                    : ''
                } ${getHeightClassName()}`}
                placeholder={
                  generationMode === 'custom'
                    ? t('enterYourLyrics1')
                    : hasGeneratedLyrics
                    ? t('generatedLyrics')
                    : t('describeDesiredSong2')
                }
              />

              {/* 右上角放大/缩小按钮 */}
              <button
                onClick={toggleTextareaSize}
                className="absolute top-2 right-2 p-1.5 bg-gray-700/60 hover:bg-gray-600/80 rounded-md text-gray-300 hover:text-white transition-colors"
                aria-label={textareaSize === 'large' ? 'Collapse' : 'Expand'}
              >
                {textareaSize === 'large' ? (
                  <Minimize2 size={12} />
                ) : (
                  <Maximize2 size={12} />
                )}
              </button>

              {/* 左下角按钮 */}
              {/* {generationMode === 'description' && !isInstrumentalOnly && ( */}
              {generationMode === 'custom' && !isInstrumentalOnly && (
                <TooltipButton
                  onClick={() => generateLyrics()}
                  isGenerating={isGeneratingLyrics}
                  cooldownRemaining={cooldownRemaining2}
                  disabled={currentLength < minLength}
                  loadingText={t('generating')}
                  buttonText={t('aiGenerate2')}
                  position="absolute bottom-4 left-3"
                  tooltipContent={
                    <div className="space-y-2">
                      <p className="text-amber-300 font-medium mt-3">
                        ⚡️ {t('aiLyricsDesc')}
                      </p>

                      <p className="text-sm text-gray-400  mb-6">
                        {t('magicPricing')}
                      </p>

                      {!isPremium && (
                        <div className="flex mt-3 justify-center">
                          <a
                            href="/pricing"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="ml-2 mt-3 text-amber-500 hover:text-amber-600 transition-colors duration-200"
                          >
                            {t('magicSubscribe')}
                          </a>
                        </div>
                      )}
                    </div>
                  }
                />
              )}

              {/*  */}
              {generationMode === 'description' && !isInstrumentalOnly && (
                <TooltipButton
                  onClick={generateSongDesc}
                  isGenerating={isGeneratingDesc}
                  cooldownRemaining={cooldownRemaining3}
                  disabled={currentLength < minLength}
                  loadingText={t('generating')}
                  buttonText={t('aiGenerate2')}
                  position="absolute bottom-4 left-3"
                  tooltipContent={
                    <div className="space-y-2">
                      <p className="text-amber-300 font-medium mt-3">
                        ⚡️ {t('aiDescIntro')}
                      </p>

                      <p className="text-sm text-gray-400  mb-6">
                        {t('magicPricing')}
                      </p>

                      {!isPremium && (
                        <div className="flex mt-3 justify-center">
                          <a
                            href="/pricing"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="ml-2 mt-3 text-amber-500 hover:text-amber-600 transition-colors duration-200"
                          >
                            {t('magicSubscribe')}
                          </a>
                        </div>
                      )}
                    </div>
                  }
                />
              )}

              {/* 示例按钮和弹出面板 */}
              {/* 示例按钮组 */}
              <div className="absolute bottom-4 right-20 flex gap-2">
                {songExamples
                  .slice(
                    0,
                    isMobile
                      ? 1
                      : pathname.includes('ai-music-generator')
                      ? 1
                      : 3
                  )
                  .map((example, index) => (
                    <button
                      key={index}
                      onClick={() => handleApplyExample(example)}
                      className="min-w-[64px] h-5 px-3 bg-gray-800 hover:bg-gray-700 
                   border border-gray-700 rounded-full
                   text-xs text-gray-300 transition-colors"
                    >
                      {isMobile ? 'Example' : example.title}
                    </button>
                  ))}
              </div>

              {/* 右下角字数限制 */}
              <div
                className={`absolute bottom-4 right-3 text-xs ${
                  currentLength >= maxLength
                    ? 'text-red-400/90'
                    : currentLength < minLength
                    ? 'text-amber-400/90'
                    : 'text-gray-400/90'
                }`}
              >
                {currentLength}/
                {generationMode === 'description' ? 200 : maxLength}
              </div>
            </>
          )}
        </div>
        {/* Style Selection */}
        <div className="mb-6 mt-2 p-4 rounded-xl border border-gray-700/50 bg-gray-800/20 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-4 text-[14px]">
            <div className="text-lg font-medium text-purple-500">
              {t('styles')}
            </div>
            <div className="flex gap-2">
              <button
                onClick={generateRandomStyles}
                className="flex items-center gap-2 px-4 py-1.5 bg-gray-700/50 rounded-lg hover:bg-gray-600/50 transition-colors"
              >
                ✨ {t('random')}
              </button>
            </div>
          </div>
          <div className="mb-2 mt-2  rounded-xl  border-gray-700/50 bg-gray-800/20 backdrop-blur-sm">
            {/* 风格输入框和标签展示区域 */}
            <div className="mb-4 p-2 min-h-[40px] rounded-lg bg-gray-900/50 border border-gray-700/50 focus-within:border-purple-500/50 transition-colors">
              <div className="flex flex-wrap gap-1.5 items-center">
                {selectedStyles.map((style) => (
                  <StyleTag
                    key={style}
                    text={style}
                    onRemove={() => removeStyle(style)}
                  />
                ))}
                {isEditing ? (
                  <input
                    ref={inputRef}
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleInputKeyDown}
                    onBlur={() => setIsEditing(false)}
                    className="w-[100px] bg-transparent text-xs text-white outline-none placeholder:text-gray-500 focus:ring-purple-500"
                    placeholder={t('enterStyle')}
                    autoFocus
                  />
                ) : (
                  <>
                    <button
                      onClick={() => setIsEditing(true)}
                      className="px-2 py-0.5 text-xs text-gray-400 hover:text-purple-400 transition-colors"
                    >
                      + {t('add')}
                    </button>
                    {selectedStyles.length === 0 && (
                      <span className="text-xs text-gray-500 ml-2 max-sm:text-left">
                        {t('enterSongStyle')}
                      </span>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* 分类标签 */}
            <div className="flex justify-between items-center">
              <div className="flex flex-nowrap gap-1.5 mb-3 overflow-x-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
                {musicCategories.map((category) => (
                  <button
                    key={category.type}
                    onClick={() => setActiveCategory(category.type)}
                    className={`px-2 py-0.5 text-xs font-medium rounded-md transition-all whitespace-nowrap flex-shrink-0 ${
                      activeCategory === category.type
                        ? 'bg-gray-900 text-white border border-gray-700'
                        : 'bg-transparent text-gray-400 border border-gray-700 hover:bg-gray-800'
                    }`}
                  >
                    # {category.label}
                  </button>
                ))}
              </div>
              {activeCategory === 'Style' && !isMobile && (
                <div className="flex items-center gap-2 text-purple-500 text-sm mb-3">
                  <Volume2 className="h-4 w-4" color="#AD58D8" />
                  {t('hoverToPreview')}
                </div>
              )}
            </div>
            {/* 风格选择器 */}
            {activeCategory === 'Style' ? (
              <div className="flex flex-wrap gap-1.5" id="demo">
                {styles.map((style, index) => {
                  const isHidden = !isExpanded && index >= 10
                  const requiresSubscription =
                    index >= 10 && membershipStatus !== 'active'

                  return (
                    <Tooltip.Provider delayDuration={200} key={style.name}>
                      <Tooltip.Root
                        onOpenChange={(open) => {
                          if (!open && audioPlaying === style.path) {
                            resetAudioOnTooltipClose()
                          }
                        }}
                      >
                        <Tooltip.Trigger asChild>
                          <button
                            onClick={() =>
                              !requiresSubscription && toggleStyle(style.name)
                            }
                            className={cn(
                              'px-2.5 py-0.5 text-xs rounded-full transition-all border',
                              selectedStyles.includes(style.name)
                                ? 'bg-purple-500/90 text-white border-purple-400'
                                : 'bg-gray-800/40 text-gray-400 hover:bg-gray-700/40 border-gray-600 hover:border-gray-500',
                              isHidden && 'hidden'
                            )}
                          >
                            {style.name}
                          </button>
                        </Tooltip.Trigger>
                        <Tooltip.Portal>
                          <Tooltip.Content
                            className={cn(
                              'w-96 p-4 rounded-xl relative',
                              'bg-[#1C1F26]/95 backdrop-blur-sm',
                              'shadow-xl shadow-[#9D5BF0]/10',
                              'border border-[#2A2D36]/50',
                              'animate-in fade-in-0 zoom-in-95 duration-200'
                            )}
                            side="top"
                            sideOffset={5}
                          >
                            {requiresSubscription && (
                              <>
                                <div className="absolute inset-0 bg-[#1C1F26]/85 backdrop-blur-sm rounded-lg z-10" />
                                <div className="absolute inset-0 flex items-center justify-center z-10">
                                  <div className="text-center px-6">
                                    <p className="text-sm font-medium text-[#E4E6EB] mb-3">
                                      {t('previewTip')}
                                    </p>
                                    <button
                                      onClick={() =>
                                        window.open('/pricing', '_blank')
                                      }
                                      className="px-6 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 rounded-full hover:opacity-90 transition-opacity"
                                    >
                                      {t('price.subscribeButton')}
                                    </button>
                                  </div>
                                </div>
                              </>
                            )}
                            <div className="space-y-3 relative">
                              <div className="flex items-start justify-between">
                                <div className="flex-1 min-w-0 pr-4">
                                  <h3 className="text-sm font-medium text-[#E4E6EB] mb-2">
                                    {style.name}
                                  </h3>
                                  <p className="text-xs leading-relaxed text-[#9DA3B4]">
                                    {style.description}
                                  </p>
                                </div>
                                <button
                                  onClick={(e) => {
                                    e.preventDefault()
                                    handlePlayPreview(e, style.path)
                                  }}
                                  disabled={audioLoading === style.path}
                                  className={cn(
                                    'flex-shrink-0 p-2 rounded-full transition-all',
                                    'bg-[#9D5BF0]/10 text-[#9D5BF0] hover:bg-[#9D5BF0]/20',
                                    audioLoading === style.path &&
                                      'cursor-wait',
                                    isPaused(style.path) &&
                                      'bg-yellow-500/10 text-yellow-500 hover:bg-yellow-500/20'
                                  )}
                                >
                                  {audioLoading === style.path ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : isPaused(style.path) ? (
                                    <Pause className="h-4 w-4" />
                                  ) : (
                                    <Play className="h-4 w-4" />
                                  )}
                                </button>
                              </div>
                            </div>
                            <Tooltip.Arrow className="fill-[#1C1F26]/95" />
                          </Tooltip.Content>
                        </Tooltip.Portal>
                      </Tooltip.Root>
                    </Tooltip.Provider>
                  )
                })}
                {styles.length > 10 && (
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="px-2.5 py-0.5 text-xs rounded-full bg-purple-500/10 text-purple-400 hover:bg-purple-500/20 transition-all"
                  >
                    {isExpanded ? `${t('showLess')} ↑` : `${t('showMore')} ↓`}
                  </button>
                )}
              </div>
            ) : (
              <div className="flex flex-wrap gap-1.5">
                {musicCategories
                  .find((c) => c.type === activeCategory)
                  ?.styles.map((style, index) => {
                    const isHidden = !isExpanded && index >= 10
                    const requiresSubscription = index >= 10

                    return (
                      <Tooltip.Provider delayDuration={200} key={style}>
                        <Tooltip.Root>
                          <Tooltip.Trigger asChild>
                            <button
                              onClick={() =>
                                !requiresSubscription && toggleStyle(style)
                              }
                              className={cn(
                                'px-2.5 py-0.5 text-xs rounded-full transition-all',
                                selectedStyles.includes(style)
                                  ? 'bg-purple-500/90 text-white'
                                  : 'bg-gray-800/40 text-gray-400 hover:bg-gray-700/40',
                                isHidden && 'hidden',
                                requiresSubscription && 'cursor-default'
                              )}
                            >
                              {style}
                            </button>
                          </Tooltip.Trigger>
                          {requiresSubscription && (
                            <Tooltip.Portal>
                              <Tooltip.Content
                                className={cn(
                                  'w-96 p-4 rounded-xl relative overflow-hidden',
                                  'bg-[#1C1F26]/95 backdrop-blur-sm',
                                  'shadow-xl shadow-[#9D5BF0]/10',
                                  'border border-[#2A2D36]/50',
                                  'animate-in fade-in-0 zoom-in-95 duration-200'
                                )}
                                side="top"
                                sideOffset={5}
                              >
                                <div className="space-y-3">
                                  <div className="relative">
                                    <div className="absolute inset-0 bg-purple-500/10 backdrop-blur-sm rounded-lg" />
                                    <div className="relative z-10 p-4 text-center">
                                      <p className="text-xs leading-relaxed text-[#9DA3B4] mb-4">
                                        {t('previewTip')}
                                      </p>
                                      <button
                                        onClick={() => {
                                          /* 处理订阅跳转 */
                                        }}
                                        className="px-4 py-2 text-sm font-medium text-white bg-purple-500 rounded-full hover:bg-purple-600 transition-colors"
                                      >
                                        {t('price.subscribeButton')}
                                      </button>
                                    </div>
                                  </div>
                                </div>
                                <Tooltip.Arrow className="fill-[#1C1F26]/95" />
                              </Tooltip.Content>
                            </Tooltip.Portal>
                          )}
                        </Tooltip.Root>
                      </Tooltip.Provider>
                    )
                  })}
                {musicCategories.find((c) => c.type === activeCategory)?.styles
                  .length! > 10 && (
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="px-2.5 py-0.5 text-xs rounded-full bg-purple-500/10 text-purple-400 hover:bg-purple-500/20 transition-all"
                  >
                    {isExpanded ? `${t('showLess')} ↑` : `${t('showMore')} ↓`}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
        {/* Title Input */}
        <div className="mb-6 space-y-4">
          <div className="flex items-center gap-4">
            <label className="text-white text-sm font-medium whitespace-nowrap">
              {t('title')}
            </label>
            <div className="relative flex-1">
              <input
                ref={inputRefTitle}
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value.slice(0, 30))}
                placeholder={t('enterSongTitle')}
                className="w-full px-4 py-2 bg-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 pr-16"
              />
              <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-400">
                {title.length}/30
              </div>
            </div>

            {!isMobile && (
              <Tooltip.Provider delayDuration={200}>
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={generateSongTitle}
                      disabled={
                        !lyrics || isGeneratingTitle || cooldownRemaining > 0
                      }
                      className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors
              ${
                lyrics && !isGeneratingTitle && cooldownRemaining <= 0
                  ? 'bg-purple-600 hover:bg-purple-700 text-white'
                  : 'bg-gray-700 text-gray-400 cursor-not-allowed'
              }`}
                    >
                      {isGeneratingTitle ? (
                        <img
                          src="/loading.gif"
                          alt="loading"
                          className="w-4 h-4"
                        />
                      ) : (
                        <Wand2 className="w-4 h-4" />
                      )}

                      {cooldownRemaining > 0
                        ? `Wait ${cooldownRemaining}s`
                        : t('magicTitle')}
                    </motion.button>
                  </Tooltip.Trigger>

                  <Tooltip.Portal>
                    <Tooltip.Content
                      className="data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade 
    data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade 
    select-none rounded-lg bg-[#1F2937]/95 px-4 py-3 text-sm leading-relaxed
    text-gray-100 shadow-xl max-w-[350px]"
                      sideOffset={5}
                    >
                      <div className="space-y-2">
                        <p className="text-amber-300 font-medium mt-3">
                          ⚡️ {t('headerTitle')}
                        </p>
                        <p className="text-gray-200">🤖 {t('magicSubtitle')}</p>
                        <div className="mt-3 space-y-1.5 text-gray-300 text-sm">
                          <p className="flex items-center gap-1.5">
                            🔍 {t('magicFeature1')}
                          </p>
                          <p className="flex items-center gap-1.5">
                            📈 {t('magicFeature2')}
                          </p>
                          <p className="flex items-center gap-1.5">
                            🎯 {t('magicFeature3')}
                          </p>
                        </div>
                        <p className="text-sm text-gray-400  mb-6">
                          ⏱ {t('magicPricing')}
                        </p>
                        {!isPremium && (
                          <div className="flex mt-3 justify-center">
                            <a
                              href="/pricing"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="ml-2 mt-3 text-amber-500 hover:text-amber-600 transition-colors duration-200"
                            >
                              {t('magicSubscribe')}
                            </a>
                          </div>
                        )}
                      </div>
                      <Tooltip.Arrow className="fill-[#1F2937]" />
                    </Tooltip.Content>
                  </Tooltip.Portal>
                </Tooltip.Root>
              </Tooltip.Provider>
            )}
          </div>
        </div>

        <ModelSelector
          isPremium={isPremium}
          onModelChange={handleModelChange}
          defaultModel={songModel}
        />

        {/* 错误提示 */}
        {errorMessage && (
          <div className="p-4 bg-red-500/20 border border-red-500 rounded-lg text-red-400 text-center">
            {errorMessage}
          </div>
        )}

        {/* 进度条 */}
        {isGenerating && (
          <ProgressBar progress={progress} progressMessage={progressMessage} />
        )}

        {/* Generate Button */}
        <button
          onClick={generateMusic}
          className="w-full mt-4 bg-gradient-to-r from-purple-500 to-pink-500 py-4 rounded-lg font-medium hover:opacity-90 transition-opacity"
          style={{ cursor: `url(${mofabang.src}), auto` }}
        >
          {t('generateMusic')}
        </button>

        <p className="md:text-base text-xs text-purple-400 mt-4 mb-2">
          {t('contentWarning')}
        </p>

        {showLoginModal && (
          <ShowLoginModal
            title={t('loginTipsTitle')}
            desc={t('tipLogin')}
            onClose={() => setShowLoginModal(false)}
          />
        )}

        {/* 白撸的用户只能等待 8 ~ 15 */}
        {showQueueModal && (
          <QueueModal
            locale={locale}
            isMobile={isMobile}
            // waitTimeInMinutes={Math.floor(Math.random() * (4 - 2 + 1)) + 2}
            // waitTimeInMinutes={Math.random() * (1 - 0.5) + 0.5}
            //介于 0.7 到 1.5
            waitTimeInMinutes={
              // 如果是俄罗斯用户，需要等待更长的时间
              Math.random() * (0.4 - 0.2) + 0.2
              // locale === 'ru'
              //   ? Math.random() * (20 - 7.5) + 7.5
              //   : Math.random() * (0.4 - 0.2) + 0.2
            }
            // waitTimeInMinutes={Math.random() * (0.3 - 0.1) + 0.1}
            isOpen={showQueueModal}
            generateMusic={generateMusic}
            onClose={() => setShowQueueModal(false)}
          />
        )}
      </div>

      <AnimatePresence>
        {showToast && <Toast message={toastMessage} />}
      </AnimatePresence>

      <Dialog
        open={!!payDialogTitle}
        onOpenChange={(open) =>
          setPayDialogTitle((title) => (open ? title : ''))
        }
      >
        <DialogContent
          className="
          max-w-[1200px] 
          w-full 
          h-[920px]
          bg-[#1A1B1E] 
          border-[#2D2E32]
          shadow-xl
        "
        >
          <DialogHeader>
            <DialogTitle className="text-white">
              {t('insufficientCredits')}
            </DialogTitle>
          </DialogHeader>
          <div className="w-full bg-red-500/10 border border-red-500/20 rounded-xl p-4">
            <p className="text-center font-semibold text-xl text-purple-300">
              <span className="text-red-500">{payDialogTitle}</span>
            </p>
          </div>
          <PricingSection needTitle={true} className="leading-none" />
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default Index
