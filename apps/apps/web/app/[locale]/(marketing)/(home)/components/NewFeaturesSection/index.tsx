import React from 'react'
import Link from 'next/link'
import { getTranslations } from 'next-intl/server'

// 静态图标组件替代Lucide图标
const MusicIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="h-6 w-6 text-purple-400"
  >
    <path d="M9 18V5l12-2v13"></path>
    <circle cx="6" cy="18" r="3"></circle>
    <circle cx="18" cy="16" r="3"></circle>
  </svg>
)

const RefreshIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="h-6 w-6 text-pink-400"
  >
    <path d="M3 2v6h6"></path>
    <path d="M21 12A9 9 0 0 0 6 5.3L3 8"></path>
    <path d="M21 22v-6h-6"></path>
    <path d="M3 12a9 9 0 0 0 15 6.7l3-2.7"></path>
  </svg>
)

const ZapIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="h-6 w-6 text-amber-400"
  >
    <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
  </svg>
)

const SparklesIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="h-6 w-6 text-blue-400"
  >
    <path d="M12 3v.59l.59.59.58-.59L12 2.41z"></path>
    <path d="m18 9-.59.59.59.58.59-.59-.59-.58z"></path>
    <path d="m6 9 .59.59-.59.58-.59-.59.59-.58z"></path>
    <path d="m12 18 .59-.59-.59-.58-.58.59.58.58z"></path>
    <path d="m14.5 9.5-5 5"></path>
    <path d="M3 21a7.24 7.24 0 0 1 5.5-7"></path>
    <path d="M15.5 13a7.23 7.23 0 0 1 5.5 7"></path>
    <path d="M9 7.2a7.23 7.23 0 0 1-2-5.2"></path>
    <path d="M15 7.2a7.23 7.23 0 0 0 2-5.2"></path>
  </svg>
)

const NewFeaturesSection = async () => {
  const t = await getTranslations()
  // 特性数据
  const features = [
    {
      id: 'extended-songs',
      icon: <MusicIcon />,
      title: t('extended8MinuteSongs'),
      description: t('createCompleteCompositions'),
      color: 'bg-purple-900/30',
      border: 'border-purple-500/20',
      hover: 'hover:border-purple-500/40',
    },
    {
      id: 'multi-style',
      icon: <RefreshIcon />,
      title: t('multiStyleConversionFeature'),
      description: t('transformYourSong'),
      color: 'bg-pink-900/30',
      border: 'border-pink-500/20',
      hover: 'hover:border-pink-500/40',
    },
    {
      id: 'smart-prompts',
      icon: <ZapIcon />,
      title: t('smarterPromptUnderstandingFeature'),
      description: t('aiNowBetterCaptures'),
      color: 'bg-amber-900/30',
      border: 'border-amber-500/20',
      hover: 'hover:border-amber-500/40',
    },
    {
      id: 'style-mixing',
      icon: <SparklesIcon />,
      title: t('clearStyleMixingFeature'),
      description: t('preciselyControlElements'),
      color: 'bg-blue-900/30',
      border: 'border-blue-500/20',
      hover: 'hover:border-blue-500/40',
    },
  ]

  // 结构化数据 - 使用Schema.org 29.1版本的最新标准
  const structuredDataString = JSON.stringify({
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: 'AIMakeSong',
    applicationCategory: 'MultimediaApplication',
    applicationSubCategory: 'MusicComposition',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    softwareVersion: '2.0',
    releaseNotes: t('script.releaseNotes'),
    description: t('script.description'),
    featureList: [
      t('script.feature1'),
      t('script.feature2'),
      t('script.feature3'),
      t('script.feature4'),
    ],
    potentialAction: {
      '@type': 'UseAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: 'https://aimakesong.com/create',
      },
    },
  })

  // FAQ结构化数据 - 使用Schema.org的官方FAQ格式
  const faqStructuredDataString = JSON.stringify({
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: [
      {
        '@type': 'Question',
        name: t('whatsNewInAIMakeSong2'),
        acceptedAnswer: {
          '@type': 'Answer',
          text: t('aimakesong2IntroducesFull'),
        },
      },
      {
        '@type': 'Question',
        name: t('doINeedToPayForAccess'),
        acceptedAnswer: {
          '@type': 'Answer',
          text: t('yesAIMakeSong2IsAvailable'),
        },
      },
      {
        '@type': 'Question',
        name: t('howMuchLongerAreSongs'),
        acceptedAnswer: {
          '@type': 'Answer',
          text: t('aimakesong2CanGenerate'),
        },
      },
    ],
  })

  return (
    <section
      id="aimakesong-features"
      className="py-16 lg:py-24 overflow-hidden relative"
    >
      {/* 背景元素 */}
      <div className="absolute inset-0 opacity-30 overflow-hidden">
        <div className="absolute h-64 w-64 rounded-full bg-purple-900/30 blur-3xl -top-10 -left-20" />
        <div className="absolute h-64 w-64 rounded-full bg-pink-900/20 blur-3xl bottom-10 right-10" />
      </div>

      <div className="container mx-auto px-4 relative">
        {/* 标题区域 - 使用符合Schema.org的产品名称和版本 */}
        <div className="text-center mb-12 md:mb-16">
          <span className="inline-block px-3 py-1 text-xs font-semibold bg-purple-900/30 text-purple-400 rounded-full mb-3">
            {t('newVersion')}
          </span>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            <span className="text-purple-500">
              {t('introducingAIMakeSong2')}
            </span>
          </h2>
          <p className="text-gray-300 max-w-2xl mx-auto text-lg">
            {t('mostAdvancedTechYet')}
          </p>
        </div>

        {/* 特性网格 - 使用语义化HTML增强SEO */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature) => (
            <div
              key={feature.id}
              className={`bg-gray-800/50 rounded-xl p-6 border ${feature.border} transition-all duration-300 hover:-translate-y-1 ${feature.hover}`}
            >
              <div
                className={`w-12 h-12 ${feature.color} rounded-lg flex items-center justify-center mb-4`}
              >
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-400">{feature.description}</p>
            </div>
          ))}
        </div>

        {/* CTA 区域 */}
        <div className="mt-12 text-center">
          <Link
            href="/ai-music-generator"
            className="inline-block px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20"
          >
            {t('tryAIMakeSong2Now')}
          </Link>
          <p className="mt-4 text-gray-500 text-sm">
            {t('premiumFeatureSubscribeOnce')}
          </p>
        </div>

        {/* FAQ 部分 - 针对Schema.org的FAQPage结构化数据优化 */}
        <div className="mt-16 max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold text-white mb-6 text-center">
            {t('frequentlyAskedQuestions')}
          </h2>

          <div className="divide-y divide-gray-800">
            <div className="py-4">
              <h3 className="font-medium text-white mb-2">
                {t('whatsNewInAIMakeSong2')}
              </h3>
              <p className="text-gray-400">{t('aimakesong2IntroducesFull')}</p>
            </div>

            <div className="py-4">
              <h3 className="font-medium text-white mb-2">
                {t('doINeedToPayForAccess')}
              </h3>
              <p className="text-gray-400">{t('yesAIMakeSong2IsAvailable')}</p>
            </div>

            <div className="py-4">
              <h3 className="font-medium text-white mb-2">
                {t('howMuchLongerAreSongs')}
              </h3>
              <p className="text-gray-400">{t('aimakesong2CanGenerate')}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 软件应用结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: structuredDataString }}
      />

      {/* FAQ结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: faqStructuredDataString }}
      />
    </section>
  )
}

export default NewFeaturesSection
