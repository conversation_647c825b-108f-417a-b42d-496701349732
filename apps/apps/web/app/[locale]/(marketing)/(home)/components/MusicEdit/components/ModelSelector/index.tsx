import React, { useState, useEffect } from 'react'
import { Lock, X, Sparkles, HelpCircle } from 'lucide-react'
import { motion } from 'framer-motion'
import * as Dialog from '@radix-ui/react-dialog'
import * as Tooltip from '@radix-ui/react-tooltip'
import { useTranslations } from 'next-intl'
//

type ModelSelectorProps = {
  isPremium: boolean
  onModelChange: (model: string) => void
  defaultModel?: string
}

// Star component
const Star = ({
  delay,
  size,
  top,
  left,
}: {
  delay: number
  size: number
  top: string
  left: string
}) => {
  return (
    <motion.div
      className="absolute text-amber-300"
      style={{ top, left }}
      initial={{ opacity: 0, scale: 0 }}
      animate={{
        opacity: [0, 1, 0],
        scale: [0, 1, 0],
      }}
      transition={{
        duration: 1.5,
        delay,
        repeat: Infinity,
        repeatDelay: Math.random() * 3 + 1,
      }}
    >
      <Sparkles size={size} />
    </motion.div>
  )
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  isPremium,
  onModelChange,
  defaultModel = 'aimakesong1.0',
}) => {
  const t = useTranslations()
  const [selectedModel, setSelectedModel] = useState<string>(defaultModel)
  const [upgradeModalOpen, setUpgradeModalOpen] = useState<boolean>(false)
  const [mobileTooltipOpen, setMobileTooltipOpen] = useState<boolean>(false)

  const handleModelChange = (model: string) => {
    if (model === 'aimakesong2.0' && !isPremium) {
      setUpgradeModalOpen(true)
      return
    }

    setSelectedModel(model)
    onModelChange(model)
  }

  useEffect(() => {
    setSelectedModel(defaultModel)
    handleModelChange(defaultModel)
  }, [defaultModel])

  return (
    <>
      <div className="mb-3 space-y-2">
        <div className="flex items-center gap-3">
          <label className="text-white text-xs font-medium whitespace-nowrap">
            {t('model')}
          </label>
          <div className="relative flex-1">
            <div className="flex bg-gray-700 rounded-lg overflow-hidden h-8">
              <button
                onClick={() => handleModelChange('aimakesong1.0')}
                className={`flex-1 py-1 px-3 text-xs transition-colors ${
                  selectedModel === 'aimakesong1.0'
                    ? 'bg-gradient-to-r from-purple-600 to-pink-500 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                AIMakeSong 1.0
              </button>

              <Tooltip.Provider delayDuration={200}>
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <div className="relative flex-1">
                      {/* Star animations, only shown for non-premium users */}
                      {!isPremium && (
                        <>
                          <Star delay={0.1} size={9} top="15%" left="10%" />
                          <Star delay={0.5} size={8} top="50%" left="20%" />
                          <Star delay={0.3} size={10} top="20%" left="80%" />
                          <Star delay={0.7} size={7} top="60%" left="90%" />
                          <Star delay={0.2} size={8} top="70%" left="50%" />
                        </>
                      )}
                      <button
                        onClick={() => handleModelChange('aimakesong2.0')}
                        className={`w-full h-full py-1 px-3 text-xs transition-colors flex items-center justify-center gap-1.5 ${
                          selectedModel === 'aimakesong2.0'
                            ? 'bg-gradient-to-r from-purple-600 to-pink-500 text-white'
                            : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        }`}
                      >
                        AIMakeSong 2.0
                        {!isPremium && <Lock className="w-3 h-3" />}
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            setMobileTooltipOpen(true)
                          }}
                          className={`md:hidden ml-1 ${
                            selectedModel === 'aimakesong2.0'
                              ? 'text-white'
                              : 'text-gray-400 hover:text-white'
                          }`}
                        >
                          <HelpCircle className="w-3 h-3" />
                        </button>
                      </button>
                    </div>
                  </Tooltip.Trigger>

                  <Tooltip.Portal>
                    <Tooltip.Content
                      className="hidden md:block data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade 
                      data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade 
                      select-none rounded-lg bg-[#1F2937]/95 px-3 py-2 text-xs leading-relaxed z-20
                      text-gray-100 shadow-xl max-w-[300px]"
                      sideOffset={5}
                    >
                      <div className="space-y-1.5">
                        <p className="text-amber-300 font-medium">
                          ⭐️ AIMakeSong 2.0
                        </p>
                        <p className="text-gray-200 text-xs">
                          {t('advancedMusicModel')}
                        </p>
                        <div className="mt-1.5 space-y-1 text-gray-300 text-xs">
                          <p className="flex items-center gap-1">
                            {t('extended8MinMusicGen')}
                          </p>
                          <p className="flex items-center gap-1">
                            {t('singleSongMultiStyleConv')}
                          </p>
                          <p className="flex items-center gap-1">
                            {t('smarterPromptUnderstand')}
                          </p>
                          <p className="flex items-center gap-1">
                            {t('clearerStyleMix')}
                          </p>
                        </div>
                      </div>
                      <Tooltip.Arrow className="fill-[#1F2937]" />
                    </Tooltip.Content>
                  </Tooltip.Portal>
                </Tooltip.Root>
              </Tooltip.Provider>
            </div>
          </div>
        </div>
      </div>

      {/* 移动端特性弹窗 */}
      <Dialog.Root open={mobileTooltipOpen} onOpenChange={setMobileTooltipOpen}>
        <Dialog.Portal>
          <Dialog.Overlay className="md:hidden bg-black/60 fixed inset-0 z-50 backdrop-blur-sm" />
          <Dialog.Content className="md:hidden fixed z-50 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-sm bg-gradient-to-b from-gray-900 to-gray-800 rounded-xl p-4 shadow-xl border border-purple-500/20">
            <Dialog.Title className="sr-only">
              AIMakeSong 2.0 特性说明
            </Dialog.Title>
            <div className="relative">
              <Dialog.Close className="absolute right-1 top-1 text-gray-400 hover:text-white">
                <X className="w-4 h-4" />
              </Dialog.Close>

              <div className="space-y-3">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-amber-300 font-medium text-base">
                    ⭐️ AIMakeSong 2.0
                  </h3>
                </div>

                <div className="space-y-2">
                  <div className="bg-purple-900/20 p-2 rounded-lg border border-purple-500/20">
                    <p className="text-gray-200 text-xs">
                      {t('advancedMusicModel')}
                    </p>
                  </div>

                  <div className="space-y-1.5">
                    <div className="flex items-center gap-2 text-gray-300 text-xs">
                      <span>{t('extended8MinMusicGen')}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-300 text-xs">
                      <span>{t('singleSongMultiStyleConv')}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-300 text-xs">
                      <span>{t('smarterPromptUnderstand')}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-300 text-xs">
                      <span>{t('clearerStyleMix')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>

      {/* Upgrade Modal */}
      <Dialog.Root open={upgradeModalOpen} onOpenChange={setUpgradeModalOpen}>
        <Dialog.Portal>
          <Dialog.Overlay className="bg-black/60 fixed inset-0 z-50 backdrop-blur-sm" />
          <Dialog.Content className="fixed z-50 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-md max-h-[85vh] overflow-auto bg-gray-900 rounded-xl p-4 shadow-xl border border-gray-800 sm:p-5">
            <div className="relative">
              <Dialog.Close className="absolute right-1 top-1 text-gray-400 hover:text-white">
                <X className="w-4 h-4 sm:w-5 sm:h-5" />
              </Dialog.Close>

              <h2 className="text-lg sm:text-xl font-bold text-white mb-2 pr-6">
                {t('upgradeNeeded')}
              </h2>
              <p className="text-gray-300 mb-3 sm:mb-4 text-xs sm:text-sm">
                {t('upgradeToV2')}
              </p>

              <div className="space-y-3 mb-4">
                <div className="bg-purple-900/30 p-2.5 sm:p-3 rounded-lg border border-purple-500/30">
                  <h3 className="text-amber-300 font-medium text-sm sm:text-base">
                    ✨ {t('aiMakeSong2Features')}
                  </h3>
                  <ul className="mt-1.5 space-y-1.5 text-gray-200 text-xs sm:text-sm">
                    <li className="flex items-start gap-2">
                      <span className="text-amber-400 mt-0.5">⏱️</span>
                      <span>{t('extended8MinMusic')}</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-amber-400 mt-0.5">🔄</span>
                      <span>{t('multiStyleConversion')}</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-amber-400 mt-0.5">🧠</span>
                      <span>{t('smarterPromptUnderstanding')}</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-amber-400 mt-0.5">🎨</span>
                      <span>{t('clearerStyleMixing')}</span>
                    </li>
                  </ul>
                </div>

                {/* Lifetime Privilege Notice */}
                <div className="bg-gradient-to-r from-amber-900/30 to-yellow-900/30 p-2.5 sm:p-3 rounded-lg border border-amber-500/30">
                  <h3 className="text-amber-300 font-medium text-sm sm:text-base flex items-center gap-1">
                    <Sparkles className="w-3.5 h-3.5 sm:w-4 sm:h-4" />{' '}
                    {t('lifetimePrivilege')}
                  </h3>
                  <p className="mt-1 text-amber-200/90 text-xs sm:text-sm">
                    {t('subscribeOncePermanentAccess')}
                  </p>
                </div>
              </div>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => (window.location.href = '/pricing')}
                className="w-full py-2 sm:py-2.5 bg-gradient-to-r from-purple-600 to-pink-500 rounded-lg text-white text-xs sm:text-sm font-medium hover:opacity-90 transition-opacity"
              >
                {t('upgradePlan')}
              </motion.button>

              <div className="mt-3 space-y-1 text-xs">
                <p className="text-green-400">{t('cancelAnytime')}</p>
                <p className="text-green-400">{t('unusedCreditsRollOver')}</p>
              </div>
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </>
  )
}

export default ModelSelector
