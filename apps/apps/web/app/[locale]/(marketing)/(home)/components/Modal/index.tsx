import React from "react";
import ReactDOM from "react-dom";
import { useTranslations } from "next-intl";


type ModalProps = {
  visible: boolean;
  title: React.ReactNode;
  children: React.ReactNode;
  onCancel?: () => void;
  onConfirm?: () => void | Promise<void>;
  cancelText?: React.ReactNode;
  confirmText?: React.ReactNode;
}
function Modal(props: ModalProps) {
  const t = useTranslations();
  const { visible, title, children, onCancel, onConfirm, cancelText, confirmText } = props
  if (!visible) return null;
  return ReactDOM.createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-96">
        {/* Header */}
        <div className="px-6 py-4 border-b flex justify-between items-center">
          <h3 className="transition-colors duration-200 text-purple-400 text-lg font-semibold">{title}</h3>
          <button
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            ×
          </button>
        </div>
        {/* Content */}
        <div className="px-6 py-4 text-purple-600 text-lg font-semibold">{children}</div>
        {/* Footer */}
        <div className="px-6 py-4 border-t flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
          >
            {cancelText || t('common.confirmation.cancel')}
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2transition-all bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            {confirmText || 'Confirm'}
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
}

export default Modal;