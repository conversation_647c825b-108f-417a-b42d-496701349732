'use client'

import { NETWORK_CONFIGS, useWeb3 } from '@/hooks/useWeb3'
import { ethers } from 'ethers'
import { AlertCircle, CheckCircle, Wallet, ExternalLink } from 'lucide-react'
import { useState, useCallback, useEffect } from 'react'
import type { PaymentProps, TransactionState } from './types'
import { useTranslations } from 'next-intl'

const USDT_ABI = [
  'function transfer(address recipient, uint256 amount) external returns (bool)',
  'function balanceOf(address account) external view returns (uint256)',
]

const CryptoPayment: React.FC<PaymentProps> = ({
  amount,
  network = 56,
  receiverAddress,
  onSuccess,
  onError,
  onBeforePayment,
}) => {
  const t = useTranslations()
  const {
    active,
    account,
    library,
    connect,
    loading: connectLoading,
    chainId,
    error: web3Error,
  } = useWeb3()

  const handleRefresh = async () => {
    console.log('faith=============更新了')
    setIsRefreshing(true)
    await fetchBalance()
    // 确保动画至少运行 3 秒
    setTimeout(() => {
      setIsRefreshing(false)
    }, 3000)
  }

  const [isRefreshing, setIsRefreshing] = useState(false)
  const [balance, setBalance] = useState<string>('0')
  const [txState, setTxState] = useState<TransactionState>({
    loading: false,
    error: '',
    txHash: '',
    success: false,
  })

  const fetchBalance = useCallback(async () => {
    if (!active || !account) return

    try {
      const provider = new ethers.providers.Web3Provider(window.ethereum)
      const tokenConfig =
        NETWORK_CONFIGS[network as keyof typeof NETWORK_CONFIGS]
      const tokenContract = new ethers.Contract(
        tokenConfig.usdt,
        USDT_ABI,
        provider
      )

      const balance = await tokenContract.balanceOf(account)
      const formattedBalance = ethers.utils.formatUnits(
        balance,
        tokenConfig.decimals
      )
      console.log('faith=============更新了', formattedBalance)
      setBalance(formattedBalance)
    } catch (error) {
      console.error('Error fetching balance:', error)
    }
  }, [active, account, network])

  useEffect(() => {
    fetchBalance()
  }, [fetchBalance])

  const updateTxState = useCallback((newState: Partial<TransactionState>) => {
    setTxState((prev) => ({ ...prev, ...newState }))
  }, [])

  const handlePayment = async () => {
    if (!active || !account) {
      updateTxState({ error: t('connectWalletError') })
      return
    }

    try {
      if (chainId !== network) {
        updateTxState({ loading: true, error: '', success: false })
        if (onBeforePayment) {
          const canProceed = await onBeforePayment()
          if (!canProceed) {
            updateTxState({
              error: t('switchNetworkError', {
                network:
                  NETWORK_CONFIGS[network as keyof typeof NETWORK_CONFIGS].name,
              }),
              loading: false,
            })
            return
          }
        }
      }

      const provider = new ethers.providers.Web3Provider(window.ethereum)
      const signer = provider.getSigner()

      const tokenConfig =
        NETWORK_CONFIGS[network as keyof typeof NETWORK_CONFIGS]
      const tokenContract = new ethers.Contract(
        tokenConfig.usdt,
        USDT_ABI,
        signer
      )

      const balance = await tokenContract.balanceOf(account)
      const amountInWei = ethers.utils.parseUnits(
        amount.toString(),
        tokenConfig.decimals
      )
      const userBalance = ethers.utils.formatUnits(
        balance,
        tokenConfig.decimals
      )

      if (balance.lt(amountInWei)) {
        throw new Error(
          t('insufficientBalance', {
            available: Number(userBalance).toFixed(2),
            required: amount,
          })
        )
      }

      const tx = await tokenContract.transfer(receiverAddress, amountInWei)
      updateTxState({ txHash: tx.hash })

      await tx.wait(1)

      updateTxState({ success: true, loading: false })
      onSuccess?.(tx.hash)
      await fetchBalance() // 更新余额
    } catch (err: any) {
      const errorMessage = err.message || 'Transaction failed'
      updateTxState({ error: errorMessage, loading: false })
      onError?.(errorMessage)
    }
  }

  const getExplorerLink = () => {
    const config = NETWORK_CONFIGS[network as keyof typeof NETWORK_CONFIGS]
    return `${config.explorer}/tx/${txState.txHash}`
  }

  return (
    <div className="w-full max-w-md mx-auto bg-white rounded-xl shadow-xl p-6 space-y-5">
      <div className="flex items-center justify-between border-b border-gray-100 pb-4">
        <h2 className="text-xl font-bold text-gray-900">
          {t('paymentTitle')}{' '}
          <span className="text-blue-600">
            {NETWORK_CONFIGS[network as keyof typeof NETWORK_CONFIGS].name}
          </span>
        </h2>
        {account && (
          <span className="text-sm bg-gray-100 px-3 py-1 rounded-full text-gray-700 font-medium">
            {account.slice(0, 6)}...{account.slice(-4)}
          </span>
        )}
      </div>

      <div className="bg-gray-50 rounded-xl p-6 border border-gray-100">
        <div className="text-3xl font-bold text-center text-gray-900">
          {amount} USDT
        </div>
        {active && (
          <div className="text-sm text-gray-600 text-center mt-2 font-medium flex justify-center items-center">
            {t('balance')} {parseFloat(balance).toFixed(2)} USDT{' '}
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="ml-2 p-1 hover:bg-gray-200 rounded-full transition-all inline-flex items-center"
              title={t('refreshBalance')}
            >
              <svg
                className={`w-4 h-4 text-gray-600 transition-transform ${
                  isRefreshing ? 'animate-spin duration-3000' : ''
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </button>
          </div>
        )}
      </div>

      {txState.error && (
        <div className="flex items-center gap-3 text-red-600 bg-red-50 p-4 rounded-xl border border-red-100">
          <AlertCircle className="w-5 h-5 flex-shrink-0" />
          <span className="text-sm font-medium">{txState.error}</span>
        </div>
      )}

      {txState.success && (
        <div className="flex items-center gap-3 text-green-600 bg-green-50 p-4 rounded-xl border border-green-100">
          <CheckCircle className="w-5 h-5 flex-shrink-0" />
          <span className="text-sm font-medium">{t('paymentCompleted')}</span>
        </div>
      )}

      {!active ? (
        <button
          onClick={connect}
          className="w-full flex items-center justify-center gap-2 bg-blue-600 text-white py-4 px-6 rounded-xl hover:bg-blue-700 transition-colors font-semibold text-lg"
          disabled={connectLoading || txState.loading}
        >
          <Wallet className="w-5 h-5" />
          {connectLoading ? t('connecting') : t('connectWallet')}
        </button>
      ) : (
        <button
          onClick={handlePayment}
          disabled={txState.loading}
          className="w-full bg-green-600 text-white py-4 px-6 rounded-xl hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-semibold text-lg"
        >
          {txState.loading ? t('processing') : t('pay')}
        </button>
      )}

      <a
        href="https://t.me/+QOBPgQ6EEy42ODY9"
        target="_blank"
        rel="noopener noreferrer"
        className="block mt-4 text-center text-blue-500 hover:text-blue-700 font-medium"
      >
        {t('telegramHelp')}
      </a>

      {txState.txHash && (
        <a
          href={getExplorerLink()}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center justify-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-800 mt-4"
        >
          {t('viewTransaction')}
          <ExternalLink className="w-4 h-4" />
        </a>
      )}

      <div className="text-center text-sm text-gray-500 mt-6 pt-4 border-t border-gray-100">
        {t('balanceWarning')}
      </div>
    </div>
  )
}

export default CryptoPayment
