import { useTranslations } from 'next-intl'
import { Clock, Globe, Music, Sparkles, Wand, Mic2 } from 'lucide-react'
import Link from 'next/link'
import AudioPlayer from '../HomeSongFeatures/AudioPlayer'

export default function WhyUs() {
  const t = useTranslations()
  const features = [
    {
      icon: Clock,
      title: t('saveTimeEffort'),
      descriptions: [
        t('useMusicWithoutLicenseWorry'),
        t('randomSongGenerator'),
        t('aiLyricsGenerator'),
      ],
    },
    {
      icon: Globe,
      title: t('multipleLanguages'),
      descriptions: [t('createMultiLangMusic'), t('whetherYouAreMaking')],
    },
    {
      icon: Sparkles,
      title: t('roundTheClockSupport'),
      descriptions: [t('get247Help'), t('musicMakerTools')],
    },
    {
      icon: Music,
      title: t('aiLyricsGen.title'),
      descriptions: [t('lyricsBuilder'), t('aiLyricsHelp')],
    },
    {
      icon: Wand,
      title: t('vocalRemover'),
      descriptions: [t('extractVocals'), t('createKaraoke'), t('aiPowered')],
    },
    {
      icon: Mic2,
      title: t('whyusInstrumentalOnly'),
      descriptions: [t('coverSongs'), t('useOurTools')],
    },
  ]

  return (
    <section className="py-16">
      <div className="max-w-7xl mx-auto px-4 space-y-24">
        {/* Header & Features */}
        <div>
          <h2 className="text-4xl md:text-5xl font-bold text-center text-purple-500 mb-4">
            {t('whyChooseOurAIMusicGen')}
          </h2>
          <p className="text-gray-400 max-w-5xl mx-auto mb-16 text-center text-lg md:text-xl">
            {t('aiMusicIntro')}
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="p-6 bg-gray-800/50 rounded-xl backdrop-blur-sm"
              >
                <feature.icon className="w-12 h-12 text-purple-400 mb-4" />
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <ul className="text-gray-400 space-y-2">
                  {feature.descriptions.map((item) => (
                    <li className="flex items-start gap-2" key={item}>
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-2"></span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Tools Section */}
        <div className="space-y-24">
          <div className="text-center space-y-3">
            <h2 className="text-4xl font-bold text-purple-500 !leading-normal">
              {t('newToolForSongMakers')}
            </h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              {t('aiPoweredMusicTools')}
            </p>
          </div>

          {/* Vocal Remover */}
          <div className="grid md:grid-cols-2 gap-16 items-center rounded-2xl bg-gradient-to-b from-gray-800/30 to-transparent p-8">
            <div className="space-y-8">
              <div className="space-y-4">
                <h3 className="text-3xl font-bold text-white">
                  <span className="text-purple-500">
                    {t('vocalRemoverAndSplitter')}
                  </span>
                </h3>
                <ul className="space-y-6 text-gray-300">
                  <li className="flex items-start gap-4">
                    <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0">
                      <span className="text-purple-400">•</span>
                    </div>
                    <span>{t('splitAfterGen')}</span>
                  </li>
                  <li className="flex items-start gap-4">
                    <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0">
                      <span className="text-purple-400">•</span>
                    </div>
                    <span>{t('importAndSeparate')}</span>
                  </li>
                </ul>
              </div>
              <Link href="/ai-vocal-remover" target="_blank">
                <button className="bg-gradient-to-r mt-8 from-purple-500 to-pink-500 text-white px-8 py-3 rounded-full text-lg font-medium hover:opacity-90 transition-opacity">
                  {t('tryVocalRemover')}
                </button>
              </Link>
            </div>
            <AudioPlayer />
          </div>

          {/* Instrumental */}
          <div className="grid md:grid-cols-2 gap-16 items-center rounded-2xl bg-gradient-to-b from-gray-800/30 to-transparent p-8">
            <div className="bg-gray-800/50 rounded-2xl h-[360px] overflow-hidden backdrop-blur-sm border border-gray-700/50">
              <img
                src="/feature_3.png"
                alt="Instrumental Music"
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
              />
            </div>
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="inline-block px-4 py-2 rounded-xl bg-purple-500/10 border border-purple-500/20">
                  <span className="text-purple-400 font-medium">
                    {t('instrumentalGen')}
                  </span>
                </div>
                <h3 className="text-3xl font-bold text-white">
                  {t('createInstrumental')}
                </h3>
                <p className="text-gray-300">{t('moodMatchInst')}</p>
                <div className="space-y-6 mt-6">
                  <ul className="space-y-4 text-gray-300">
                    <li className="flex items-start gap-4">
                      <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0">
                        <span className="text-purple-400">1</span>
                      </div>
                      <span>{t('simpleModeInst')}</span>
                    </li>
                    <li className="flex items-start gap-4">
                      <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0">
                        <span className="text-purple-400">2</span>
                      </div>
                      <span>{t('customModeInst')}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Lyrics Generator */}
          <div className="grid md:grid-cols-2 gap-16 items-center rounded-2xl bg-gradient-to-b from-gray-800/30 to-transparent p-8">
            <div className="space-y-8">
              <div className="space-y-4">
                <h3 className="text-3xl font-bold text-white">
                  <span className="text-purple-500">
                    {t('genAILyricsOneClick')}
                  </span>
                </h3>
                <p className="text-gray-300">{t('shareForLyrics')}</p>
              </div>
              <Link href="/ai-music-generator" target="_blank">
                <button className="bg-gradient-to-r mt-8 from-purple-500 to-pink-500 text-white px-8 py-3 rounded-full text-lg font-medium hover:opacity-90 transition-opacity">
                  {t('generateNow')}
                </button>
              </Link>
            </div>
            <div className="bg-gray-800/50 rounded-2xl h-[360px] overflow-hidden backdrop-blur-sm border border-gray-700/50">
              <img
                src="/feature_2.png"
                alt="Audio Visualization"
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
