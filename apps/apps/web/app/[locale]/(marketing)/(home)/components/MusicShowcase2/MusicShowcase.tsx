// components/MusicShowcase.jsx (服务端组件)
// import { getMusicData } from '@/lib/data' // 假设这是获取音乐数据的函数
//

import ClientMusicPlayer from './components/ClientMusicPlayer'
import { useTranslations } from 'next-intl'

interface MusicShowcaseProps {
  isInMusicGenerator?: boolean // 新增属性，表示是否在音乐生成器页面中使用
}

function MusicShowcase({ isInMusicGenerator = false }: MusicShowcaseProps) {
  const t = useTranslations()
  const musicData = [
    {
      title: 'The Holy Spirit',
      genre: 'Rasta rap, soulful',
      plays: '234.8K · 4.8★', // 使用中点 · 和星号 ★ 分隔
      duration: '3:42',
      image: 'song_1.jpeg',
      audioUrl: '11',
    },
    {
      title: 'Cerros',
      genre: 'Electronic, jazz',
      plays: '156.2K · 4.5★',
      duration: '4:15',
      image: 'song_2.jpeg',
      audioUrl: '22',
    },
    {
      title: 'Lost in the now',
      genre: 'EDM, Guajira',
      plays: '328.1K · 4.7★',
      duration: '3:58',
      image: 'song_3.jpeg',
      audioUrl: '33',
    },
    {
      title: 'Ton PERE',
      genre: 'House French touch',
      plays: '198.4K · 4.3★',
      duration: '4:23',
      image: 'song_4.jpeg',
      audioUrl: '44',
    },
    {
      title: 'Wir sind die Welt 2',
      genre: 'Pop, World',
      plays: '415.9K · 4.9★',
      duration: '3:36',
      image: 'song_5.jpeg',
      audioUrl: '55',
    },
    {
      title: 'Te echo de menos',
      genre: 'Bachata, Latin',
      plays: '267.3K · 4.4★',
      duration: '4:07',
      image: 'song_6.jpeg',
      audioUrl: '66',
    },
    {
      title: 'Terr#ne',
      genre: 'Nu jazz, Acoustic',
      plays: '143.7K · 4.6★',
      duration: '3:51',
      image: 'song_7.jpeg',
      audioUrl: '77',
    },
    {
      title: 'Πληγωμένες ψυχές',
      genre: 'Instrumental',
      plays: '289.5K · 4.8★',
      duration: '4:31',
      image: 'song_8.jpeg',
      audioUrl: '88',
    },
    {
      title: 'The Gift of Me',
      genre:
        'Delicate hesitant Slow Raw dark old school glissando synth modulation drone, solo, breakbeat,',
      plays: '176.2K · 4.5★',
      duration: '3:47',
      image: 'song_9.jpeg',
      audioUrl: 'The Gift of Me',
    },
    {
      title: 'Here To Love',
      genre: 'Wild vibrant introspective Pop punk sad',
      plays: '394.6K · 4.7★',
      duration: '4:12',
      image: 'song_10.jpg',
      audioUrl: 'Here To Love',
    },
    {
      title: 'Welcome to Earth',
      genre:
        'french house, Hip hop R&b, Electronic synths, 80s retro, fast paced',
      plays: '245.8K · 4.6★',
      duration: '3:55',
      image: 'song_11.jpg',
      audioUrl: 'Welcome to Earth',
    },
    {
      title: 'District K Dreams',
      genre:
        'vinahouse, vietnamese, oriental, electronic house, fast, high bpm, energetic',
      plays: '312.4K · 4.8★',
      duration: '4:18',
      image: 'song_12.jpg',
      audioUrl: 'x12',
    },
  ]
  return (
    <div className="text-white py-16 px-4">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-4xl md:text-5xl text-center font-bold text-purple-500 mb-4">
          {t('communityTitle')}
        </h2>
        <p className="text-gray-400 mb-10 text-lg md:text-xl text-center">
          {t('exploreDesc')}
        </p>

        <ClientMusicPlayer
          musicData={musicData}
          isInMusicGenerator={isInMusicGenerator}
        />
      </div>
    </div>
  )
}

export default MusicShowcase
