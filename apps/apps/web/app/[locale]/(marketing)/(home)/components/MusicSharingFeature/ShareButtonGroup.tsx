// ShareButtonGroup.tsx
'use client'
import React, { useState } from 'react'
import Link from 'next/link'
import {
  Share1Icon, // 用于分享按钮
  InstagramLogoIcon, // Instagram图标
  Cross2Icon, // 用于Reddit，因为Radix没有Reddit图标，这是一个替代方案
} from '@radix-ui/react-icons'
import { useTranslations } from 'next-intl'

interface ShareButtonGroupProps {
  className?: string
}

const ShareButtonGroup: React.FC<ShareButtonGroupProps> = ({
  className = '',
}) => {
  const t = useTranslations()
  const [showModal, setShowModal] = useState(false)

  const handleShareClick = () => {
    setShowModal(true)
  }

  const closeModal = () => {
    setShowModal(false)
    window.location.href = '/pricing' // 导航到购买页面
  }

  return (
    <>
      <div className={`flex flex-wrap justify-center gap-2 mt-4 ${className}`}>
        <button
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 transition-colors rounded-full text-white text-sm flex items-center"
          onClick={handleShareClick}
        >
          <Share1Icon className="w-4 h-4 mr-1.5" />
          {t('share.share')}
        </button>

        <button
          className="px-4 py-2 transition-colors rounded-full text-white text-sm flex items-center bg-white/90 hover:bg-white"
          onClick={handleShareClick}
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-black"
          >
            <path
              d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
              fill="currentColor"
            ></path>
          </svg>
        </button>

        <button
          className="px-4 py-2 bg-gradient-to-r from-amber-500 to-pink-500 hover:from-amber-600 hover:to-pink-600 transition-colors rounded-full text-white text-sm flex items-center"
          onClick={handleShareClick}
        >
          <InstagramLogoIcon className="w-4 h-4 mr-1.5" />
          Instagram
        </button>

        <button
          className="px-4 py-2 bg-orange-600 hover:bg-orange-700 transition-colors rounded-full text-white text-sm flex items-center"
          onClick={handleShareClick}
        >
          {/* 由于Radix UI没有Reddit图标，我们使用自定义SVG */}
          <svg
            className="w-4 h-4 mr-1.5"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z" />
          </svg>
          Reddit
        </button>
      </div>

      {/* 分享提示弹窗 */}
      {showModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full relative shadow-2xl border border-purple-500/30">
            <button
              onClick={closeModal}
              className="absolute right-4 top-4 text-gray-400 hover:text-white"
            >
              <Cross2Icon className="w-5 h-5" />
            </button>

            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 text-purple-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">
                {t('share.shareFeaturePrompt')}
              </h3>
            </div>

            <div className="text-gray-300 space-y-4">
              <p>
                {t('share.firstStep')}
                <Link href="/ai-music-generator">
                  <span className="text-purple-400 hover:text-purple-300 font-medium mx-1 underline underline-offset-2">
                    {t('share.secondStep')}
                  </span>
                </Link>
                {t('share.thirdStep')}
              </p>
              <p>
                <span className="text-purple-400 font-semibold">
                  {t('share.pleaseNote')}
                </span>{' '}
                {t('share.subscribersOnly')}
              </p>
            </div>

            <div className="mt-8 flex gap-3 justify-center">
              <button
                onClick={closeModal}
                className="px-5 py-2.5 bg-gray-700 hover:bg-gray-600 rounded-lg text-white font-medium"
              >
                {t('share.understood')}
              </button>
              <button
                onClick={closeModal}
                className="px-5 py-2.5 bg-gradient-to-r from-purple-600 to-pink-600 hover:opacity-90 rounded-lg text-white font-medium"
              >
                {t('share.upgradeSubscription')}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default ShareButtonGroup
