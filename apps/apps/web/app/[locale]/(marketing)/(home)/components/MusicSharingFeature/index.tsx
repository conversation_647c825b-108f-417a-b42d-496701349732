// MusicSharingFeature.jsx
// 一个可复用的React组件，展示AI音乐分享功能
// 使用方法: import MusicSharingFeature from './components/MusicSharingFeature';

import React from 'react'
import ShareButtonGroup from './ShareButtonGroup'
import { getTranslations } from 'next-intl/server'

const MusicSharingFeature = async () => {
  const t = await getTranslations()
  return (
    <div className="music-sharing-feature py-12 pb-32 px-4 ">
      <div className="max-w-6xl mx-auto">
        {/* 标题部分 */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-3 text-white">
            <span className="text-purple-500">{t('share.shareAiMusic')}</span>
          </h2>
          <h3 className="text-xl md:text-2xl font-semibold text-gray-200 mb-2">
            {t('share.letWorldHear')}
          </h3>
          <p className="text-gray-300 max-w-3xl mx-auto">
            {t('share.musicShareFeature')}
          </p>
        </div>

        {/* 内容区域 */}
        <div className="flex flex-col lg:flex-row items-center gap-10">
          {/* 左侧演示界面 */}
          <div className="lg:w-1/2 relative">
            <div className="bg-gradient-to-r from-purple-900/20 to-pink-900/20 p-6 rounded-xl border border-gray-800/50 shadow-lg">
              {/* 音乐卡片 */}
              <div className="bg-gray-800/90 rounded-lg overflow-hidden shadow-lg p-5">
                {/* 音乐信息 */}
                <div className="flex items-start gap-4">
                  {/* 封面 */}
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-10 w-10 text-white/90"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z" />
                    </svg>
                  </div>

                  {/* 音乐详情 */}
                  <div className="flex-1">
                    <h4 className="text-white font-medium text-lg">
                      Cosmic Dreams
                    </h4>
                    <p className="text-gray-400 text-sm">
                      Electronic • Ambient
                    </p>
                    <div className="flex items-center mt-1.5">
                      <div className="flex items-center">
                        <svg
                          className="w-4 h-4 text-amber-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <span className="ml-1 text-amber-400 text-sm">4.8</span>
                      </div>
                      <span className="text-gray-500 text-xs mx-2">•</span>
                      <span className="text-gray-500 text-xs">
                        2,345 {t('playVolume')}
                      </span>
                    </div>
                  </div>
                </div>

                {/* 音频可视化区域 */}
                <div className="mt-6 mb-4">
                  <div className="h-20 w-full">
                    <svg viewBox="0 0 100 40" className="w-full h-full">
                      <defs>
                        <linearGradient
                          id="waveGradient"
                          x1="0%"
                          y1="0%"
                          x2="0%"
                          y2="100%"
                        >
                          <stop offset="0%" stopColor="#ed64a6" />
                          <stop offset="100%" stopColor="#9f7aea" />
                        </linearGradient>
                      </defs>
                      <path
                        d="M0,20 Q12.5,5 25,20 Q37.5,35 50,20 Q62.5,5 75,20 Q87.5,35 100,20"
                        stroke="url(#waveGradient)"
                        strokeWidth="2"
                        fill="none"
                      />
                    </svg>
                  </div>
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>0:00</span>
                    <span>1:45</span>
                    <span>3:30</span>
                  </div>
                </div>

                {/* 分享按钮组 */}
                <ShareButtonGroup />

                {/* 分享链接 */}
                {/* <div className="mt-5 flex items-center bg-gray-700/50 rounded-lg overflow-hidden">
                  <input
                    type="text"
                    value="https://aimakesong.com/share/cosmic-dreams"
                    readOnly
                    className="flex-1 bg-transparent border-0 text-gray-300 text-sm py-2.5 px-4 focus:outline-none"
                  />
                  <button className="bg-purple-600 hover:bg-purple-700 transition-colors py-2.5 px-4 text-white text-sm font-medium">
                    复制
                  </button>
                </div> */}
              </div>
            </div>
          </div>

          {/* 右侧内容：特性介绍 */}
          <div className="lg:w-1/2">
            <div className="space-y-8">
              {/* 一键分享功能 */}
              <div className="flex">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-600/20 flex items-center justify-center mr-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-purple-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">
                    {t('shareFeatures.oneClickShare')}
                  </h4>
                  <p className="text-gray-300">
                    {t('shareFeatures.oneClickDesc')}
                  </p>
                </div>
              </div>

              {/* 专属分享链接 */}
              <div className="flex">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-600/20 flex items-center justify-center mr-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-purple-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">
                    {t('shareFeatures.uniqueLink')}
                  </h4>
                  <p className="text-gray-300">
                    {t('shareFeatures.uniqueLinkDesc')}
                  </p>
                </div>
              </div>

              {/* 自定义分享卡片 */}
              <div className="flex">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-600/20 flex items-center justify-center mr-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-purple-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">
                    {t('shareFeatures.customCard')}
                  </h4>
                  <p className="text-gray-300">
                    {t('shareFeatures.customCardDesc')}
                  </p>
                </div>
              </div>

              {/* 二维码生成 */}
              <div className="flex">
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-600/20 flex items-center justify-center mr-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-purple-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">
                    {t('shareFeatures.qrCode')}
                  </h4>
                  <p className="text-gray-300">
                    {t('shareFeatures.qrCodeDesc')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MusicSharingFeature
