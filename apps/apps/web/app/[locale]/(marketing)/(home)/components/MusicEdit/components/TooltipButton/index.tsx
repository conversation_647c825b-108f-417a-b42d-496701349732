// components/TooltipButton.tsx
import React from 'react'
import * as Tooltip from '@radix-ui/react-tooltip'
import { motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'

interface TooltipButtonProps {
  onClick: () => void
  isGenerating: boolean
  cooldownRemaining: number
  disabled: boolean
  loadingText?: string
  buttonText?: string
  tooltipContent: React.ReactNode
  className?: string
  position?: string
  icon?: React.ComponentType<{ className?: string }> // 改为可选属性
}

const TooltipButton = ({
  onClick,
  isGenerating,
  cooldownRemaining,
  disabled,
  loadingText = 'Generating...',
  buttonText = 'Generate',
  tooltipContent,
  className = '',
  position = 'absolute bottom-4 left-3',
  icon: Icon, // 可选的 Icon 组件
}: TooltipButtonProps) => {
  const isDisabled = isGenerating || disabled || cooldownRemaining > 0

  return (
    <div className={position}>
      <Tooltip.Provider delayDuration={200}>
        <Tooltip.Root>
          <Tooltip.Trigger asChild>
            <motion.button
              whileHover={{ scale: isDisabled ? 1 : 1.05 }}
              whileTap={{ scale: isDisabled ? 1 : 0.95 }}
              onClick={onClick}
              disabled={isDisabled}
              className={`group px-3 py-1 ${
                !isDisabled
                  ? 'hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 hover:text-white hover:border-transparent'
                  : ''
              } rounded-full 
                transition-all duration-300 flex items-center gap-1.5 text-xs backdrop-blur-sm 
                border border-gray-500/30 disabled:opacity-40 disabled:cursor-not-allowed 
                disabled:hover:bg-transparent ${
                  isDisabled
                    ? 'bg-gray-900/70 backdrop-blur-md border-gray-500 shadow-md text-gray-300'
                    : 'bg-gray-800/40 shadow-sm'
                } ${className}`}
            >
              {isGenerating ? (
                <>
                  {loadingText}
                  <Loader2 className="w-3 h-3 animate-spin" />
                </>
              ) : cooldownRemaining > 0 ? (
                `Wait ${cooldownRemaining}s`
              ) : (
                <>
                  {buttonText} {Icon && <Icon className="w-4 h-4" />} ✨
                </>
              )}
            </motion.button>
          </Tooltip.Trigger>

          <Tooltip.Portal>
            <Tooltip.Content
              className="data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade 
                data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade 
                select-none rounded-lg bg-[#1F2937]/95 px-4 py-3 text-sm leading-relaxed
                text-gray-100 shadow-xl max-w-[350px]"
              sideOffset={5}
            >
              {tooltipContent}
              <Tooltip.Arrow className="fill-[#1F2937]" />
            </Tooltip.Content>
          </Tooltip.Portal>
        </Tooltip.Root>
      </Tooltip.Provider>
    </div>
  )
}

export default TooltipButton
