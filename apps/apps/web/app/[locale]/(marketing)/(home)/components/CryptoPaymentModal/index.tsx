import { LucideIcon, Copy } from 'lucide-react'
import { useState } from 'react'

interface CryptoPaymentModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  IconComponent: LucideIcon
  price: string
}

const CryptoPaymentModal: React.FC<CryptoPaymentModalProps> = ({
  isOpen,
  onClose,
  title,
  email,
  IconComponent,
  price,
}) => {
  const [copiedWallet, setCopiedWallet] = useState(false)
  const [copiedEmail, setCopiedEmail] = useState(false)
  const [copiedTemplate, setCopiedTemplate] = useState(false)
  const [userWallet, setUserWallet] = useState('')
  const [userEmail, setUserEmail] = useState('')
  const [paymentAmount, setPaymentAmount] = useState('')

  // 单一钱包地址
  const walletAddress = 'TF2fakQ6fKJhqpbG7DbQbxDm1uB3NbJqu1' // USDT TRC20 地址示例
  const supportEmail = '<EMAIL>'

  const copyToClipboard = (
    text: string,
    type: 'wallet' | 'email' | 'template'
  ) => {
    navigator.clipboard.writeText(text)

    if (type === 'wallet') {
      setCopiedWallet(true)
      setTimeout(() => setCopiedWallet(false), 2000)
    } else if (type === 'email') {
      setCopiedEmail(true)
      setTimeout(() => setCopiedEmail(false), 2000)
    } else if (type === 'template') {
      setCopiedTemplate(true)
      setTimeout(() => setCopiedTemplate(false), 2000)
    }
  }

  const getEmailTemplate = () => {
    return `Моя почта: ${userEmail || '[пожалуйста, укажите вашу почту]'}
Оплатил(а) ${paymentAmount || price} USDT
Мой адрес кошелька: ${
      userWallet || '[пожалуйста, укажите адрес вашего кошелька]'
    }`
  }

  const generateQRCode = (address: string) => {
    // 这是一个占位符 - 实际应用中，你会生成真正的二维码
    return (
      <div className="w-full h-full bg-white p-2 rounded-lg">
        <div className="w-full h-full border-2 border-gray-900 flex items-center justify-center">
          <div className="text-black text-xs text-center">
            QR Code for:
            <br />
            {address.substring(0, 10)}...{address.substring(address.length - 8)}
          </div>
        </div>
      </div>
    )
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-[#1a1d24] border border-gray-800 rounded-xl p-6 md:p-8 w-full md:min-w-[900px] max-w-xs sm:max-w-md md:max-w-5xl mx-4 overflow-y-auto max-h-[90vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mb-4">
            <IconComponent className="w-8 h-8 text-purple-400" />
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">
            Оплата криптовалютой
          </h3>

          {/* PC 和移动端不同的布局 */}
          <div className="w-full flex flex-col md:flex-row md:gap-8 md:items-start">
            {/* 左侧：说明文字和表单 (移动端在上方) */}
            <div className="mb-6 md:mb-0 md:flex-1">
              <p className="text-gray-300 text-center md:text-left mb-6">
                Уважаемые пользователи из России, к сожалению, из-за некоторых
                политических факторов, платежные платформы, с которыми мы
                сотрудничаем, не поддерживают платежные системы вашей страны. Но
                aimakesong считает, что каждый пользователь заслуживает уважения
                и равного отношения. Мы не можем изменить ситуацию в мире, но
                можем предоставить вам возможность пользоваться нашим продуктом.
              </p>
              <p className="text-gray-300 text-center md:text-left mb-6">
                Вы можете отправить эквивалентную сумму в USDT (TRC20) на
                указанный ниже адрес. После успешного перевода отправьте
                подтверждающее письмо на официальную почту, и мы обработаем ваш
                заказ в кратчайшие сроки.
              </p>

              <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3 mb-6">
                <p className="text-yellow-400 text-sm">
                  Важное примечание: При оплате убедитесь, что отправляемая
                  сумма эквивалентна {price} (небольшие колебания допустимы).
                  После оплаты обязательно свяжитесь с нашей службой поддержки и
                  отправьте необходимую информацию по электронной почте.
                </p>
              </div>

              {/* 邮件信息表单和收款钱包地址 */}
              <div className="bg-[#121218] border border-gray-800 rounded-xl p-4 mb-4">
                <h4 className="text-lg font-semibold text-purple-400 mb-3 text-center md:text-left">
                  Платежная информация
                </h4>

                <div className="mb-4 border-b border-gray-700 pb-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-gray-300 text-sm">
                      Адрес кошелька получателя (USDT-TRC20)
                    </div>
                    <button
                      onClick={() => copyToClipboard(walletAddress, 'wallet')}
                      className="flex items-center gap-1 text-purple-400 hover:text-purple-300 bg-[#1a1d24] px-2 py-1 rounded"
                    >
                      <Copy className="w-4 h-4" />
                      <span className="text-sm">
                        {copiedWallet ? 'Скопировано' : 'Копировать'}
                      </span>
                    </button>
                  </div>
                  <div className="text-gray-300 text-sm break-all font-mono">
                    {walletAddress}
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <label className="text-gray-300 text-sm block mb-1">
                      Ваш email
                    </label>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setUserEmail(e.target.value)}
                      className="w-full bg-[#1a1d24] border border-gray-700 rounded-lg p-2 text-white text-sm"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="text-gray-300 text-sm block mb-1">
                      Сумма оплаты (USDT): {price}
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧：二维码和邮件模板 (移动端在下方) */}
            <div className="md:flex-1">
              <div className="bg-[#121218] border border-gray-800 rounded-xl p-4 mb-4">
                <h4 className="text-lg font-semibold text-purple-400 mb-3 text-center md:text-left">
                  QR-код для оплаты (USDT-TRC20)
                </h4>
                <div className="w-48 h-48 md:w-64 md:h-64 mx-auto">
                  <img src="/trc20.jpg" alt="" className="w-full h-full" />
                </div>
              </div>

              <div className="bg-[#121218] border border-gray-800 rounded-xl p-4">
                <h4 className="text-lg font-semibold text-purple-400 mb-3 text-center md:text-left">
                  Информация для подтверждения по email
                </h4>

                <div className="flex items-center justify-between mb-3 border-b border-gray-700 pb-3">
                  <div className="text-gray-300 text-sm">Email получателя</div>
                  <div className="flex items-center gap-2">
                    <div className="text-gray-300 text-sm">{supportEmail}</div>
                    <button
                      onClick={() => copyToClipboard(supportEmail, 'email')}
                      className="flex items-center gap-1 text-purple-400 hover:text-purple-300 bg-[#1a1d24] px-2 py-1 rounded"
                    >
                      <Copy className="w-4 h-4" />
                      <span className="text-sm">
                        {copiedEmail ? 'Скопировано' : 'Копировать'}
                      </span>
                    </button>
                  </div>
                </div>

                <div className="mb-3">
                  <div className="text-gray-300 text-sm mb-2">
                    Шаблон письма
                  </div>
                  <div className="bg-[#1a1d24] border border-gray-700 rounded-lg p-3 text-gray-300 text-sm font-mono whitespace-pre-line min-h-[100px]">
                    {getEmailTemplate()}
                  </div>
                </div>

                <button
                  onClick={() =>
                    copyToClipboard(getEmailTemplate(), 'template')
                  }
                  className="w-full flex items-center justify-center gap-2 text-purple-400 hover:text-purple-300 bg-[#242731] border border-gray-700 px-4 py-2 rounded-lg"
                >
                  <Copy className="w-4 h-4" />
                  <span>
                    {copiedTemplate
                      ? 'Содержимое скопировано'
                      : 'Копировать содержимое письма'}
                  </span>
                </button>

                <div className="mt-4 text-gray-400 text-sm">
                  <p>Порядок действий:</p>
                  <ol className="list-decimal pl-5 mt-1 space-y-1">
                    <li>После завершения оплаты скопируйте email получателя</li>
                    <li>
                      Откройте ваш почтовый клиент и вставьте адрес получателя
                    </li>
                    <li>
                      Скопируйте содержимое письма и вставьте в текст сообщения
                    </li>
                    <li>
                      Отправьте письмо, и мы обработаем ваш заказ как можно
                      скорее (минимум 1 час, максимум 12 часов)
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 w-full mt-6">
            <button
              onClick={onClose}
              className="w-full py-3 px-4 rounded-lg font-medium bg-gray-800 text-white hover:bg-gray-700 transition-colors"
            >
              Закрыть
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CryptoPaymentModal
