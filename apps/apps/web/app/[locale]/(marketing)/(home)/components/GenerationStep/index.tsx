import React from 'react'
import Link from 'next/link'
import { useTranslations } from 'next-intl'

//
export default function MusicGenerationSteps() {
  const t = useTranslations()
  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-16">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold text-purple-500 mb-4">
          {t('howToUse')}
        </h2>
        <p className="text-gray-400 max-w-4xl mx-auto text-lg md:text-xl">
          {t('aiDescription')}
        </p>
        <Link href="/ai-music-generator" target="_blank">
          <button className="mt-8 px-8 py-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium text-lg hover:opacity-90 transition-opacity">
            {t('createMusicBtn')}
          </button>
        </Link>
      </div>

      <div className="grid md:grid-cols-2 gap-12 items-stretch">
        <div className="space-y-8 flex flex-col justify-between">
          <div className="bg-gray-900/50 rounded-xl p-6 backdrop-blur border border-gray-800/50 hover:border-purple-500/50 transition-colors">
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white flex items-center justify-center font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold text-purple-400">
                {t('chooseInput')}
              </h3>
            </div>
            <p className="text-gray-300 mb-2">{t('selectGenerate')}</p>
            <ul className="text-gray-400 space-y-2 list-disc">
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full flex-shrink-0 bg-gradient-to-r from-purple-500 to-pink-500"></span>
                <span>{t('text2music')}</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full flex-shrink-0 bg-gradient-to-r from-purple-500 to-pink-500"></span>
                <span>{t('lyricsToMusic')}</span>
              </li>
            </ul>
          </div>

          <div className="bg-gray-900/50 rounded-xl p-6 backdrop-blur border border-gray-800/50 hover:border-purple-500/50 transition-colors">
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white flex items-center justify-center font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold text-purple-400">
                {t('selectVoiceStyle')}
              </h3>
            </div>
            <p className="text-gray-300 mb-2">{t('customizeDesc')}</p>
            <ul className="text-gray-400 space-y-2">
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full flex-shrink-0 bg-gradient-to-r from-purple-500 to-pink-500"></span>
                <span>{t('voiceOptions')}</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full flex-shrink-0 bg-gradient-to-r from-purple-500 to-pink-500"></span>
                <span>{t('styleOptions')}</span>
              </li>
            </ul>
          </div>

          <div className="bg-gray-900/50 rounded-xl p-6 backdrop-blur border border-gray-800/50 hover:border-purple-500/50 transition-colors">
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white flex items-center justify-center font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold text-purple-400">
                {t('generateDownload')}
              </h3>
            </div>
            <p className="text-gray-300">{t('finalStepDesc')}</p>
          </div>
        </div>

        <div className="hidden md:block">
          <div className="w-full h-full rounded-2xl overflow-hidden">
            <div
              className="w-full h-full bg-center bg-cover"
              style={{
                backgroundImage: `url('/images/step.png')`,
              }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  )
}
