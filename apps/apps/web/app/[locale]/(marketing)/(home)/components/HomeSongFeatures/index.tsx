// HomeSongFeatures.jsx
import Link from 'next/link'
import { useTranslations } from 'next-intl'

const HomeSongFeatures = () => {
  const t = useTranslations()

  return (
    <section className="pt-20">
      <div className="container mx-auto px-4">
        {/* 顶部标题部分 */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-purple-500 mb-8">
            {t('makeUnlimitedSongWithAI')}
          </h2>
          <p className="text-gray-400 max-w-4xl mb-4 mx-auto text-center text-lg md:text-xl">
            {t('howToMakeDesc')}
          </p>
          <p className="text-[#b2de4a] max-w-4xl mx-auto mb-16 text-center text-lg md:text-xl">
            {t('howToMakeDesc1')}
          </p>
          {/* 两种模式说明 */}
          <div className="grid md:grid-cols-2 gap-8 mb-12 relative">
            {/* Description Mode */}
            <div className="p-6 bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <h3 className="text-2xl font-semibold mb-4 text-purple-400">
                {t('songDescriptionMode')}
              </h3>
              <p className="text-gray-400 mb-4 text-left">
                {t('descriptionToSong')}
              </p>
            </div>

            {/* Customer Mode */}
            <div className="p-6 bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <h3 className="text-2xl font-semibold mb-4 text-purple-400">
                {t('customerMode')}
              </h3>
              <p className="text-gray-400 mb-4 text-left">
                {t('jmakesongLyricsInput')}
              </p>
            </div>
          </div>
          <Link href="/ai-music-generator" target="_blank">
            <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-3 rounded-full text-lg font-medium hover:opacity-90 transition-opacity">
              {t('freeAISongGen')}
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default HomeSongFeatures
