import React, { useState, useEffect } from 'react'
import { Clock, Zap, Check, X, Loader2 } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
// import { useRouter } from 'next/router'
import { getLocale, getMessages } from 'next-intl/server'

const QueueModal = ({
  isOpen,
  onClose,
  waitTimeInMinutes = 15,
  isMobile,
  locale,
  generateMusic,
}: any) => {
  const [isQueuing, setIsQueuing] = useState(false)
  const [progress, setProgress] = useState(0)
  const t = useTranslations()
  const router = useRouter()

  console.log('faith=============locale-locale', locale)
  console.log('faith=============router', router)

  const MAX_WAIT_TIME = Math.round(waitTimeInMinutes * 60) // 将分钟转换为秒，并四舍五入
  const [timeLeft, setTimeLeft] = useState(MAX_WAIT_TIME)

  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     setTimeLeft((prev) => Math.max(prev - 1, 0))
  //   }, 1000)
  //   return () => clearInterval(interval)
  // }, [])

  // 监听剩余时间是否到达10秒
  useEffect(() => {
    if (timeLeft <= 2 && timeLeft > 0) {
      sessionStorage.setItem('queue_played', '1')
      console.log('还剩10秒！') // 这里可以添加你想要的处理逻辑
      //
      // 比如：
      // - 播放提示音
      // - 显示特殊提示
      // - 触发回调函数
      // - 改变UI样式等
    }
  }, [timeLeft])

  // 添加一个函数来计算时间百分比
  const getTimePercentage = () => {
    return ((900 - timeLeft) / 900) * 100
  }

  const formatTime = (seconds: any) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 只保留并修改这个 useEffect
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeLeft((prev) => {
        const newTime = Math.max(prev - 1, 0)
        // 根据剩余时间更新进度
        setProgress(((MAX_WAIT_TIME - newTime) / MAX_WAIT_TIME) * 100)

        // 检查是否剩余2秒
        if (newTime <= 2 && newTime > 0) {
          sessionStorage.setItem('queue_played', '1')
          console.log('还剩2秒！')
        }

        return newTime
      })
    }, 1000)

    return () => clearInterval(interval)
  }, []) // 添加 MAX_WAIT_TIME 作为依赖

  useEffect(() => {
    if (timeLeft <= 0) {
      setTimeout(() => {
        onClose()
        generateMusic()
      }, 1000)
    }
  }, [timeLeft])
  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/75 backdrop-blur-sm"
          />

          <div className="fixed inset-0 flex items-center justify-center p-4">
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="relative w-full max-w-md bg-gray-800/95 rounded-2xl p-6 shadow-xl border border-gray-700/50"
            >
              {!isQueuing ? (
                <>
                  <button
                    onClick={onClose}
                    className="absolute right-4 top-4 text-gray-400 hover:text-gray-300 transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Clock className="w-5 h-5 text-amber-500" />
                      <span className="text-base text-white font-medium">
                        {t('freeChannelQueuing')}
                      </span>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <span>{t('highTrafficWaitMessage')}</span>
                      <span className="text-purple-400 font-medium">
                        {formatTime(timeLeft)}
                      </span>
                    </div>

                    <div className="relative h-1 bg-gray-700/50 rounded-full overflow-hidden">
                      <motion.div
                        className="absolute left-0 top-0 h-full bg-gradient-to-r from-purple-500 to-pink-500"
                        style={{ width: `${progress}%` }}
                        initial={{ width: '0%' }}
                        animate={{ width: `${progress}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>

                    {/* 添加提示信息 */}
                    <div className="flex items-center space-x-2 text-xs text-amber-500/90 bg-amber-500/10 px-3 py-2 rounded-lg">
                      <span>⚠️</span>
                      <span>{t('closeWindowWarning')},</span>
                    </div>
                  </div>

                  <div className="mt-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl p-4 backdrop-blur-sm border border-purple-500/20">
                    <div className="flex items-center space-x-2 text-purple-400 mb-4">
                      <Zap className="w-5 h-5" />
                      <h3 className="font-medium">
                        {t('memberBenefitsTitle')}
                      </h3>
                    </div>

                    <ul className="space-y-3 ">
                      {[
                        t('memberFeatures1'),
                        t('memberFeatures2'),
                        t('memberFeatures3'),
                        t('memberFeatures4'),
                      ].map((feature, index) => (
                        <li
                          key={index}
                          className="flex items-center space-x-3 text-sm"
                        >
                          <div className="bg-green-400/10 p-1 rounded-full">
                            <Check className="w-4 h-4 text-green-400" />
                          </div>
                          <span className="text-gray-200">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {!isMobile && (
                    <div className="space-y-3 mt-3">
                      <div className="flex flex-col items-center space-y-3 bg-gray-800/50 px-4 py-3 rounded-lg border border-gray-700/50">
                        <div className="text-center">
                          <span className="text-xl font-semibold text-white">
                            {t('creatorsChoice')}
                          </span>
                          <div className="mt-1 text-sm text-gray-400">
                            {t('globalCoverageMessage')}
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-1.5 justify-center text-xs">
                          <span className="px-2 py-0.5 bg-gray-700/50 text-gray-300 rounded-full">
                            {t('socialPlatformstext1')}
                          </span>
                          <span className="px-2 py-0.5 bg-gray-700/50 text-gray-300 rounded-full">
                            {t('socialPlatformstext2')}
                          </span>
                          <span className="px-2 py-0.5 bg-gray-700/50 text-gray-300 rounded-full">
                            {t('socialPlatformstext3')}
                          </span>
                          <span className="px-2 py-0.5 bg-gray-700/50 text-gray-300 rounded-full">
                            {t('socialPlatformstext4')}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="mt-6 space-y-3">
                    <Link
                      // href="/pricing"
                      href={locale?.includes('ru') ? '/ru/pricing' : '/pricing'}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl font-medium hover:opacity-90 transition-all text-center"
                    >
                      {t('subscribeCTA')}
                    </Link>
                  </div>
                </>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Clock className="w-5 h-5 text-blue-400 animate-pulse" />
                      <span className="font-medium text-white">
                        {t('queueingTitle')}
                      </span>
                    </div>
                    <button
                      onClick={() => {
                        setIsQueuing(false)
                        onClose()
                      }}
                      className="text-sm text-gray-400 hover:text-gray-300 transition-colors"
                    >
                      {t('cancelGeneration')}
                    </button>
                  </div>

                  <div className="relative h-2 bg-gray-700 rounded-full overflow-hidden">
                    <motion.div
                      className="absolute left-0 top-0 h-full bg-gradient-to-r from-purple-500 to-pink-500"
                      style={{ width: `${progress}%` }}
                      initial={{ width: '0%' }}
                      animate={{ width: `${progress}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>

                  <div className="flex justify-between items-center text-sm text-gray-400">
                    <div className="flex items-center space-x-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>
                        {t('estimatedWait')}: {formatTime(timeLeft)}
                      </span>
                    </div>
                    <span>{Math.round(progress)}%</span>
                  </div>

                  <div className="p-3 bg-gray-700/30 rounded-lg border border-gray-600/30">
                    <div className="flex items-center space-x-2 text-sm text-gray-300">
                      <Zap className="w-4 h-4 text-purple-400" />
                      <span>{t('memberFastTrackTip')}</span>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  )
}

export default QueueModal
