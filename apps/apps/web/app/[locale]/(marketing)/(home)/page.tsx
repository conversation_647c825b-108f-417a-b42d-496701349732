import { Footer } from '@marketing/shared/components/Footer'
import { Music, Sparkles, Mic2, Timer } from 'lucide-react'
import Head from 'next/head'
import MusicEdit from './components/MusicEdit'
import MusicShowcase2 from './components/MusicShowcase2/MusicShowcase'
import HomeSongFeatures from './components/HomeSongFeatures'
import AiSongGenerator from './components/AiSongGenerator'
import FaqSectionPro from './components/FaqSectionPro'
import TestimonialSection from './components/TestimonialSection'
import { LanguageSection } from './components/LanguageSection'
import MusicBackground from './components/MusicBackground'
import { getTranslations } from 'next-intl/server'
import PricingSection from './components/PricingSection'
import MusicGenerationSteps from './components/GenerationStep'
import ProductIntro from './components/ProductIntro'
import WhyUs from './components/WhyUs'
import MusicSharingFeature from './components/MusicSharingFeature'
import NewFeaturesSection from './components/NewFeaturesSection'
import LabubuPromo from './components/LabubuPromo'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('makeSongTagline'),
    description: t('makeSongDesc'),
    keywords: t('makeSongKeywords'),
  }
}

export default async function Home() {
  const t = await getTranslations()
  return (
    <>
      <div className="relative min-h-screen">
        {/* 背景容器 */}
        <div className="absolute inset-0 overflow-hidden">
          <MusicBackground />
        </div>

        {/* 主要内容，确保在背景之上 */}
        <div className=" pt-32 text-center relative z-10">
          <div className="relative">
            <h1 className="mx-auto text-white max-4xl text-balance font-bold text-3xl sm:text-4xl md:text-5xl lg:text-7xl sm:pb-8 xl:pb-8  animate-floating">
              {t('home.title')}
            </h1>
            <h2 className="text-lg font-bold text-white mb-4 px-2">
              {t('home.desc')}
            </h2>
            <div className="relative mb-3 flex justify-center">
              <div className="overflow-x-auto pb-1.5 hide-scrollbar">
                <div className="flex gap-2 px-0.5">
                  <span className="px-3 py-1.5 bg-gray-800/80 rounded-full backdrop-blur-sm flex-shrink-0 flex items-center gap-1.5 text-xs">
                    <Sparkles className="w-3.5 h-3.5 text-pink-400" />
                    {t('royaltyFree100')}
                  </span>
                  <span className="px-3 py-1.5 bg-gray-800/80 rounded-full backdrop-blur-sm flex-shrink-0 flex items-center gap-1.5 text-xs">
                    <Music className="w-3.5 h-3.5 text-purple-400" />
                    {t('textToMusic')}
                  </span>
                  <span className="px-3 py-1.5 bg-gray-800/80 rounded-full backdrop-blur-sm flex-shrink-0 flex items-center gap-1.5 text-xs">
                    <Mic2 className="w-3.5 h-3.5 text-purple-400" />
                    {t('supportIsolateVocals')}
                  </span>
                  <span className="px-3 py-1.5 bg-gray-800/80 rounded-full backdrop-blur-sm flex-shrink-0 flex items-center gap-1.5 text-xs">
                    <Timer className="w-3.5 h-3.5 text-purple-400" />
                    {t('songIn30s')}
                  </span>
                  <span className="px-3 py-1.5 bg-gray-800/80 rounded-full backdrop-blur-sm flex-shrink-0 flex items-center gap-1.5 text-xs">
                    <Timer className="w-3.5 h-3.5 text-purple-400" />
                    {t('stemSeparator')}
                  </span>
                </div>
              </div>

              {/* 可选：添加渐变指示器表明可以滚动 */}
              <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-black/40 to-transparent pointer-events-none"></div>
            </div>
          </div>

          <section className="pb-20">
            <MusicEdit />
          </section>
        </div>

        {/* 您的页面内容 */}
        <LabubuPromo />
      </div>

      {/* 音乐展示 */}
      <MusicShowcase2 />

      {/* 新特性 */}
      <NewFeaturesSection />

      <MusicGenerationSteps />
      <ProductIntro />
      {/* <LanguageSection /> */}
      <HomeSongFeatures />
      <AiSongGenerator />
      <WhyUs />
      <TestimonialSection />

      {/* 音乐分享模块 */}
      <MusicSharingFeature />
      <PricingSection />
      <FaqSectionPro />
      {/* <Features /> */}
      {/* <FaqSection /> */}
      <Head>
        <title>AI Music Generator | Create Royalty Free Music</title>
        <meta
          name="description"
          content="Create unlimited royalty-free music using advanced AI. Transform your lyrics or descriptions into professional songs instantly."
        />
        <meta
          name="keywords"
          content="ai music generator, music ai, song generator, lyrics to music, ai composer"
        />
        <meta
          property="og:title"
          content="AI Music Generator | Create Royalty Free Music"
        />
        <meta
          property="og:description"
          content="Create unlimited royalty-free music using advanced AI technology."
        />
        <link rel="canonical" href="https://yourdomain.com" />
      </Head>
      <main className="text-white">
        <Footer />
      </main>
    </>
  )
}

const testimonials = [
  {
    content:
      'This AI music generator has revolutionized my creative process. Amazing results!',
    name: 'Sarah Johnson',
    title: 'Content Creator',
  },
  // Add more testimonials...
]
