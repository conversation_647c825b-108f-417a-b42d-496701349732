'use client'

import { But<PERSON> } from '@ui/components/button'
import { Input } from '@ui/components/input'
import { Textarea } from '@ui/components/textarea'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import {
  Sparkles,
  Music,
  Zap,
  Palette,
  Globe,
  Volume2,
  Co<PERSON>,
  Check,
  Play,
  RefreshCw,
  Edit3,
} from 'lucide-react'
import { CustomSearchableSelect } from './CustomSearchableSelect'
import { ShimmerButton } from './ShimmerButton'
import { SectionCard } from './SectionCard'
import { ProductAdvantages } from './ProductAdvantages'
import { UseCases } from './UseCases'
import { UserGuide } from './UserGuide'
import { POPULAR_LANGUAGES, REGIONS, REGION_LANG_MAP } from '../constants'
import { enStyles } from '../../(home)/components/MusicEdit/config'
import { UserReviews } from './UserReviews'
import { FAQ } from './FAQ'
import { Footer } from '@marketing/shared/components/Footer'
import ScrollAnimation from './ScrollAnimation/ScrollAnimation'
import { useTranslations } from 'next-intl'

interface Translations {
  title: string
  subtitle: string
  songDescription: {
    title: string
    placeholder: string
  }
  analyzeSong: {
    title: string
    songNamePlaceholder: string
    analyzeButton: string
    analyzingButton: string
    loadingText: string
  }
  musicStyle: {
    title: string
    showAll: string
    collapse: string
  }
  eraRegion: {
    title: string
    eraLabel: string
    regionLabel: string
  }
  musicAttributes: {
    title: string
    tempoLabel: string
    durationLabel: string
  }
  generateButton: {
    text: string
    generating: string
  }
  generatedLyrics: {
    title: string
    copyButton: string
    copiedButton: string
    generateMusicButton: string
    editHint: string
    resetButton: string
  }
  errors: {
    analyzeFailed: string
    noLyricsGenerated: string
    copyFailed: string
  }
  ERAS: string[]
  DURATION_OPTIONS: { value: number; label: string }[]
  BPM_OPTIONS: { value: number; label: string; description: string }[]
}

interface LyricsGeneratorClientProps {
  translations: Translations
}

export function LyricsGeneratorClient({
  translations,
}: LyricsGeneratorClientProps) {
  const router = useRouter()
  const t = useTranslations('aiLyricsGen')
  const [description, setDescription] = useState('')
  const [songName, setSongName] = useState('')
  const [songRegion, setSongRegion] = useState('US/Europe')
  const [analyzing, setAnalyzing] = useState(false)
  const [analyzeResult, setAnalyzeResult] = useState<any>(null)
  const [selectedAnalyzedStyles, setSelectedAnalyzedStyles] = useState<
    string[]
  >([])
  const [selectedStyles, setSelectedStyles] = useState<string[]>([])
  const [showAllStyles, setShowAllStyles] = useState(false)
  const [selectedEra, setSelectedEra] = useState('Modern')
  const [selectedRegion, setSelectedRegion] = useState('English')
  const [tempo, setTempo] = useState(120)
  const [duration, setDuration] = useState(3)
  const [generating, setGenerating] = useState(false)
  const [lyrics, setLyrics] = useState('')
  const [editedLyrics, setEditedLyrics] = useState('') // 新增：用于存储用户编辑后的歌词
  const [copied, setCopied] = useState(false)
  const [rowHeight, setRowHeight] = useState<number>(260)

  const analyzeCardRef = useRef<HTMLDivElement>(null)
  const analyzeTitleRef = useRef<HTMLDivElement>(null)
  const analyzeRef = useRef<HTMLDivElement>(null)
  const styleRef = useRef<HTMLDivElement>(null)
  const lyricsRef = useRef<HTMLDivElement>(null)

  // BPM速度术语（保留用于兼容性）
  const getTempoTerm = (bpm: number): string => {
    if (bpm <= 60) return 'Largo/Lento'
    if (bpm <= 108) return 'Andante'
    if (bpm <= 115) return 'Moderato'
    if (bpm <= 168) return 'Allegro'
    if (bpm <= 200) return 'Presto'
    return 'Prestissimo'
  }

  // BPM速度描述（保留用于兼容性）
  const getTempoDescription = (bpm: number): string => {
    if (bpm <= 60) return t('musicAttributes.tempo.Largo/Lento')
    if (bpm <= 108) return t('musicAttributes.tempo.Andante')
    if (bpm <= 115) return t('musicAttributes.tempo.Moderato')
    if (bpm <= 168) return t('musicAttributes.tempo.Allegro')
    if (bpm <= 200) return t('musicAttributes.tempo.Presto')
    return t('musicAttributes.tempo.Prestissimo')
  }

  // 动态计算高度
  useEffect(() => {
    const updateHeight = () => {
      if (analyzeResult && analyzeRef.current) {
        setRowHeight(analyzeRef.current.offsetHeight)
      } else {
        setRowHeight(0)
      }
    }

    updateHeight()
    window.addEventListener('resize', updateHeight)

    const observer = new ResizeObserver(updateHeight)
    if (analyzeRef.current) observer.observe(analyzeRef.current)
    if (styleRef.current) observer.observe(styleRef.current)

    return () => {
      window.removeEventListener('resize', updateHeight)
      observer.disconnect()
    }
  }, [analyzeResult, showAllStyles, selectedAnalyzedStyles, selectedStyles])

  // 当分析结果出现时，自动展开Music Style
  useEffect(() => {
    if (analyzeResult && !showAllStyles) {
      setShowAllStyles(true)
    }
  }, [analyzeResult])

  // 当歌词生成后，同步到编辑状态
  useEffect(() => {
    if (lyrics) {
      setEditedLyrics(lyrics)
    }
  }, [lyrics])

  // 尝试从本地存储加载之前编辑的歌词
  useEffect(() => {
    const savedLyrics = localStorage.getItem('EDITED_LYRICS')
    if (savedLyrics) {
      setEditedLyrics(savedLyrics)
      if (!lyrics) {
        setLyrics(savedLyrics) // 如果没有原始歌词，也设置它以便UI正确显示
      }
    }
  }, [])

  // 保存编辑后的歌词到本地存储
  useEffect(() => {
    if (editedLyrics) {
      localStorage.setItem('EDITED_LYRICS', editedLyrics)
    }
  }, [editedLyrics])

  // 分析歌曲 - 使用后端API
  async function handleAnalyzeSong() {
    setAnalyzing(true)
    setAnalyzeResult(null)
    setSelectedAnalyzedStyles([])

    try {
      // 调用后端API
      const response = await fetch('/api/music/analyze-song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          songName,
          songRegion,
        }),
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('API Response:', data)

      if (!data.success) {
        throw new Error(data.error || 'Analysis failed')
      }

      setAnalyzeResult(data.analysis)
    } catch (error: any) {
      console.error('Analysis error:', error)
      setAnalyzeResult({
        error: `Analysis failed: ${error.message}`,
        styles: [],
        elements: [],
        description: '',
      })
    } finally {
      setAnalyzing(false)
    }
  }

  // 生成歌词 - 使用后端API
  async function handleGenerateLyrics() {
    if (!description.trim()) return

    setGenerating(true)
    setLyrics('')
    setEditedLyrics('') // 新生成时清空编辑状态

    const allStyles = Array.from(
      new Set([...selectedAnalyzedStyles, ...selectedStyles])
    )

    try {
      // 调用后端API
      const response = await fetch('/api/music/generate-lyrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description,
          styles: allStyles,
          era: selectedEra,
          region: selectedRegion,
          tempo,
          duration,
        }),
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('Lyrics generation response:', data)

      if (!data.success) {
        throw new Error(data.error || translations.errors.noLyricsGenerated)
      }

      const lyricsText = data.song.lyrics

      if (!lyricsText.trim()) {
        throw new Error(translations.errors.noLyricsGenerated)
      }

      setLyrics(lyricsText.trim())
      // editedLyrics 会通过 useEffect 设置

      setTimeout(() => {
        if (lyricsRef.current) {
          lyricsRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          })
        }
      }, 100)
    } catch (error: any) {
      console.error('Lyrics generation error:', error)
      setLyrics(`Error: ${translations.errors.analyzeFailed}`)
      setEditedLyrics(`Error: ${translations.errors.analyzeFailed}`)
    } finally {
      setGenerating(false)
    }
  }

  const handleCopyLyrics = async () => {
    try {
      // 复制编辑后的歌词而不是原始歌词
      await navigator.clipboard.writeText(editedLyrics)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error(translations.errors.copyFailed, error)
    }
  }

  const handleGenerateMusic = () => {
    if (!editedLyrics) return

    const allStyles = Array.from(
      new Set([...selectedAnalyzedStyles, ...selectedStyles])
    )

    // 使用编辑后的歌词
    const musicData = {
      lyrics: editedLyrics,
      selectedStyles: allStyles,
      title: description.substring(0, 50) || 'Generated Song',
      generationMode: 'custom' as const,
      fromLyricsGenerator: true,
    }

    localStorage.setItem('LYRICS_TO_MUSIC_DATA', JSON.stringify(musicData))
    router.push('/')
  }

  // 重置编辑的歌词为原始生成的歌词
  const handleResetLyrics = () => {
    setEditedLyrics(lyrics)
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* 背景动画效果 */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob"></div>
        <div className="absolute top-0 right-0 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-0 left-0 w-72 h-72 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-blob animation-delay-4000"></div>
      </div>

      <ScrollAnimation direction="up" delay={600} duration={800}>
        <div className="relative z-10 container mx-auto py-8 px-4 min-h-screen">
          {/* 标题区域 */}
          <div className="space-y-4 mb-12 mt-24">
            <h1 className="mx-auto text-white text-balance font-bold text-3xl sm:text-4xl md:text-5xl lg:text-7xl animate-floating text-center">
              {translations.title}
            </h1>
            <p className="text-lg max-w-5xl mx-auto font-bold text-white px-2 text-center">
              {translations.subtitle}
            </p>
          </div>

          {/* Song Description */}
          <div className="mb-8">
            <SectionCard
              title={translations.songDescription.title}
              icon={Music}
              iconGradient="bg-gradient-to-r from-purple-500 to-pink-500"
            >
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder={translations.songDescription.placeholder}
                rows={4}
                className="bg-white/10 border-white/20 text-white placeholder:text-[#b0b0b0] rounded-2xl resize-none focus:ring-2 focus:ring-purple-400 focus:border-transparent"
              />
            </SectionCard>
          </div>

          {/* 第一行：分析和风格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {/* Analyze Existing Song */}
            <div
              ref={analyzeRef}
              className="flex-1 bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 shadow-2xl flex flex-col box-border"
            >
              <div
                ref={analyzeTitleRef}
                className="flex items-center gap-3 mb-4"
              >
                <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl">
                  <Music className="w-5 h-5 text-white" />
                </div>
                <h2 className="text-xl font-semibold text-white">
                  {translations.analyzeSong.title}
                </h2>
              </div>
              <div className="flex gap-2 mb-3 items-center">
                <Input
                  value={songName}
                  onChange={(e) => setSongName(e.target.value)}
                  placeholder={translations.analyzeSong.songNamePlaceholder}
                  className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-[#b0b0b0] rounded-2xl focus:ring-2 focus:ring-purple-400 focus:border-transparent h-12"
                />
                <CustomSearchableSelect
                  value={songRegion}
                  onChange={setSongRegion}
                  options={REGIONS as any}
                  placeholder={t('selectRegion')}
                  className="w-32"
                />
                <ShimmerButton
                  onClick={handleAnalyzeSong}
                  disabled={!songName || analyzing}
                  size="md"
                >
                  {analyzing
                    ? translations.analyzeSong.analyzingButton
                    : translations.analyzeSong.analyzeButton}
                </ShimmerButton>
              </div>
              {analyzing && (
                <div className="mt-4 bg-white/5 rounded-2xl p-6 border border-white/10">
                  <div className="flex items-center justify-center gap-3">
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span className="text-white text-sm">
                      {translations.analyzeSong.loadingText}
                    </span>
                  </div>
                </div>
              )}

              {analyzeResult && !analyzing && (
                <div
                  ref={analyzeCardRef}
                  className="mt-4 bg-white/5 rounded-2xl p-4 border border-white/10 animate-fade-in"
                >
                  {analyzeResult.error && (
                    <div className="mb-3 text-sm text-red-300">
                      Error: {analyzeResult.error}
                    </div>
                  )}

                  {!analyzeResult.error && (
                    <>
                      <div className="mb-3 text-sm font-medium text-white">
                        Styles:
                      </div>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {(analyzeResult.styles || []).map(
                          (style: string, index: number) => (
                            <Button
                              key={style}
                              type="button"
                              variant={
                                selectedAnalyzedStyles.includes(style)
                                  ? 'default'
                                  : 'outline'
                              }
                              className={`rounded-full transition-all duration-300 px-3 py-1 text-xs animate-slide-in ${
                                selectedAnalyzedStyles.includes(style)
                                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                                  : 'bg-white/10 border-white/20 text-white hover:bg-white/20 hover:scale-105'
                              }`}
                              style={{
                                animationDelay: `${index * 0.1}s`,
                                animationFillMode: 'both',
                              }}
                              onClick={() => {
                                setSelectedAnalyzedStyles(
                                  selectedAnalyzedStyles.includes(style)
                                    ? selectedAnalyzedStyles.filter(
                                        (s) => s !== style
                                      )
                                    : [...selectedAnalyzedStyles, style]
                                )
                              }}
                            >
                              {style}
                            </Button>
                          )
                        )}
                      </div>
                      <div className="mb-2 text-sm font-medium text-white">
                        Elements:
                      </div>
                      <div className="flex flex-wrap gap-2 mb-2">
                        {analyzeResult.elements?.map(
                          (el: string, index: number) => (
                            <span
                              key={el}
                              className="px-3 py-1 rounded-full bg-purple-500/20 text-xs font-medium text-purple-200 border border-purple-500/30 animate-slide-in"
                              style={{
                                animationDelay: `${
                                  (index +
                                    (analyzeResult.styles?.length || 0)) *
                                  0.1
                                }s`,
                                animationFillMode: 'both',
                              }}
                            >
                              {el}
                            </span>
                          )
                        )}
                      </div>
                      <div className="text-xs text-purple-200 mt-2 animate-fade-in-up">
                        {analyzeResult.description}
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Music Style */}
            <div
              ref={styleRef}
              className="flex-1 bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 shadow-2xl flex flex-col box-border"
              style={analyzeResult ? { height: `${rowHeight}px` } : {}}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-pink-500 to-rose-500 rounded-xl">
                    <Palette className="w-5 h-5 text-white" />
                  </div>
                  <h2 className="text-xl font-semibold text-white">
                    {translations.musicStyle.title}
                  </h2>
                </div>
                <button
                  onClick={() => setShowAllStyles(!showAllStyles)}
                  className="text-purple-300 hover:text-white transition-colors duration-200 text-sm underline underline-offset-2 hover:no-underline"
                >
                  {showAllStyles
                    ? translations.musicStyle.collapse
                    : translations.musicStyle.showAll}
                </button>
              </div>
              <div
                className={
                  showAllStyles
                    ? 'flex-1 overflow-auto flex flex-wrap gap-2'
                    : 'flex flex-wrap gap-2'
                }
                style={
                  showAllStyles
                    ? {
                        paddingRight: '0.25rem',
                        maxHeight: analyzeResult ? 'none' : '200px',
                        overflowY: 'auto',
                      }
                    : {}
                }
              >
                {(showAllStyles ? enStyles : enStyles.slice(0, 12)).map(
                  (style) => (
                    <Button
                      key={style}
                      type="button"
                      variant={
                        selectedStyles.includes(style) ? 'default' : 'outline'
                      }
                      className={`rounded-full transition-all duration-300 px-3 py-1 text-xs ${
                        selectedStyles.includes(style)
                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg scale-105'
                          : 'bg-white/10 border-white/20 text-white hover:bg-white/20 hover:scale-105'
                      }`}
                      onClick={() => {
                        setSelectedStyles(
                          selectedStyles.includes(style)
                            ? selectedStyles.filter((s) => s !== style)
                            : [...selectedStyles, style]
                        )
                      }}
                    >
                      {style}
                    </Button>
                  )
                )}
              </div>
            </div>
          </div>

          {/* 第二行：Region和Music Attributes */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {/* Region */}
            <SectionCard
              title={translations.eraRegion.title}
              icon={Globe}
              iconGradient="bg-gradient-to-r from-violet-500 to-purple-500"
              minHeight="260px"
            >
              <div className="space-y-4">
                <div>
                  <label className="text-purple-200 text-sm mb-2 block">
                    Popular Languages
                  </label>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {POPULAR_LANGUAGES.map((language) => (
                      <Button
                        key={language}
                        type="button"
                        variant={
                          selectedRegion === language ? 'default' : 'outline'
                        }
                        className={`rounded-full transition-all duration-300 px-3 py-1 text-xs ${
                          selectedRegion === language
                            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                            : 'bg-white/10 border-white/20 text-white hover:bg-white/20 hover:scale-105'
                        }`}
                        onClick={() => setSelectedRegion(language)}
                      >
                        {language}
                      </Button>
                    ))}
                  </div>
                  <label className="text-purple-200 text-sm mb-2 block">
                    Other Regions
                  </label>
                  <CustomSearchableSelect
                    value={selectedRegion}
                    onChange={setSelectedRegion}
                    options={REGIONS as any}
                    placeholder={t('selectRegion')}
                    className="w-full"
                  />
                </div>
              </div>
            </SectionCard>

            {/* Music Attributes */}
            <SectionCard
              title={translations.musicAttributes.title}
              icon={Volume2}
              iconGradient="bg-gradient-to-r from-fuchsia-500 to-pink-500"
              minHeight="260px"
            >
              <div className="space-y-4">
                <div>
                  <label className="text-purple-200 text-sm mb-2 block">
                    {translations.eraRegion.eraLabel}
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {translations.ERAS.map((era) => (
                      <Button
                        key={era}
                        type="button"
                        variant={selectedEra === era ? 'default' : 'outline'}
                        className={`rounded-full transition-all duration-300 px-3 py-1 text-xs ${
                          selectedEra === era
                            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                            : 'bg-white/10 border-white/20 text-white hover:bg-white/20 hover:scale-105'
                        }`}
                        onClick={() => setSelectedEra(era)}
                      >
                        {era}
                      </Button>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="text-purple-200 text-sm mb-2 block">
                    {translations.musicAttributes.tempoLabel}
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {translations.BPM_OPTIONS.map((option) => (
                      <Button
                        key={option.value}
                        type="button"
                        variant={tempo === option.value ? 'default' : 'outline'}
                        className={`rounded-full transition-all duration-300 px-3 py-1 text-xs ${
                          tempo === option.value
                            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                            : 'bg-white/10 border-white/20 text-white hover:bg-white/20 hover:scale-105'
                        }`}
                        onClick={() => setTempo(option.value)}
                      >
                        <div className="text-center">
                          <div className="font-semibold">{option.label}</div>
                          <div className="text-xs opacity-80">
                            {option.description}
                          </div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="text-purple-200 text-sm mb-2 block">
                    {translations.musicAttributes.durationLabel}
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {translations.DURATION_OPTIONS.map((option) => (
                      <Button
                        key={option.value}
                        type="button"
                        variant={
                          duration === option.value ? 'default' : 'outline'
                        }
                        className={`rounded-full transition-all duration-300 px-3 py-1 text-xs ${
                          duration === option.value
                            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                            : 'bg-white/10 border-white/20 text-white hover:bg-white/20 hover:scale-105'
                        }`}
                        onClick={() => setDuration(option.value)}
                      >
                        {option.label}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </SectionCard>
          </div>

          {/* 生成按钮 */}
          <div className="mt-8">
            <button
              onClick={handleGenerateLyrics}
              disabled={generating || !description}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 py-4 rounded-lg font-medium hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed text-white text-lg"
            >
              {generating ? (
                <div className="flex items-center justify-center gap-3">
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  {translations.generateButton.generating}
                </div>
              ) : (
                <div className="flex items-center justify-center gap-3">
                  <Zap className="w-6 h-6" />
                  {translations.generateButton.text}
                </div>
              )}
            </button>
          </div>

          {/* 歌词结果 - 添加编辑功能 */}
          {lyrics && (
            <div
              ref={lyricsRef}
              className="mt-8 bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 shadow-2xl"
            >
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <h2 className="text-xl font-semibold text-white">
                    {translations.generatedLyrics.title}
                  </h2>
                </div>
                <div className="flex items-center gap-2 flex-wrap">
                  <Button
                    onClick={handleGenerateMusic}
                    variant="default"
                    size="sm"
                    className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white rounded-xl transition-all duration-200 flex items-center gap-2"
                  >
                    <Play className="w-4 h-4" />
                    <span className="text-sm">
                      {translations.generatedLyrics.generateMusicButton}
                    </span>
                  </Button>
                  <Button
                    onClick={handleCopyLyrics}
                    variant="ghost"
                    size="sm"
                    className="text-white hover:text-purple-300 hover:bg-white/10 rounded-xl transition-all duration-200"
                  >
                    {copied ? (
                      <div className="flex items-center gap-2">
                        <Check className="w-4 h-4" />
                        <span className="text-sm">
                          {translations.generatedLyrics.copiedButton}
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Copy className="w-4 h-4" />
                        <span className="text-sm">
                          {translations.generatedLyrics.copyButton}
                        </span>
                      </div>
                    )}
                  </Button>
                  {/* 新增：重置按钮 */}
                  <Button
                    onClick={handleResetLyrics}
                    variant="outline"
                    size="sm"
                    className="text-white hover:text-purple-300 hover:bg-white/10 rounded-xl transition-all duration-200"
                  >
                    <div className="flex items-center gap-2">
                      <RefreshCw className="w-4 h-4" />
                      <span className="text-sm">
                        {translations.generatedLyrics.resetButton || 'Reset'}
                      </span>
                    </div>
                  </Button>
                </div>
              </div>
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                {/* 添加编辑提示 */}
                <div className="flex justify-end mb-2">
                  <span className="text-xs text-purple-300 flex items-center gap-1">
                    <Edit3 className="w-3 h-3" />
                    {translations.generatedLyrics.editHint ||
                      'Click below to edit lyrics'}
                  </span>
                </div>
                {/* 替换静态pre为可编辑的Textarea */}
                <Textarea
                  value={editedLyrics}
                  onChange={(e) => setEditedLyrics(e.target.value)}
                  className="bg-transparent border-none w-full text-base font-mono leading-relaxed text-white/90 resize-none focus:ring-0 focus:outline-none min-h-[800px]"
                  placeholder="编辑歌词..."
                />
              </div>
            </div>
          )}
        </div>
      </ScrollAnimation>

      {/* 其他模块 */}
      <ScrollAnimation direction="down" delay={600} duration={800}>
        <ProductAdvantages />
      </ScrollAnimation>

      <ScrollAnimation direction="left" delay={600} duration={800}>
        <UseCases />
      </ScrollAnimation>

      <ScrollAnimation direction="right" delay={600} duration={800}>
        <UserGuide />
      </ScrollAnimation>

      <ScrollAnimation direction="fade" delay={600} duration={800}>
        <UserReviews />
      </ScrollAnimation>

      <ScrollAnimation direction="up" delay={600} duration={800}>
        <FAQ />
      </ScrollAnimation>

      <ScrollAnimation direction="up" delay={600} duration={800}>
        <Footer />
      </ScrollAnimation>

      <style jsx>{`
        @keyframes blob {
          0% {
            transform: translate(0px, 0px) scale(1);
          }
          33% {
            transform: translate(30px, -50px) scale(1.1);
          }
          66% {
            transform: translate(-20px, 20px) scale(0.9);
          }
          100% {
            transform: translate(0px, 0px) scale(1);
          }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
        @keyframes floating {
          0%,
          100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        .animate-floating {
          animation: floating 3s ease-in-out infinite;
        }
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: scale(0.95);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
        .animate-fade-in {
          animation: fade-in 0.5s ease-out;
        }
        @keyframes slide-in {
          from {
            opacity: 0;
            transform: translateY(20px) scale(0.8);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        .animate-slide-in {
          animation: slide-in 0.4s ease-out;
        }
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fade-in-up {
          animation: fade-in-up 0.6s ease-out;
          animation-delay: 0.3s;
          animation-fill-mode: both;
        }
      `}</style>
    </div>
  )
}
