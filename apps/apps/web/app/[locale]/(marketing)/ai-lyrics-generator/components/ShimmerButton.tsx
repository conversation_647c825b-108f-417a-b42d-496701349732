'use client'

import { ButtonHTMLAttributes } from 'react'

interface ShimmerButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
}

export function ShimmerButton({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  disabled,
  ...props
}: ShimmerButtonProps) {
  const baseClasses =
    'group/button relative inline-flex items-center justify-center overflow-hidden rounded-2xl font-semibold transition-all duration-300 ease-in-out hover:scale-110 hover:shadow-xl disabled:opacity-50 disabled:scale-100 disabled:shadow-none'

  const variantClasses = {
    primary:
      'bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white hover:shadow-purple-600/50',
    secondary:
      'bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white hover:shadow-pink-600/50',
  }

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm h-10',
    md: 'px-4 py-3 text-sm h-12',
    lg: 'px-6 py-4 text-base h-14',
  }

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      disabled={disabled}
      {...props}
    >
      <span>{children}</span>
      <div className="absolute inset-0 flex h-full w-full justify-center [transform:skew(-13deg)_translateX(-100%)] group-hover/button:duration-1000 group-hover/button:[transform:skew(-13deg)_translateX(100%)] transition-transform duration-1000">
        <div className="relative h-full w-10 bg-white/30"></div>
      </div>
    </button>
  )
}
