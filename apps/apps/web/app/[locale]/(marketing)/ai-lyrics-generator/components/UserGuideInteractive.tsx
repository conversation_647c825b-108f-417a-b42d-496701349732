'use client'

import { But<PERSON> } from '@ui/components/button'
import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'

export function UserGuideInteractive() {
  const t = useTranslations('aiLyricsGen')
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }

  return (
    <>
      {/* 免费试用按钮 */}
      <div
        className={`flex justify-center mt-12 transition-all duration-1000 delay-600 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}
      >
        <Button
          onClick={scrollToTop}
          className="group relative bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-12 py-6 rounded-full transition-all duration-300 shadow-2xl text-lg overflow-hidden"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <span className="relative z-10">{t('quickStart.cta')}</span>
          {/* 按钮光环效果 */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-300 -z-10"></div>
        </Button>
      </div>
    </>
  )
}
