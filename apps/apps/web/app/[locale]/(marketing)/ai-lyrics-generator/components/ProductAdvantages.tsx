import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react'
import { useTranslations } from 'next-intl'

export function ProductAdvantages() {
  const t = useTranslations('aiLyricsGen')
  return (
    <div className="bg-gradient-to-b from-[#131313] via-[#131313]/60 to-gray-900/80 backdrop-blur-xl p-8">
      <div className="container mx-auto px-4">
        {/* 主标题 */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-4">
            {t('standout.title')}
          </h2>
          <p className="text-gray-300 max-w-2xl mx-auto text-lg leading-relaxed">
            {t('standout.description')}
          </p>
        </div>

        {/* 优势卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* 第一个优势 */}
          <div className="bg-gray-800/50 rounded-2xl p-6 hover:bg-gray-700/50 transition-all duration-300 group">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">
                {t('creative.title')}
              </h3>
            </div>
            <p className="text-white/80 leading-relaxed">
              {t('creative.description')}
            </p>
          </div>

          {/* 第二个优势 */}
          <div className="bg-gray-800/50 rounded-2xl p-6 hover:bg-gray-700/50 transition-all duration-300 group">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white">
                {t('privacy.title')}
              </h3>
            </div>
            <p className="text-white/80 leading-relaxed">
              {t('privacy.description')}
            </p>
          </div>
        </div>

        {/* 装饰性元素 */}
        <div className="mt-8 flex justify-center">
          <div className="flex items-center gap-2 text-purple-300">
            <Sparkles className="w-5 h-5" />
            <span className="text-sm font-medium">{t('poweredBy')}</span>
            <Sparkles className="w-5 h-5" />
          </div>
        </div>
      </div>
    </div>
  )
}
