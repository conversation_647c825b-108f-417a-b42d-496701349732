'use client'

import { But<PERSON> } from '@ui/components/button'
import { useLocale } from 'next-intl'
import Link from 'next/link'
import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'

interface Review {
  content: string
  avatar: string
  name: string
  role: string
}

interface UserReviewsInteractiveProps {
  reviews: Review[]
}

export function UserReviewsInteractive({
  reviews,
}: UserReviewsInteractiveProps) {
  const t = useTranslations('aiLyricsGen')
  const [isVisible, setIsVisible] = useState(false)
  const locale = useLocale()

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <>
      {/* 动画评价网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
        {reviews.map((review, index) => (
          <div
            key={index}
            className={`transition-all duration-1000 delay-${index * 200} ${
              isVisible
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <div className="bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 hover:border-purple-500/30 transition-all duration-500 hover:bg-white/10 group">
              {/* 评价内容 */}
              <div className="mb-6">
                <p className="text-lg text-white/90 leading-relaxed italic group-hover:text-white transition-colors duration-300">
                  "{review.content}"
                </p>
              </div>

              {/* 用户信息 */}
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-r from-purple-500 to-pink-500 p-0.5 group-hover:scale-110 transition-transform duration-300">
                  <img
                    src={review.avatar}
                    alt={review.name}
                    className="w-full h-full rounded-full object-cover"
                  />
                </div>
                <div>
                  <h4 className="text-white font-semibold group-hover:text-purple-200 transition-colors duration-300">
                    {review.name}
                  </h4>
                  <p className="text-purple-300 text-sm group-hover:text-purple-100 transition-colors duration-300">
                    {review.role}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 行动按钮 */}
      <div
        className={`flex justify-center mt-12 transition-all duration-1000 delay-800 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}
      >
        <Link href={`/${locale}/contact`}>
          <Button className="group relative bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-12 py-6 rounded-full transition-all duration-300 shadow-2xl text-lg overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <span className="relative z-10">{t('testimonials.cta')}</span>
            {/* 按钮光环效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-300 -z-10"></div>
          </Button>
        </Link>
      </div>
    </>
  )
}
