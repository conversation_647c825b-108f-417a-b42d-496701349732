import { Sparkles, Play, Download } from 'lucide-react'
import { UserGuideInteractive } from './UserGuideInteractive'
import { useTranslations } from 'next-intl'

export function UserGuide() {
  const t = useTranslations('aiLyricsGen')

  // 静态数据，可以在服务端渲染
  const steps = [
    {
      title: t('quickStart.step1.title'),
      content: t('quickStart.step1.description'),
      icon: Play,
      gradient: 'from-blue-500 to-cyan-500',
    },
    {
      title: t('quickStart.step2.title'),
      content: t('quickStart.step2.description'),
      icon: Download,
      gradient: 'from-purple-500 to-pink-500',
    },
  ]
  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-[#131313] via-[#131313]/60 to-gray-900/80 backdrop-blur-xl p-8">
      {/* 背景动画效果 */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"
          style={{ animationDelay: '2s' }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-pink-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"
          style={{ animationDelay: '4s' }}
        ></div>
      </div>

      <div className="relative z-10 container mx-auto px-4">
        {/* 主标题 */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 mb-4">
            <Sparkles className="w-6 h-6 text-purple-400 animate-pulse" />
            <span className="text-purple-400 font-semibold text-sm uppercase tracking-wider">
              {t('quickStart.title')}
            </span>
            <Sparkles className="w-6 h-6 text-purple-400 animate-pulse" />
          </div>

          <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-4">
            {t('quickStart.subtitle')}
          </h2>

          <p className="text-gray-300 max-w-2xl mx-auto text-lg leading-relaxed">
            {t('quickStart.description')}
          </p>
        </div>

        {/* 步骤指南 */}
        <div className="max-w-6xl mx-auto space-y-16">
          {steps.map((step, index) => (
            <div
              className="flex flex-col lg:flex-row items-center gap-12"
              key={index}
            >
              {/* 步骤图标 */}
              <div className="flex-shrink-0 group">
                <div className="relative">
                  <div
                    className={`w-24 h-24 bg-gradient-to-r ${step.gradient} rounded-3xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-all duration-500 group-hover:shadow-purple-500/25 relative overflow-hidden`}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <step.icon className="w-10 h-10 text-white relative z-10 group-hover:scale-110 transition-transform duration-500" />
                  </div>
                  <div className="absolute -top-3 -right-3 w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg border-2 border-purple-500">
                    <span className="text-purple-600 font-bold text-lg">
                      {index + 1}
                    </span>
                  </div>
                  {/* 装饰性光环 */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-r ${step.gradient} rounded-3xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500 -z-10`}
                  ></div>
                </div>
              </div>

              {/* 步骤内容 */}
              <div className="flex-1 text-center lg:text-left group">
                <div className="bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 group-hover:border-purple-500/30 transition-all duration-500 group-hover:bg-white/10">
                  <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-purple-200 transition-colors duration-300">
                    {step.title}
                  </h3>
                  <p className="text-lg text-white/80 leading-relaxed group-hover:text-white/90 transition-colors duration-300">
                    {step.content}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 交互组件 */}
        <UserGuideInteractive />
      </div>
    </div>
  )
}
