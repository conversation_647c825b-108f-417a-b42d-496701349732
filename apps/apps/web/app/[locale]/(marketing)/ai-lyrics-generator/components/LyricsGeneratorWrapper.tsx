import { getTranslations } from 'next-intl/server'
import { LyricsGeneratorClient } from './LyricsGeneratorClient'

export async function LyricsGeneratorWrapper() {
  const t = await getTranslations('aiLyricsGen')

  // 将翻译函数传递给客户端组件
  const translations = {
    title: t('title'),
    subtitle: t('subtitle'),
    songDescription: {
      title: t('songDescription.title'),
      placeholder: t('songDescription.placeholder'),
    },
    analyzeSong: {
      title: t('analyzeSong.title'),
      songNamePlaceholder: t('analyzeSong.songNamePlaceholder'),
      analyzeButton: t('analyzeSong.analyzeButton'),
      analyzingButton: t('analyzeSong.analyzingButton'),
      loadingText: t('analyzeSong.loadingText'),
    },
    musicStyle: {
      title: t('musicStyle.title'),
      showAll: t('musicStyle.showAll'),
      collapse: t('musicStyle.collapse'),
    },
    eraRegion: {
      title: t('eraRegion.title'),
      eraLabel: t('eraRegion.eraLabel'),
      regionLabel: t('eraRegion.regionLabel'),
    },
    musicAttributes: {
      title: t('musicAttributes.title'),
      tempoLabel: t('musicAttributes.tempoLabel'),
      durationLabel: t('musicAttributes.durationLabel'),
    },
    generateButton: {
      text: t('generateButton.text'),
      generating: t('generateButton.generating'),
    },
    generatedLyrics: {
      title: t('generatedLyrics.title'),
      copyButton: t('generatedLyrics.copyButton'),
      copiedButton: t('generatedLyrics.copiedButton'),
      generateMusicButton: t('generatedLyrics.generateMusicButton'),
    },
    errors: {
      analyzeFailed: t('errors.analyzeFailed'),
      noLyricsGenerated: t('errors.noLyricsGenerated'),
      copyFailed: t('errors.copyFailed'),
    },
    ERAS: [
      t('eraRegion.eras.70s'),
      t('eraRegion.eras.80s'),
      t('eraRegion.eras.90s'),
      t('eraRegion.eras.Modern'),
    ],
    // 主流BPM选项（单选框）
    BPM_OPTIONS: [
      {
        value: 80,
        label: '80 BPM',
        description: t('musicAttributes.tempo.Slow'),
      },
      {
        value: 120,
        label: '120 BPM',
        description: t('musicAttributes.tempo.Medium'),
      },
      {
        value: 140,
        label: '140 BPM',
        description: t('musicAttributes.tempo.Fast'),
      },
      {
        value: 160,
        label: '160 BPM',
        description: t('musicAttributes.tempo.VeryFast'),
      },
    ],

    // 时长选项（单选框）
    DURATION_OPTIONS: [
      { value: 1, label: t('musicAttributes.duration.1min') },
      { value: 2, label: t('musicAttributes.duration.2min') },
      { value: 3, label: t('musicAttributes.duration.3min') },
    ],
  }

  return <LyricsGeneratorClient translations={translations} />
}
