'use client'

import { Button } from '@ui/components/button'
import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'

interface FAQ {
  question: string
  answer: string
}

interface FAQInteractiveProps {
  faqs: FAQ[]
}

export function FAQInteractive({ faqs }: FAQInteractiveProps) {
  const [isVisible, setIsVisible] = useState(false)
  const t = useTranslations()
  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <>
      {/* FAQ 列表 */}
      <div className="grid gap-6 max-w-4xl mx-auto">
        {faqs.map((faq, index) => (
          <div
            key={index}
            className={`transition-all duration-1000 delay-${index * 200} ${
              isVisible
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 hover:border-purple-500/50 transition-all duration-300 hover:-translate-y-1">
              <h3 className="text-xl font-semibold mb-4 text-white">
                {faq.question}
              </h3>
              <p className="text-gray-400 leading-relaxed">{faq.answer}</p>
            </div>
          </div>
        ))}
      </div>

      {/* 行动按钮 */}
      <div
        className={`flex justify-center mt-12 transition-all duration-1000 delay-800 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}
      >
        <a href="mailto:<EMAIL>">
          <Button className="group relative bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-12 py-6 rounded-full transition-all duration-300 shadow-2xl text-lg overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <span className="relative z-10">
              {t('LYRICS_GENERATOR_TEXT.stillHaveQuestions')}
            </span>
            {/* 按钮光环效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-300 -z-10"></div>
          </Button>
        </a>
      </div>
    </>
  )
}
