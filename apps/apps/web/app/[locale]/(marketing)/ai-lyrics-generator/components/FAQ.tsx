import { FAQInteractive } from './FAQInteractive'
import { useTranslations } from 'next-intl'

export function FAQ() {
  const t = useTranslations()
  const faqs = [
    {
      question: t('LYRICS_GENERATOR_TEXT.howDoesLyricsToSongAiToolWork'),
      answer: t('LYRICS_GENERATOR_TEXT.platformUsesSophisticatedLanguageModel'),
    },
    {
      question: t(
        'LYRICS_GENERATOR_TEXT.isRapLyricsGeneratorAiSuitableForProfessionalArtists'
      ),
      answer: t('LYRICS_GENERATOR_TEXT.absolutelyManyProfessionalsUse'),
    },
    {
      question: t(
        'LYRICS_GENERATOR_TEXT.whatMakesYourSongWritingToolBestChoice'
      ),
      answer: t('LYRICS_GENERATOR_TEXT.wePrioritizeContextualRelevance'),
    },
    {
      question: t(
        'LYRICS_GENERATOR_TEXT.canIGetIdeasForSongsAboutWritingSongs'
      ),
      answer: t('LYRICS_GENERATOR_TEXT.yesItsGreatMetaExercise'),
    },
  ]
  return (
    <div className="bg-gradient-to-br from-[#131313] via-[#131313]/60 to-[#1A1A29] backdrop-blur-xl p-8">
      {/* 背景动画效果 */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"
          style={{ animationDelay: '2s' }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-pink-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"
          style={{ animationDelay: '4s' }}
        ></div>
      </div>

      <div className="relative z-10 container mx-auto px-4">
        {/* 主标题 */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-4">
            {t('faq.title')}
          </h2>
          <p className="text-gray-300 max-w-2xl mx-auto text-lg leading-relaxed">
            {t('LYRICS_GENERATOR_TEXT.getAnswersToMostCommonQuestions')}
          </p>
        </div>

        {/* 交互组件 */}
        <FAQInteractive faqs={faqs} />
      </div>
    </div>
  )
}
