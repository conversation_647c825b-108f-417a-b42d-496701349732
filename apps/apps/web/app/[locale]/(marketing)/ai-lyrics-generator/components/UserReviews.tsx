import { UserReviewsInteractive } from './UserReviewsInteractive'
import { useTranslations } from 'next-intl'

export function UserReviews() {
  const t = useTranslations('aiLyricsGen')
  // 静态数据，可以在服务端渲染
  const reviews = [
    {
      content: t('testimonials.testimonial1.quote'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Alex',
      name: t('testimonials.testimonial1.name'),
      role: t('testimonials.testimonial1.title'),
    },
    {
      content: t('testimonials.testimonial2.quote'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah',
      name: t('testimonials.testimonial2.name'),
      role: t('testimonials.testimonial2.title'),
    },
    {
      content: t('testimonials.testimonial3.quote'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Mike',
      name: t('testimonials.testimonial3.name'),
      role: t('testimonials.testimonial3.title'),
    },
    {
      content: t('testimonials.testimonial4.quote'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Rachel',
      name: t('testimonials.testimonial4.name'),
      role: t('testimonials.testimonial4.title'),
    },
  ]
  return (
    <div className="bg-gradient-to-b from-gray-900/80 via-[#131313]/60 to-[#131313] backdrop-blur-xl p-8">
      {/* 背景动画效果 */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"
          style={{ animationDelay: '2s' }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-pink-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"
          style={{ animationDelay: '4s' }}
        ></div>
      </div>

      <div className="relative z-10 container mx-auto px-4">
        {/* 主标题 */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-4">
            {t('testimonials.title')}
          </h2>

          <p className="text-gray-300 max-w-2xl mx-auto text-lg leading-relaxed">
            {t('testimonials.description')}
          </p>
        </div>

        {/* 交互组件 */}
        <UserReviewsInteractive reviews={reviews} />
      </div>
    </div>
  )
}
