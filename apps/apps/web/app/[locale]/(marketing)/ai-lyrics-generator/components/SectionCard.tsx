import { ReactNode } from 'react'
import { LucideIcon } from 'lucide-react'

interface SectionCardProps {
  children: ReactNode
  title: string
  icon: LucideIcon
  iconGradient: string
  className?: string
  minHeight?: string
}

export function SectionCard({
  children,
  title,
  icon: Icon,
  iconGradient,
  className = '',
  minHeight,
}: SectionCardProps) {
  return (
    <div
      className={`flex-1 bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 shadow-2xl flex flex-col ${className}`}
      style={minHeight ? { minHeight } : undefined}
    >
      <div className="flex items-center gap-3 mb-4">
        <div className={`p-2 ${iconGradient} rounded-xl`}>
          <Icon className="w-5 h-5 text-white" />
        </div>
        <h2 className="text-xl font-semibold text-white">{title}</h2>
      </div>
      {children}
    </div>
  )
}
