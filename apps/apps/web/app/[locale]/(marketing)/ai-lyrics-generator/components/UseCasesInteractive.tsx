'use client'

import { Button } from '@ui/components/button'
import Image from 'next/image'
import { useState, useEffect } from 'react'

interface UseCase {
  title: string
  content: string
  buttonText: string
  imageUrl: string
  imageAlt: string
  isReversed?: boolean
}

interface UseCasesInteractiveProps {
  useCases: UseCase[]
}

export function UseCasesInteractive({ useCases }: UseCasesInteractiveProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }

  return (
    <div className="space-y-16">
      {useCases.map((useCase, index) => (
        <div
          key={index}
          className={`transition-all duration-800 delay-${index * 200} ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
          }`}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {useCase.isReversed ? (
              <>
                {/* 图片部分 */}
                <div className="flex-1">
                  <div className="relative w-full aspect-video rounded-2xl overflow-hidden">
                    <Image
                      src={useCase.imageUrl}
                      alt={useCase.imageAlt}
                      width={500}
                      height={300}
                      loading="lazy"
                      className="w-full h-full object-cover rounded-2xl"
                    />
                  </div>
                </div>
                {/* 内容部分 */}
                <div className="flex-1 space-y-4">
                  <h3 className="text-2xl font-bold text-white">
                    {useCase.title}
                  </h3>
                  <p className="text-lg text-white/80 leading-relaxed">
                    {useCase.content}
                  </p>
                  <Button
                    onClick={scrollToTop}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-5 py-2.5 rounded-full transition-all duration-200 shadow-lg"
                  >
                    {useCase.buttonText}
                  </Button>
                </div>
              </>
            ) : (
              <>
                {/* 内容部分 */}
                <div className="flex-1 space-y-4">
                  <h3 className="text-2xl font-bold text-white">
                    {useCase.title}
                  </h3>
                  <p className="text-lg text-white/80 leading-relaxed">
                    {useCase.content}
                  </p>
                  <Button
                    onClick={scrollToTop}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-5 py-2.5 rounded-full transition-all duration-200 shadow-lg"
                  >
                    {useCase.buttonText}
                  </Button>
                </div>
                {/* 图片部分 */}
                <div className="flex-1">
                  <div className="relative w-full aspect-video rounded-2xl overflow-hidden">
                    <Image
                      src={useCase.imageUrl}
                      alt={useCase.imageAlt}
                      width={500}
                      height={300}
                      loading="lazy"
                      className="w-full h-full object-cover rounded-2xl"
                    />
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
