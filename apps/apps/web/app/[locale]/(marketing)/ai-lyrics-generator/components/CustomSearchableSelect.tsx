'use client'

import { useState, useRef, useEffect } from 'react'
import { ChevronDown, Check, Search } from 'lucide-react'
import { createPortal } from 'react-dom'
import { useTranslations } from 'next-intl'

interface CustomSearchableSelectProps {
  value: string
  onChange: (value: string) => void
  options: string[]
  placeholder?: string
  className?: string
}

export function CustomSearchableSelect({
  value,
  onChange,
  options,
  className = '',
}: CustomSearchableSelectProps) {
  const t = useTranslations('aiLyricsGen')
  const placeholder = t('selectOption')
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [highlightedIndex, setHighlightedIndex] = useState(-1)
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
  })
  const containerRef = useRef<HTMLDivElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // 过滤选项
  const filteredOptions = options.filter((option) =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const selectedOption = options.find((option) => option === value)

  // 计算下拉框位置
  const updateDropdownPosition = () => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect()
      setDropdownPosition({
        top: rect.bottom + window.scrollY + 8,
        left: rect.left + window.scrollX,
        width: rect.width,
      })
    }
  }

  // 打开下拉框
  const handleOpen = () => {
    setIsOpen(true)
    setSearchTerm('')
    setHighlightedIndex(-1)
    // 延迟计算位置，确保DOM已更新
    setTimeout(updateDropdownPosition, 0)
  }

  // 关闭下拉框
  const handleClose = () => {
    setIsOpen(false)
    setSearchTerm('')
    setHighlightedIndex(-1)
  }

  // 选择选项
  const handleSelect = (option: string) => {
    onChange(option)
    handleClose()
  }

  // 处理键盘导航
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setHighlightedIndex((prev) =>
          prev < filteredOptions.length - 1 ? prev + 1 : 0
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setHighlightedIndex((prev) =>
          prev > 0 ? prev - 1 : filteredOptions.length - 1
        )
        break
      case 'Enter':
        e.preventDefault()
        if (highlightedIndex >= 0 && filteredOptions[highlightedIndex]) {
          handleSelect(filteredOptions[highlightedIndex])
        }
        break
      case 'Escape':
        e.preventDefault()
        handleClose()
        break
    }
  }

  // 监听点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 检查点击的元素是否在下拉框内
      const target = event.target as Node
      const dropdownElement = document.querySelector('[data-dropdown="true"]')

      if (
        containerRef.current &&
        !containerRef.current.contains(target) &&
        !dropdownElement?.contains(target)
      ) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      updateDropdownPosition()
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, filteredOptions.length])

  // 搜索框获得焦点时重置高亮
  const handleSearchFocus = () => {
    setHighlightedIndex(-1)
  }

  return (
    <div ref={containerRef} className={`relative z-50 ${className}`}>
      {/* 触发器按钮 */}
      <button
        type="button"
        onClick={isOpen ? handleClose : handleOpen}
        className="w-full bg-white/10 border border-white/20 text-white rounded-2xl h-12 px-4 flex items-center justify-between hover:bg-white/15 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
      >
        <span
          className={`${
            selectedOption ? 'text-white' : 'text-gray-400'
          } truncate flex-1 text-left`}
        >
          {selectedOption || placeholder}
        </span>
        <ChevronDown
          className={`w-4 h-4 text-white transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      {/* 下拉框 */}
      {isOpen &&
        createPortal(
          <div
            data-dropdown="true"
            className="absolute z-[20] bg-gray-900/95 backdrop-blur-xl border border-white/30 rounded-2xl shadow-2xl overflow-hidden min-w-64 max-w-80"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              width: `${dropdownPosition.width}px`,
              maxHeight: '300px',
              transform: 'translateZ(0)', // 强制硬件加速
            }}
            onKeyDown={handleKeyDown}
          >
            {/* 搜索框 */}
            <div className="sticky top-0 bg-gray-900/95 backdrop-blur-xl border-b border-white/20 p-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder={t('search')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onFocus={handleSearchFocus}
                  className="w-full bg-white/10 border border-white/20 text-white placeholder:text-gray-400 rounded-lg pl-10 pr-3 py-2 text-sm focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
                  autoFocus
                />
              </div>
            </div>

            {/* 选项列表 */}
            <div className="max-h-[180px] overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option, index) => (
                  <button
                    key={option}
                    type="button"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleSelect(option)
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                    }}
                    className={`w-full px-4 py-3 text-left text-white hover:bg-purple-500/50 focus:bg-purple-500/50 transition-colors duration-150 ${
                      option === value ? 'bg-purple-500/70' : ''
                    } ${index === highlightedIndex ? 'bg-purple-500/50' : ''}`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="truncate">{option}</span>
                      {option === value && (
                        <Check className="w-4 h-4 text-purple-300 flex-shrink-0" />
                      )}
                    </div>
                  </button>
                ))
              ) : (
                <div className="py-3 px-4 text-gray-400 text-sm text-center">
                  No options found
                </div>
              )}
            </div>
          </div>,
          document.body
        )}
    </div>
  )
}
