'use client'

import React, { useEffect, useRef, useState } from 'react'

interface AnimationConfig {
  id: string
  threshold?: number
  delay?: number
  duration?: number
  direction?: 'up' | 'down' | 'left' | 'right' | 'fade' | 'scale'
  distance?: number
  ease?: string
  className?: string
}

interface ScrollAnimationManagerProps {
  children: React.ReactNode
  config: AnimationConfig
  onAnimationStart?: () => void
  onAnimationComplete?: () => void
}

const ScrollAnimationManager: React.FC<ScrollAnimationManagerProps> = ({
  children,
  config,
  onAnimationStart,
  onAnimationComplete,
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [hasAnimated, setHasAnimated] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true)
          setHasAnimated(true)
          if (onAnimationStart) {
            onAnimationStart()
          }

          // 延迟调用完成回调
          setTimeout(() => {
            if (onAnimationComplete) {
              onAnimationComplete()
            }
          }, config.duration || 800)
        }
      },
      {
        threshold: config.threshold || 0.1,
        rootMargin: '0px 0px -50px 0px',
      }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [
    config.threshold,
    hasAnimated,
    config.duration,
    onAnimationStart,
    onAnimationComplete,
  ])

  const getInitialTransform = () => {
    const distance = config.distance || 50
    switch (config.direction) {
      case 'up':
        return `translateY(${distance}px)`
      case 'down':
        return `translateY(-${distance}px)`
      case 'left':
        return `translateX(${distance}px)`
      case 'right':
        return `translateX(-${distance}px)`
      case 'scale':
        return 'scale(0.8)'
      case 'fade':
        return 'translateY(0px)'
      default:
        return `translateY(${distance}px)`
    }
  }

  const getFinalTransform = () => {
    switch (config.direction) {
      case 'up':
      case 'down':
      case 'left':
      case 'right':
        return 'translateY(0px) translateX(0px)'
      case 'scale':
        return 'scale(1)'
      case 'fade':
        return 'translateY(0px)'
      default:
        return 'translateY(0px) translateX(0px)'
    }
  }

  const getEaseFunction = () => {
    return config.ease || 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
  }

  return (
    <div
      ref={ref}
      className={`scroll-animation-container ${config.className || ''}`}
      style={{
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? getFinalTransform() : getInitialTransform(),
        transition: `all ${config.duration || 800}ms ${getEaseFunction()} ${
          config.delay || 0
        }ms`,
        willChange: 'opacity, transform',
      }}
      data-animation-id={config.id}
    >
      {children}
    </div>
  )
}

export default ScrollAnimationManager
