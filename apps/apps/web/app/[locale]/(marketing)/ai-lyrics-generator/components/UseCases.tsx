import { UseCasesInteractive } from './UseCasesInteractive'
import { useTranslations } from 'next-intl'

export function UseCases() {
  const t = useTranslations('aiLyricsGen')
  // 静态数据，可以在服务端渲染
  const useCases = [
    {
      title: t('writersBlock.title'),
      content: t('writersBlock.description'),
      buttonText: t('writersBlock.cta'),
      imageUrl:
        'https://aimakesong.oss-us-west-1.aliyuncs.com/seo_image/3308bb51-3f87-4e2f-bab7-2afbc05766eb.jpeg',
      imageAlt: t('imageDescriptions.musicianCreative'),
      isReversed: false,
    },
    {
      title: t('rapLyrics.title'),
      content: t('rapLyrics.description'),
      buttonText: t('rapLyrics.cta'),
      imageUrl:
        'https://aimakesong.oss-us-west-1.aliyuncs.com/seo_image/5ca741a0-2fa4-4d57-967d-4efe11b76896.jpeg',
      imageAlt: t('imageDescriptions.rapperPerforming'),
      isReversed: true,
    },
    {
      title: t('loveSong.title'),
      content: t('loveSong.description'),
      buttonText: t('loveSong.cta'),
      imageUrl:
        'https://aimakesong.oss-us-west-1.aliyuncs.com/seo_image/9b9d3011-c40a-47fc-ae4f-1e02c424d625.jpeg',
      imageAlt: t('imageDescriptions.loveSongCouple'),
      isReversed: false,
    },
    {
      title: t('structure.title'),
      content: t('structure.description'),
      buttonText: t('structure.cta'),
      imageUrl:
        'https://aimakesong.oss-us-west-1.aliyuncs.com/seo_image/d8750238-662b-4680-8e65-18fc85a54333.jpeg',
      imageAlt: t('imageDescriptions.inspiredWriter'),
      isReversed: true,
    },
  ]
  return (
    <div className="bg-gradient-to-b from-gray-900/80 via-[#131313]/60 to-[#131313] backdrop-blur-xl p-8">
      <div className="container mx-auto px-4">
        {/* 主标题 */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-4">
            {t('applications.title')}
          </h2>
          <p className="text-gray-300 max-w-2xl mx-auto text-lg leading-relaxed">
            {t('applications.description')}
          </p>
        </div>

        {/* 交互组件 */}
        <UseCasesInteractive useCases={useCases} />
      </div>
    </div>
  )
}
