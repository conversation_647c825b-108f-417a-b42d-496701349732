import { useTranslations } from 'next-intl'
import SplitMusicClient from './components/SplitMusicClient'
import { getTranslations } from 'next-intl/server'
import HowTo from './components/HowTo'
import FeatureSection from './components/FeatureSection'
import FaqSection from './components/FaqSection'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('splitterPageTitle'),
    description: t('splitterPageDesc'),
    keywords: t('splitterPageKeywords'),
  }
}

export default function CreatePage() {
  const t = useTranslations()

  return (
    <div className="w-full min-h-screen bg-gradient-to-b from-gray-900 to-[#120724]">
      {/* <LeftNav> */}
      <div className="px-6  pb-8 h-full overflow-y-auto">
        <div className="max-w-3xl mx-auto">
          {/* 标题区域 */}
          <div className="space-y-4 mb-12 mt-24">
            <h1 className="mx-auto text-white max-4xl text-balance font-bold text-3xl sm:text-4xl md:text-5xl lg:text-7xl sm:pb-8 xl:pb-8  animate-floating text-center">
              {t('splitterAI')}
            </h1>
            <p className="text-lg font-bold text-white mb-4 px-2">
              {t('splitMusicDesc')}
            </p>
          </div>
          {/* 主内容区域 */}
          <SplitMusicClient />
        </div>
        <HowTo />
        <FeatureSection />
        <FaqSection />
      </div>
      {/* </LeftNav> */}
    </div>
  )
}
