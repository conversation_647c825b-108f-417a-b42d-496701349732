'use client'

import { useState, useRef, useEffect } from 'react'
import { Upload } from './Upload'

import { WaveformLoader } from './WaveformLoader'
import { StemTrack } from './StemTrack'
import { detectMobileDevice, getUserId } from '../../../../utils/lib'
import { useTranslations } from 'next-intl'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { POINTS_CONFIG } from '../../../../../constants'
import { validateUserForGeneration } from '@/utils/userValidation'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
// import PricingSection from '../../(home)/components/PricingSection'

const UploadWithNoSSR = dynamic(
  () => import('./Upload').then((mod) => mod.Upload),
  {
    ssr: false,
    // 最好提供一个加载时的占位符
    loading: () => <p>Loading uploader...</p>,
  }
)

const PricingSectionWithNoSSR = dynamic(
  () => import('../../(home)/components/PricingSection'),
  { ssr: false }
)

import { consumeUserPoints } from '@/utils/pointsService'
import toast from 'react-hot-toast'
import dynamic from 'next/dynamic'

interface StemResult {
  id: string
  title: string
  status: number
  progress: number
  progressMsg: string
  cld2AudioUrl: string
}

export async function splitAudio(file: File, userId: string) {
  try {
    const formData = new FormData()
    formData.append('file', file)

    const headers = {
      'x-token': 'sk-f52e6bfc417c41338e7c0cc21dbeefc9',
      'x-userId': userId || '413564',
    }
    const API_BASE_URL = 'https://dzwlai.com/apiuser/_open/suno/music'

    const response = await fetch(`${API_BASE_URL}/stemsByAudio`, {
      method: 'POST',
      headers: headers,
      body: formData,
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error splitting audio:', error)
    toast.error('Error splitting audio, please try again later.')
    return { code: 500, msg: 'Error splitting audio' }
  }
}

export default function SplitMusicClient() {
  const t = useTranslations()
  const [isUploading, setIsUploading] = useState(false)
  const [taskBatchId, setTaskBatchId] = useState<string>('')
  const [stems, setStems] = useState<StemResult[]>([])
  const [originalAudio, setOriginalAudio] = useState<string>('')
  const [error, setError] = useState('')
  const [currentFile, setCurrentFile] = useState<File | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isProcessed, setIsProcessed] = useState(false)
  const [payDialogTitle, setPayDialogTitle] = useState('')
  const [isMobile, setIsMobile] = useState(false)
  const user = getUserFromClientCookies()
  const userInfoRef = useRef(null)

  const pollInterval = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    setIsMobile(detectMobileDevice().isMobile)
  }, [])

  useEffect(() => {
    return () => {
      if (originalAudio) {
        URL.revokeObjectURL(originalAudio)
      }
      stopPolling()
    }
  }, [originalAudio])

  const handleFileUpload = async (file: File) => {
    if (file.size > 10 * 1024 * 1024) {
      setError(t('errorFileSize'))
      return
    }

    setIsUploading(true)
    setError('')
    setCurrentFile(file)
    setIsProcessed(false)
    setStems([])

    try {
      const localUrl = URL.createObjectURL(file)
      setOriginalAudio(localUrl)
    } catch (err) {
      setError(t('errorLoadFile'))
    } finally {
      setIsUploading(false)
    }
  }

  // 移动端简洁提示函数
  const showMobileSubscriptionPrompt = (message) => {
    // 创建模态框元素
    const modalContainer = document.createElement('div')
    modalContainer.className =
      'fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4'
    modalContainer.style.animation = 'fadeIn 0.3s ease-out'

    // 模态框内容
    modalContainer.innerHTML = `
      <div class="bg-[#1a1d24] border border-gray-800 rounded-xl p-6 max-w-md w-full shadow-xl" 
           style="animation: scaleIn 0.3s ease-out">
        <div class="flex flex-col">
          <div class="w-12 h-12 mx-auto mb-4 bg-purple-500/20 rounded-full flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-purple-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-white text-center mb-2">Need to upgrade</h3>
          <p class="text-gray-300 text-center mb-5">${message}</p>
          <button id="subscribeButton" 
                  class="w-full py-3 px-4 rounded-lg font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-all">
            Subscribe immediately
          </button>
          <button id="closeButton"
                  class="w-full py-3 px-4 rounded-lg font-medium bg-transparent text-gray-400 hover:text-white mt-3">
            Close
          </button>
        </div>
      </div>
    `

    // 添加样式
    const style = document.createElement('style')
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      @keyframes scaleIn {
        from { transform: scale(0.95); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
      }
    `
    document.head.appendChild(style)

    // 添加到DOM
    document.body.appendChild(modalContainer)
    document.body.style.overflow = 'hidden' // 防止背景滚动

    // 添加事件监听
    setTimeout(() => {
      const subscribeButton = document.getElementById('subscribeButton')
      const closeButton = document.getElementById('closeButton')

      subscribeButton.addEventListener('click', () => {
        window.open('/pricing', '_blank')
        document.body.removeChild(modalContainer)
        document.body.style.overflow = ''
      })

      closeButton.addEventListener('click', () => {
        document.body.removeChild(modalContainer)
        document.body.style.overflow = ''
      })

      // 点击背景关闭
      modalContainer.addEventListener('click', (e) => {
        if (e.target === modalContainer) {
          document.body.removeChild(modalContainer)
          document.body.style.overflow = ''
        }
      })
    }, 100)
  }

  const handleRemoveVocals = async () => {
    if (!currentFile) {
      setError(t('errorNoFile'))
      return
    }

    //

    if (user) {
      const validationResult: any = await validateUserForGeneration(
        user,
        POINTS_CONFIG.MUSIC_GENERATION
      )
      console.log('🚀 ~ validationResult:', validationResult)
      if (!validationResult.success) {
        // 弹支付的弹窗（poe写，要大方优雅）
        // 我希望弹窗界面是一个组件，支持传入一个message展示信息，其他地方也可以调用
        // 展示之后页面的布局
        // 头部： 提示内容为 validationResult.message
        // 内容： 套餐（就是之前写的组件复用）

        // isMobile
        if (isMobile) {
          showMobileSubscriptionPrompt(validationResult.message)
        } else {
          setPayDialogTitle(validationResult.message)
        }

        return
      }

      userInfoRef.current = validationResult.userInfo
    }

    setIsProcessing(true)
    setError('')

    try {
      const userId = getUserId()
      const result = await splitAudio(currentFile, userId)

      if (result.code === 200) {
        setTaskBatchId(result.data.taskBatchId)
        startPolling(result.data.taskBatchId)

        const consumeResult = await consumeUserPoints(
          {
            ...user,
            // @ts-ignore
            id: userInfoRef.current.id,
          },
          POINTS_CONFIG.MUSIC_GENERATION
        )
        console.log('faith=============消耗积分成功', consumeResult)
      } else {
        setError(result.msg || t('errorProcessing'))
      }
    } catch (err) {
      setError(t('errorProcessingRetry'))
    }
  }

  const checkProgress = async (batchId: string) => {
    try {
      const response = await fetch(
        `/api/music/status?taskBatchId=${batchId}&user_id=${getUserId()}`,
        {
          cache: 'no-store',
        }
      )

      if (!response.ok) {
        throw new Error(t('errorFetchStatus'))
      }

      const result = await response.json()

      if (result.code === 200) {
        setStems(result.data.items)

        if (result.data.items.every((item: any) => item.status === 30)) {
          stopPolling()
          setIsProcessed(true)
          setIsProcessing(false)
        }

        if (result.data.items.every((item: any) => item.status === 40)) {
          toast.error(t('checkFileFormat'))
          stopPolling()
          setIsProcessed(true)
          setIsProcessing(false)
          setStems([])
          setOriginalAudio('')
          setCurrentFile(null)
        }
      }
    } catch (err) {
      setError(t('errorCheckProgress'))
      stopPolling()
    }
  }

  const startPolling = (batchId: string) => {
    stopPolling()
    pollInterval.current = setInterval(() => checkProgress(batchId), 2000)
  }

  const stopPolling = () => {
    if (pollInterval.current) {
      clearInterval(pollInterval.current)
      pollInterval.current = null
    }
  }

  useEffect(() => {
    return () => stopPolling()
  }, [])

  console.log('faith=============payDialogTitle', payDialogTitle)

  return (
    <div className="space-y-6">
      <div className="relative">
        {currentFile ? (
          <div className="border-2 border-dashed border-gray-700 rounded-lg p-8 text-center">
            <p className="text-gray-300 mb-2">
              {t('currentFile')} {currentFile.name}
            </p>
            <p className="text-gray-500 text-sm">
              {t('fileSize')} {(currentFile.size / (1024 * 1024)).toFixed(2)}MB
            </p>
          </div>
        ) : (
          // <div></div>
          <UploadWithNoSSR
            isUploading={isUploading}
            onUpload={handleFileUpload}
            error={error}
          />
        )}

        {(isUploading || isProcessing) && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm rounded-lg">
            <WaveformLoader />
          </div>
        )}
      </div>

      <div className="flex justify-center">
        <button
          onClick={handleRemoveVocals}
          disabled={!currentFile || isProcessing || isUploading}
          className={`w-full mt-4 py-4 rounded-lg font-medium transition-opacity
      ${
        !currentFile || isProcessing || isUploading
          ? 'bg-gray-600 cursor-not-allowed'
          : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90'
      }`}
        >
          {isProcessing ? t('common.processing') : t('buttonRemoveVocals')}
        </button>
      </div>

      {stems.length > 0 && (
        <div className="space-y-6">
          <div className="border border-gray-700 rounded-lg p-4 bg-gray-800/50">
            <h3 className="text-lg font-medium text-purple-400 mb-2">
              {t('titleOriginalTrack')}
            </h3>
            <StemTrack
              key="original"
              stem={{
                id: 'original',
                title: t('originalTrackTitle'),
                status: 30,
                progress: 100,
                progressMsg: 'Complete',
                cld2AudioUrl: originalAudio,
              }}
            />
          </div>

          <div className="border border-gray-700 rounded-lg p-4 bg-gray-800/50">
            <h3 className="text-lg font-medium text-purple-400 mb-2">
              {t('titleSeparatedTracks')}
            </h3>
            <div className="space-y-4">
              {stems.map((stem) => (
                <StemTrack key={stem.id} stem={stem} />
              ))}
            </div>
          </div>
        </div>
      )}

      {payDialogTitle && (
        <Dialog
          open={!!payDialogTitle}
          onOpenChange={(open) =>
            setPayDialogTitle((title) => (open ? title : ''))
          }
        >
          <DialogContent
            className="
          max-w-[1200px] 
          w-full 
          h-[920px]
          bg-[#1A1B1E] 
          border-[#2D2E32]
          shadow-xl
        "
          >
            {/* <DialogHeader>
              <DialogTitle className="text-white">
                {t('insufficientCredits')}
              </DialogTitle>
            </DialogHeader> */}
            <div className="w-full bg-red-500/10 border border-red-500/20 rounded-xl p-4">
              <p className="text-center font-semibold text-xl text-purple-300">
                <span className="text-red-500">{payDialogTitle}</span>
              </p>
            </div>
            <PricingSectionWithNoSSR
              needTitle={true}
              className="leading-none"
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
