import { getTranslations } from 'next-intl/server'

const FaqSection = async () => {
  const t = await getTranslations()

  const faqs = [
    {
      id: '01',
      question: t('FaqSectionPro.faqData.k.question'),
      answer: t('FaqSectionPro.faqData.k.answer'),
    },
    {
      id: '02',
      question: t('FaqSectionPro.faqData.l.question'),
      answer: t('FaqSectionPro.faqData.l.answer'),
    },
    {
      id: '03',
      question: t('FaqSectionPro.faqData.m.question'),
      answer: t('FaqSectionPro.faqData.m.answer'),
    },
    {
      id: '04',
      question: t('FaqSectionPro.faqData.n.question'),
      answer: t('FaqSectionPro.faqData.n.answer'),
    },
    {
      id: '05',
      question: t('FaqSectionPro.faqData.o.question'),
      answer: t('FaqSectionPro.faqData.o.answer'),
    },
    {
      id: '06',
      question: t('FaqSectionPro.faqData.p.question'),
      answer: t('FaqSectionPro.faqData.p.answer'),
    },
    {
      id: '07',
      question: t('FaqSectionPro.faqData.q.question'),
      answer: t('FaqSectionPro.faqData.q.answer'),
    },
  ]

  return (
    <section className="max-w-7xl mx-auto px-4 py-16">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 text-purple-500 !leading-tight">
          {t('faq.title')}
        </h2>
        <p className="text-gray-400 text-lg md:text-xl">{t('aboutFaqDesc')}</p>
      </div>

      <div className="grid gap-6 max-w-4xl mx-auto">
        {faqs.map((faq) => (
          <div
            key={faq.id}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 hover:border-purple-500/50 transition-colors"
          >
            <h3 className="text-xl font-semibold mb-4 text-white">
              <span className="text-purple-400">{faq.id}. </span>
              {faq.question}
            </h3>
            <p className="text-gray-400 leading-relaxed">{faq.answer}</p>
          </div>
        ))}
      </div>

      <div className="text-center mt-12 text-gray-400">
        <p>
          {t('pricingFaq.support.text')}{' '}
          <a
            href="mailto:<EMAIL>"
            className="text-purple-400 hover:text-purple-300 transition-colors"
          >
            <EMAIL>
          </a>
        </p>
      </div>
    </section>
  )
}

export default FaqSection
