import { getTranslations } from 'next-intl/server'

const FeatureSection = async () => {
  const t = await getTranslations()
  const features = [
    {
      icon: '🎼',
      title: t('highFidelityTitle'),
      description: t('highFidelityDesc'),
    },
    {
      icon: '⚡',
      title: t('instantProcessTitle'),
      description: t('instantProcessDesc'),
    },
    {
      icon: '🔥',
      title: t('perfectForTitle'),
      description: t('perfectForDesc'),
    },
    {
      icon: '🔒',
      title: t('secureTitle'),
      description: t('secureDesc'),
    },
    {
      icon: '📂',
      title: t('formatTitle'),
      description: t('formatDesc'),
    },
    {
      icon: '💰',
      title: t('tryFreeTitle'),
      description: t('tryFreeDesc'),
    },
  ]

  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-16">
      {/* 头部标题 */}
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold text-purple-500 mb-6">
          {t('aiVocalHeader')}
        </h2>
        <p className="text-gray-300 text-lg max-w-3xl mx-auto">
          {t('aiVocalIntro')}
        </p>
      </div>

      {/* 特性网格 */}
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {features.map((feature, index) => (
          <div
            key={index}
            className="relative bg-gray-900/80 backdrop-blur rounded-xl p-6 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/5"
          >
            <div className="flex items-center gap-4 mb-4 min-h-12">
              <div className="text-2xl">{feature.icon}</div>
              <h3 className="text-lg font-semibold text-purple-400">
                {feature.title}
              </h3>
            </div>
            <p className="text-gray-300">{feature.description}</p>
          </div>
        ))}
      </div>
    </div>
  )
}

export default FeatureSection
