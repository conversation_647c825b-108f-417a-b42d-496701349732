import { getTranslations } from 'next-intl/server'
import { Upload, Music, Download } from 'lucide-react'

const VocalRemover = async () => {
  const t = await getTranslations()
  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-8 md:py-16">
      <div className="text-center mb-8 md:mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-purple-500 mb-4 md:mb-6">
          {t('vocalRemovalTitle')}
        </h2>

        <p className="text-gray-300 text-base md:text-lg max-w-2xl mx-auto px-2">
          {t('vocalRemovalIntro')}
        </p>
      </div>

      <div className="grid gap-10 md:gap-8 sm:grid-cols-2 md:grid-cols-3">
        {/* Step 1 */}
        <div className="relative group h-auto min-h-[280px]">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 to-pink-600/20 rounded-xl transform group-hover:scale-105 transition-transform duration-300"></div>
          <div className="relative h-full bg-gray-900/80 backdrop-blur rounded-xl p-4 sm:p-6 md:p-8 border border-purple-500/20 hover:border-purple-500/40 transition-colors duration-300 flex flex-col">
            <div className="absolute -top-6 left-1/2 -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/20">
              <Upload className="w-5 h-5 md:w-6 md:h-6 text-white" />
            </div>
            <h3 className="text-lg md:text-xl font-bold text-purple-400 my-4 text-center pt-2">
              {t('step1Title')}
            </h3>
            <ul className="space-y-2 md:space-y-3 text-sm md:text-base text-gray-300 flex-grow">
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                <span className="flex-1">{t('step1Action')}</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                <span className="flex-1">{t('step1Desc')}</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Step 2 */}
        <div className="relative group h-auto min-h-[280px]">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 to-pink-600/20 rounded-xl transform group-hover:scale-105 transition-transform duration-300"></div>
          <div className="relative h-full bg-gray-900/80 backdrop-blur rounded-xl p-4 sm:p-6 md:p-8 border border-purple-500/20 hover:border-purple-500/40 transition-colors duration-300 flex flex-col">
            <div className="absolute -top-6 left-1/2 -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/20">
              <Music className="w-5 h-5 md:w-6 md:h-6 text-white" />
            </div>
            <h3 className="text-lg md:text-xl font-bold text-purple-400 my-4 text-center pt-2">
              {t('step2Title')}
            </h3>
            <ul className="space-y-2 md:space-y-3 text-sm md:text-base text-gray-300 flex-grow">
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                <span className="flex-1">{t('step2Action')}</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                <span className="flex-1">{t('step2Desc')}</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Step 3 */}
        <div className="relative group h-auto min-h-[280px] sm:col-span-2 md:col-span-1 mx-auto w-full max-w-md md:max-w-none">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 to-pink-600/20 rounded-xl transform group-hover:scale-105 transition-transform duration-300"></div>
          <div className="relative h-full bg-gray-900/80 backdrop-blur rounded-xl p-4 sm:p-6 md:p-8 border border-purple-500/20 hover:border-purple-500/40 transition-colors duration-300 flex flex-col">
            <div className="absolute -top-6 left-1/2 -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/20">
              <Download className="w-5 h-5 md:w-6 md:h-6 text-white" />
            </div>
            <h3 className="text-lg md:text-xl font-bold text-purple-400 my-4 text-center pt-2">
              {t('step3Title')}
            </h3>
            <ul className="space-y-2 md:space-y-3 text-sm md:text-base text-gray-300 flex-grow">
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                <span className="flex-1">{t('step3Action')}</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                <span className="flex-1">{t('step3Desc')}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default VocalRemover
