'use client'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useTranslations } from 'next-intl'
import { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import { LoginForm } from '@saas/auth/components/LoginForm'
import { detectMobileDevice } from '@/utils/lib'

interface UploadProps {
  isUploading: boolean
  onUpload: (file: File) => void
  error: string
}

export function Upload({ isUploading, onUpload, error }: UploadProps) {
  const t = useTranslations()
  const user = getUserFromClientCookies()
  const [showLoginDialog, setShowLoginDialog] = useState(false)

  const handleUpload = (file: File) => {
    if (!user) {
      setShowLoginDialog(true)
      return
    }
    onUpload(file)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const file = e.dataTransfer.files[0]
    if (file) handleUpload(file)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) handleUpload(file)
  }

  return (
    <>
      <div
        className="border-2 border-dashed border-gray-600 rounded-lg p-12 text-center"
        onDragOver={(e) => e.preventDefault()}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id="file"
          className="hidden"
          onChange={handleChange}
          accept="audio/*"
          disabled={isUploading}
        />
        <label
          htmlFor="file"
          className="cursor-pointer text-gray-400 hover:text-white"
        >
          <div className="space-y-2">
            <div className="text-4xl">🎵</div>
            <div>
              {detectMobileDevice().isMobile
                ? t('uploadPromptMobile')
                : t('uploadPrompt')}
            </div>
            <div className="text-sm text-gray-500">{t('supportedFormats')}</div>
          </div>
        </label>
        {error && <div className="mt-4 text-red-500">{error}</div>}
      </div>

      <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
        <DialogContent
          className="
          w-full 
          pb-0
          bg-[#1A1B1E] 
          border-[#2D2E32]
          shadow-xl
        "
        >
          <DialogHeader>
            <DialogTitle className="text-white">
              {t('loginForFreeTrial')}
            </DialogTitle>
          </DialogHeader>
          <LoginForm needBackground={false} />
        </DialogContent>
      </Dialog>
    </>
  )
}
