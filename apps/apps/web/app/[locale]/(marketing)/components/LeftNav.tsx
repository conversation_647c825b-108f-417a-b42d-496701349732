'use client'

import { useEffect, useState } from 'react'
import { Music, Speaker, Volume2, Crown } from 'lucide-react'

import { cn } from '@ui/lib'
import { usePathname, useRouter } from 'next/navigation' // 添加路由导入
import { activeItemAtom } from '@saas/stores'
import { useAtom, useAtomValue } from 'jotai'
import { useTranslations } from 'next-intl'

const colors = {
  primary: '#DA62C4',
  primaryFrom: '#9F3BF9',
  primaryTo: '#DA62C4',
  textGray: '#4B5563', // 更暗的灰色
} as const

interface LeftNavProps {
  children: React.ReactNode
}

export function LeftNav({ children }: LeftNavProps) {
  const t = useTranslations()
  const pathname = usePathname()

  const [isExpanded, setIsExpanded] = useState(false)
  const [activeItem, setActiveItem] = useAtom(activeItemAtom)

  useEffect(() => {
    const active = navItems.find((item) => pathname.includes(item.href))
    if (active) {
      setActiveItem(active.href)
    }
  }, [])

  const router = useRouter() // 初始化路由

  const navItems = [
    {
      icon: <Music className="h-6 w-6" />,
      label: t('sonicMaker'),
      href: '/ai-music-generator',
    },
    {
      icon: <Speaker className="h-6 w-6" />,
      label: t('vocalIsolation'),
      href: '/ai-vocal-remover',
    },
    // {
    //   icon: <Volume2 className='h-6 w-6' />,
    //   label: 'Free sound effects',
    //   href: '#'
    // }
  ]

  const handleNavClick = (href: string) => {
    setActiveItem(href)
    router.push(href)
  }

  return (
    <div className="flex">
      <div
        className={cn(
          'h-screen fixed left-0 top-0 z-40 flex flex-col bg-[#212936] transition-all duration-300 ease-in-out',
          'border-r pt-24',
          isExpanded ? 'w-60' : 'w-16'
        )}
        onMouseEnter={() => setIsExpanded(true)}
        onMouseLeave={() => setIsExpanded(false)}
      >
        <div className="flex flex-col space-y-2">
          {' '}
          {/* 增加间距从 space-y-1 到 space-y-2 */}
          {navItems.map((item, index) => (
            <button
              key={index}
              // onClick={() => setActiveItem(index)}
              onClick={() => handleNavClick(item.href)}
              className={cn(
                'flex items-center px-4 py-3 w-full text-left relative text-base', // 增加 padding-y 从 py-2 到 py-3
                activeItem === item.href
                  ? `text-[${colors.primary}]`
                  : `text-[${colors.textGray}] hover:bg-gray-800 hover:text-white`
              )}
            >
              {activeItem === item.href && (
                <div
                  className="absolute left-0 top-0 h-full w-0.5"
                  style={{ backgroundColor: colors.primary }}
                />
              )}
              <span
                className={cn(
                  'flex items-center justify-center w-8',
                  activeItem === item.href && `text-[${colors.primary}]`
                )}
              >
                {item.icon}
              </span>
              <span
                className={cn(
                  'ml-3 whitespace-nowrap overflow-hidden transition-all duration-300',
                  isExpanded ? 'opacity-100 w-auto' : 'opacity-0 w-0'
                )}
              >
                {item.label}
              </span>
            </button>
          ))}
        </div>

        <div className="mt-auto mb-4">
          <button
            className="flex items-center px-4 py-3 w-full text-left text-base hover:opacity-90" // 同样增加 padding-y
          >
            <span className="flex items-center justify-center w-8">
              <Crown className="h-6 w-6" />
            </span>
            <span
              onClick={() => router.push('/pricing')}
              className={cn(
                'ml-3 whitespace-nowrap overflow-hidden transition-all duration-300',
                isExpanded ? 'opacity-100 w-auto' : 'opacity-0 w-0'
              )}
            >
              {t('upgradeNow')}
            </span>
          </button>
        </div>
      </div>

      <div className="flex-1 ml-16">{children}</div>
    </div>
  )
}
