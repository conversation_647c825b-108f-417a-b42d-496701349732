import { PostListItem } from '@marketing/blog/components/PostListItem'
import { allPosts } from 'content-collections'
import { getLocale, getTranslations } from 'next-intl/server'

export async function generateMetadata() {
  const t = await getTranslations()

  return {
    title: t('blogSeo.siteTitle'),
    description: t('blogSeo.siteDescription'),
    keywords: t('blogSeo.keywords'),
  }
}

// Music Production Tips  170、19
// How to Make Music 4400, 38
// Music Making Tips 110、18
// how to produce music 1000,45

export default async function BlogListPage() {
  const locale = await getLocale()
  const t = await getTranslations()
  return (
    <div className="container max-w-6xl pt-32 pb-16">
      <div className="mb-12 text-center max-w-3xl mx-auto">
        <h1 className="font-bold text-4xl md:text-5xl leading-tight tracking-tight text-white mb-4">
          {t('blogSeo.howToMakeMusic')}
        </h1>
        <h2 className="text-xl md:text-2xl font-semibold text-purple-400 mb-6">
          {t('blogSeo.musicProductionTipsTutorials')}
        </h2>

        <p className="text-base md:text-lg text-gray-300 leading-relaxed">
          {t('blogSeo.musicProductionIntro')}
        </p>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        {allPosts
          .filter((post) => post.published && locale === post.locale)
          .sort(
            (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
          )
          .map((post) => (
            <PostListItem post={post} key={post.path} />
          ))}
      </div>
    </div>
  )
}
