// components/ClientWrapper.jsx
'use client'

import { useEffect, useState } from 'react'
import CreatePageClient from '../CreatePageClient'
import { detectMobileDevice } from '@/utils/lib'
import CreatePageMobileClient from '../CreatePageMobileClient'
import { usePathname, useSearchParams } from 'next/navigation'

export default function ClientWrapper({ batchId }: { batchId: string }) {
  const [isMobile, setIsMobile] = useState(false)
  const pathname = typeof window !== 'undefined' ? window.location.pathname : ''
  const search = typeof window !== 'undefined' ? window.location.search : ''
  let activeTab = 'create'
  if (pathname.includes('myWorks') || search.includes('myWorks')) {
    activeTab = 'myWorks'
  }

  useEffect(() => {
    setIsMobile(detectMobileDevice().isMobile)
  }, [])

  console.log('activeTab', activeTab)

  return (
    <div className="px-2 h-full">
      {!isMobile ? (
        <div className="pt-24 h-full overflow-auto scroll-container">
          <CreatePageClient batchId={batchId} />
        </div>
      ) : (
        <div className="px-1 pt-20 h-full overflow-hidden scroll-container">
          <CreatePageMobileClient activeTab={activeTab} batchId={batchId} />
        </div>
      )}
    </div>
  )
}
