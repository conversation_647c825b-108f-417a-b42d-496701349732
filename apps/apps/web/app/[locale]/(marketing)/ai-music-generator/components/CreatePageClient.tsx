'use client'

import { But<PERSON> } from '@ui/components/button'
import { cn } from '@ui/lib'
import { useEffect, useRef, useState } from 'react'
import MusicEdit, { TaskResponse } from '../../(home)/components/MusicEdit'
import { MusicPlayer } from './MusicPlayer'
import * as Tooltip from '@radix-ui/react-tooltip'
import { getPresetResponse } from './json/presetResponses'
import { Download, Loader2, Share2, Trash2, Play, Volume2 } from 'lucide-react'
import { useTranslations, useLocale } from 'next-intl'
import { getUserFromClientCookies } from '../../../../utils/client-cookies'
import ShowLoginModal from '@shared/components/ShowLoginModal'
import { getUserId, isUserLoggedIn } from '../../../../utils/lib'
import Cookies from 'js-cookie'
import PaymentModal from './PaymentModal'
import toast from 'react-hot-toast'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogCancel,
  AlertDialogDescription,
} from '@ui/components/alert-dialog'
import ShareMusicModal from './ShareMusicModal'
import { LoaderIcon } from 'lucide-react'

// 获取音乐
// 如果 cld2AudioUrl 的格式如下，说明是流式音乐，还没完全生成完毕
//  "cld2AudioUrl": "https://audiopipe.suno.ai/?item_id=cb8fc763-1469-493b-87de-f86467e100fe",
// 如果 cld2AudioUrl 的格式结尾为 .mp3 说明已经生成完毕
//  "cld2AudioUrl": "https://file.dzwlai.com/suno/music/api/000/008/400/cb8fc763-1469-493b-87de-f86467e100fe.mp3?v=30",

interface Props {
  batchId?: string
}

interface TaskItem {
  id: string
  inputType: string
  prompt: string
  title: string
  tags: string
  clipId: string
  progress: number
  continueClipId: string
  status: number
  cld2AudioUrl: string
  cld2VideoUrl: string
  progressMsg: string
  cld2ImageUrl: string
}

export default function CreatePage({ batchId }: Props) {
  const t = useTranslations()
  const locale = useLocale()
  const user = getUserFromClientCookies()
  const [selectedSong, setSelectedSong] = useState<string | null>(null)
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [batchIdItems, setBatchIdItems] = useState<any>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [userMusics, setUserMusics] = useState<TaskItem[]>([])
  const [userMusicLoading, setUserMusicLoading] = useState(false)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [currentId, setCurrentId] = useState<string | null>(null)
  const [shareItem, setShareItem] = useState<TaskItem | null>(null)
  const [showShareModal, setShowShareModal] = useState(false)
  const [isSharing, setIsSharing] = useState(false)
  const [shouldAutoPlay, setShouldAutoPlay] = useState(false)
  const [providerId, setProviderId] = useState<string | null>(null)

  const [downloadingStates, setDownloadingStates] = useState<
    Record<string, boolean>
  >({})

  const currentSelectItem = useRef<any>(null)

  // 判断是否是模拟的数据，主页上面的
  const isPresetId = (id: string) => {
    return /^1074794245686558(00[1-8])$/.test(id)
  }

  const fetchUserMusics = async () => {
    if (isUserLoggedIn() === false) return

    try {
      setUserMusicLoading(true)
      // 添加分页参数
      const response = await fetch(
        `/api/music/myMusics?user_id=${Cookies.get(
          'userEmail'
        )}&pageNum=1&pageSize=200`
      )

      if (!response.ok) throw new Error('Failed to fetch user music history')
      const data = await response.json()

      console.log('data---', data)

      const items = data.data
      setUserMusics(items || [])
    } catch (err) {
      console.error('Error fetching user music history:', err)
    } finally {
      setUserMusicLoading(false)
    }
  }

  // 单独处理用户音乐历史
  useEffect(() => {
    fetchUserMusics()
  }, [])

  const handleParams = async () => {
    if (!batchId) return

    //   {
    //     "id": "1912071433513668609",
    //     "inputType": "20",
    //     "makeInstrumental": false,
    //     "prompt": "Love and peace, let it shine bright\nGuiding us through the darkest night\n\nI see the world through loving eyes\nPeace within is my true prize\nNo more hatred, no more fear\nWhen we're united, love is near\nSpread your wings and let it soar\nBringing light to every shore\nHarmony for one and all\nTear down the dividing wall\n\nLove and peace will guide our way\nMaking a better world each day\nRising like the morning sun\nOur hearts and souls become as one\n\nNot just words upon our tongue\nBut a sweet celestial song\nEchoing across the land\nJoin with me, let's take a stand\nWar and strife we cast aside\nLet the gentle stream abide\nTruth will cut through the night\nWith love and peace as our guiding light\n\n As we walk this winding road\n Love's the burden we'll carry's load\n Let your spirit feel the breeze\n Of eternal love and peace\n\nLove and peace are here to stay\nVanquishing the night and day\nTill the whole world knows their names\nWe'll never cease these lofty aims",
    //     "title": "Mad",
    //     "tags": "Punk",
    //     "clipId": "48c3506f-f63d-41cb-be65-3ed176f136b1",
    //     "duration": 240,
    //     "progress": 100,
    //     "waitNum": 0,
    //     "continueClipId": "",
    //     "status": 30,
    //     "cld2AudioUrl": "https://cdn1.suno.ai/48c3506f-f63d-41cb-be65-3ed176f136b1.mp3",
    //     "progressMsg": "Production completed",
    //     "cld2ImageUrl": "https://cdn2.suno.ai/image_48c3506f-f63d-41cb-be65-3ed176f136b1.jpeg"
    // }

    //   {
    //     "id": "1912071433513668610",
    //     "inputType": "20",
    //     "makeInstrumental": false,
    //     "prompt": "Love and peace, let it shine bright\nGuiding us through the darkest night\n\nI see the world through loving eyes\nPeace within is my true prize\nNo more hatred, no more fear\nWhen we're united, love is near\nSpread your wings and let it soar\nBringing light to every shore\nHarmony for one and all\nTear down the dividing wall\n\nLove and peace will guide our way\nMaking a better world each day\nRising like the morning sun\nOur hearts and souls become as one\n\nNot just words upon our tongue\nBut a sweet celestial song\nEchoing across the land\nJoin with me, let's take a stand\nWar and strife we cast aside\nLet the gentle stream abide\nTruth will cut through the night\nWith love and peace as our guiding light\n\n As we walk this winding road\n Love's the burden we'll carry's load\n Let your spirit feel the breeze\n Of eternal love and peace\n\nLove and peace are here to stay\nVanquishing the night and day\nTill the whole world knows their names\nWe'll never cease these lofty aims",
    //     "title": "Mad",
    //     "tags": "Punk",
    //     "clipId": "589daf17-5ab7-4ac5-a34e-86a982f361a0",
    //     "duration": 232.76,
    //     "progress": 100,
    //     "waitNum": 0,
    //     "continueClipId": "",
    //     "status": 30,
    //     "cld2AudioUrl": "https://cdn1.suno.ai/589daf17-5ab7-4ac5-a34e-86a982f361a0.mp3",
    //     "progressMsg": "Production completed",
    //     "cld2ImageUrl": "https://cdn2.suno.ai/image_589daf17-5ab7-4ac5-a34e-86a982f361a0.jpeg"
    // }

    const checkUrlsComplete = (items: any[]) => {
      return items.every((item) => item.cld2AudioUrl?.includes('.mp3'))
    }

    let isFirstLoad = true

    const pollData = async () => {
      if (isFirstLoad) {
        setIsLoading(true)
        isFirstLoad = false
      }

      try {
        let data: any

        // 检查是否是预设 ID
        if (isPresetId(batchId)) {
          data = getPresetResponse(batchId)
        } else {
          // 如果不是预设 ID，则正常请求 API
          const response = await fetch(
            `/api/music/status?taskBatchId=${batchId}&user_id=${getUserId()}`
          )
          if (!response.ok) {
            throw new Error('Failed to fetch music data')
          }
          data = await response.json()
        }

        setIsLoading(false)

        const items = data?.data?.items ?? []

        if (items.length > 0) {
          setBatchIdItems(items)

          if (currentSelectItem.current) {
            // 找到当前选中歌曲的更新数据
            const updatedSelectedItem = items.find(
              (item: any) => item.id === currentSelectItem.current.id
            )
            if (updatedSelectedItem) {
              console.log(
                'faith=============updatedSelectedItem',
                updatedSelectedItem
              )
              setSelectedItem(updatedSelectedItem)
            }
          } else {
            // 只有在没有选中歌曲时才设置第一首
            currentSelectItem.current = items[0]
            setSelectedItem(items[0])
          }

          console.log(
            'faith=============checkUrlsComplete(items)',
            checkUrlsComplete(items)
          )
          // 检查是否所有 URL 都已生成
          if (!checkUrlsComplete(items)) {
            // 如果未完成，10秒后重新请求
            setTimeout(pollData, 10000)
          }
        } else {
          setError('No music items found')
          setBatchIdItems([])
          setSelectedItem(null)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        console.error('Error fetching music data:', err)
        setBatchIdItems([])
        setSelectedItem(null)
      } finally {
        if (isFirstLoad) {
          setIsLoading(false)
        }
      }
    }

    // 开始轮询
    pollData()
  }

  const handleDownload = async (
    url: string,
    fileType: 'audio' | 'video',
    title: string
  ) => {
    if (!user) {
      setShowLoginModal(true)
      return
    }
    // update
    // 增加功能,从cookie里面拿信息,
    // membershipStatus 为 active 的时候才可以下载
    // 否则弹窗
    // 标题：解锁下载功能
    // 内容：您还不是订阅用户，无法执行下载操作。开通任意订阅立即享受：
    // • 无限次下载音乐作品
    // • 高品质音频存储
    // • AI音乐创作助手
    // • 专属会员客服支持
    // 按钮：立即去开通

    const membershipStatus = Cookies.get('membershipStatus')
    if (membershipStatus !== 'active') {
      setShowPaymentModal(true)
      return
    }
    // 设置对应 URL 的下载状态为 loading
    setDownloadingStates((prev) => ({ ...prev, [url]: true }))

    try {
      const response = await fetch('/api/music/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      })

      if (!response.ok) {
        throw new Error('Download failed')
      }

      const now = new Date()
      const timestamp = `${now.getFullYear()}${String(
        now.getMonth() + 1
      ).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(
        now.getHours()
      ).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`

      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = `${title}_${timestamp}.${
        fileType === 'audio' ? 'mp3' : 'mp4'
      }`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Download error:', error)
    } finally {
      // 无论成功还是失败，都清除 loading 状态
      setDownloadingStates((prev) => ({ ...prev, [url]: false }))
    }
  }

  const handleDeleteMusic = async (id: string, api_provider_id: string) => {
    setCurrentId(id)
    setShowConfirmation(true)
    setProviderId(api_provider_id)
  }

  const deleteMusic = async () => {
    try {
      const response = await fetch(
        `/api/music/deleteById?id=${currentId}&providerId=${providerId}`
      )

      if (!response.ok) throw new Error('Failed to delete music')
      const data = await response.json()
      console.log('🚀 ~ data:', data)
      toast.success(t('deleteSuccess'))
      setShowConfirmation(false)
      fetchUserMusics()
    } catch (err) {
      console.error('Error deleting music:', err)
      toast.error(t('deleteFailed'))
    }
  }

  useEffect(() => {
    handleParams()
    return () => {}
  }, [])

  const handleShare = async (selectedItem: any) => {
    console.log(selectedItem, '@@@@@')

    if (!selectedItem) return
    if (!user) {
      setShowLoginModal(true)
      return
    }

    const membershipStatus = Cookies.get('membershipStatus')
    if (membershipStatus !== 'active') {
      setShowPaymentModal(true)
      return
    }

    // 设置分享中状态
    setIsSharing(true)

    try {
      // 调用分享API
      const response = await fetch('/api/music/share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clipId: selectedItem.clipId,
          title: selectedItem.title,
          prompt: selectedItem.prompt,
          tags: selectedItem.tags,
          inputType: selectedItem.inputType,
          status: selectedItem.status,
          cld2AudioUrl: selectedItem.cld2AudioUrl,
          cld2ImageUrl: selectedItem.cld2ImageUrl,
          progressMsg: selectedItem.progressMsg,
          userInfo: user,
        }),
      })

      if (!response.ok) {
        throw new Error('Share failed')
      }

      const data = await response.json()

      // 将返回的id作为分享ID
      const shareId = data.id

      // 在share对话框中显示的歌曲信息，我们保存原始信息并添加共享ID
      const updatedSelectedItem = {
        ...selectedItem,
        id: shareId, // 使用返回的UUID作为ID
      }

      // 设置当前要分享的音乐项
      setShareItem(updatedSelectedItem)
      // 打开分享模态窗口
      setShowShareModal(true)
    } catch (error) {
      console.error('Share error:', error)
      toast.error(t('shareFailed') || '分享失败')
    } finally {
      setIsSharing(false)
    }
  }

  const renderFailureIcon = (type = 'broken') => {
    const icons: { [key: string]: React.ReactNode } = {
      // 损坏的音符图标
      broken: (
        <svg
          className="w-12 h-12 text-red-500"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M9 18V5l12-2v13" />
          <path d="m9 9 12-2" />
          <circle cx="6" cy="18" r="3" />
          <circle cx="18" cy="16" r="3" />
          <path d="M3 3l18 18" />
        </svg>
      ),

      // 警告感叹号
      alert: (
        <div className="relative">
          <div className="w-3 h-8 rounded-full bg-red-500 rotate-12" />
          <div className="w-3 h-3 rounded-full bg-red-500 absolute -bottom-4 left-0" />
        </div>
      ),

      // 断开的链接
      disconnected: (
        <svg
          className="w-12 h-12 text-red-500"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
          <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
          <path d="M4 4l16 16" />
        </svg>
      ),

      // AI生成失败
      ai: (
        <svg
          className="w-12 h-12 text-red-500"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M12 8V4m0 4 4-4m-4 4L8 4" />
          <path d="M20 12h-4m4 0-4 4m4-4-4-4" />
          <path d="M12 16v4m0-4 4 4m-4-4-4 4" />
          <path d="M4 12h4m-4 0 4-4m-4 4 4 4" />
        </svg>
      ),
    }

    return icons[type] || icons.broken
  }

  const uniqueData = [...batchIdItems, ...userMusics].filter(
    (item, index, self) => index === self.findIndex((t) => t.id === item.id)
  )

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500" />
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-full text-red-400">
          {error}
        </div>
      )
    }

    if (selectedItem) {
      if (selectedItem.status === 40) {
        return (
          <div className="flex flex-col items-center justify-center h-full">
            {/* 失败状态图标组 */}
            <div className="relative">
              {/* 背景动画圆圈 */}
              <div className="absolute -inset-4 bg-red-500/5 rounded-full animate-pulse" />
              <div className="absolute -inset-8 border border-red-500/10 rounded-full animate-spin-slow" />
              <div className="absolute -inset-12 border border-dashed border-red-500/5 rounded-full" />

              {/* 主圆环 */}
              <div className="relative w-32 h-32 rounded-full bg-gradient-to-br from-red-500/20 to-red-600/10 flex items-center justify-center">
                {/* 内圆环 */}
                <div className="absolute inset-2 rounded-full border-2 border-red-500/20" />

                {/* 中心图标区域 */}
                <div className="w-20 h-20 rounded-full bg-red-500/20 flex items-center justify-center backdrop-blur-sm">
                  {renderFailureIcon()} {/* 这里可以切换不同的图标类型 */}
                </div>

                {/* 装饰元素 */}
                <div className="absolute top-4 right-4 w-3 h-3 rounded-full bg-red-500/40" />
                <div className="absolute bottom-6 left-4 w-2 h-2 rounded-full bg-red-500/30" />
                <div className="absolute top-1/2 -right-1 w-2 h-2 rounded-full bg-red-500/20" />

                {/* 装饰线条 */}
                <div className="absolute top-0 left-1/2 h-full w-px bg-gradient-to-b from-transparent via-red-500/20 to-transparent" />
                <div className="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-red-500/20 to-transparent" />
              </div>
            </div>

            {/* 失败文本 */}
            <div className="mt-12 text-red-400 text-base font-medium">
              {t('generateFailed')}
            </div>

            {/* 详细信息 */}
            <div className="mt-2 text-red-400/60 text-sm px-8 text-center">
              {t('lyricsViolation')}
            </div>
          </div>
        )
      }
      return (
        <MusicPlayer
          audioUrl={selectedItem.cld2AudioUrl}
          coverUrl={selectedItem.cld2ImageUrl}
          title={selectedItem.title}
          artist={'Unknown Artist'}
          autoPlay={shouldAutoPlay}
          lyrics={selectedItem.prompt}
          onClose={() => {
            setSelectedSong('')
            setSelectedItem(null)
          }}
        />
      )
    }

    return (
      <div className="flex flex-col items-center justify-center h-full text-gray-400 px-4">
        <div className="relative w-20 h-20">
          {/* 背景圆环 */}
          <div className="absolute inset-0 rounded-full border-2 border-dashed border-gray-700/50 animate-spin-slow" />
          {/* 中心图标 */}
          <div className="absolute inset-2 rounded-full bg-gray-800/50 flex items-center justify-center">
            <Volume2 className="w-8 h-8 text-gray-500" />
          </div>
        </div>
        <p className="mt-4 text-base text-center text-gray-500 font-medium">
          {t('noMusicSelected')}
        </p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-4 lg:gap-6 h-full bg-gradient-to-br from-purple-900/20 to-gray-900/20">
      {/* 左侧编辑区 - 使用 MusicEdit 组件 */}
      <div className="lg:col-span-4 w-full rounded-xl border border-gray-800 bg-gray-900/50 pt-3 order-1">
        <MusicEdit batchId={batchId} />
      </div>

      {/* 中间列表区 */}
      {/* 中间播放器区 */}
      <div className="lg:col-span-4 w-full rounded-xl border border-gray-800 bg-gray-900/50 order-2 md:order-1">
        {renderContent()}
      </div>

      {/* 右侧预览区 */}
      <div className="md:col-span-2 lg:col-span-4 w-full rounded-xl border border-gray-800 bg-gray-900/50 flex flex-col overflow-auto order-3">
        {userMusicLoading ? (
          <div className="flex h-full items-center justify-center">
            <LoaderIcon className="mr-2 size-4 animate-spin text-primary" />
            {t('admin.users.loading')}
          </div>
        ) : uniqueData.length > 0 ? (
          <div className="flex-1 overflow-auto bg-[#1C1F26] hide-scrollbar">
            {/* 添加深色背景 */}

            {/* {batchIdItems.map((item: any, index: any) => ( */}
            {/* {[...batchIdItems, ...userMusics].map((item: any, index: number) => ( */}
            {uniqueData.map((item: any, index: number) => (
              <div
                key={item.id}
                className={cn(
                  'flex items-center gap-4 p-4 cursor-pointer border-b border-[#2A2D36]/50 transition-all duration-300',
                  'hover:bg-gradient-to-r hover:from-[#2A2D36]/30 hover:to-[#1C1F26]/30',
                  'group relative',
                  selectedSong === item.id &&
                    'bg-gradient-to-r from-[#9D5BF0]/10 to-[#F5567C]/10' // 使用紫色到粉色的渐变
                )}
                onClick={() => {
                  if (item.status !== 40) {
                    setSelectedSong(item.id)
                    setSelectedItem(uniqueData[index])
                    currentSelectItem.current = uniqueData[index]
                    setShouldAutoPlay(true)
                  }
                }}
              >
                {/* 封面图片 */}
                <div
                  className="relative w-12 h-12 rounded-lg flex-shrink-0 overflow-hidden group-hover:shadow-lg group-hover:shadow-red-500/10 transition-shadow cursor-pointer"
                  onClick={() => {
                    if (item.status !== 40) {
                      setSelectedSong(item.id)
                      setSelectedItem(uniqueData[index])
                      currentSelectItem.current = uniqueData[index]
                      setShouldAutoPlay(true)
                    }
                  }}
                >
                  {item.status === 40 ? (
                    <div className="absolute inset-0 bg-red-500/10 flex items-center justify-center rounded-lg">
                      <div className="w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center">
                        <svg
                          className="w-4 h-4 text-red-500"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M18 6L6 18" />
                          <path d="M6 6l12 12" />
                        </svg>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="absolute inset-0 bg-[#2A2D36] animate-pulse" />
                      {item.cld2ImageUrl && (
                        <>
                          <img
                            src={item.cld2ImageUrl}
                            alt={item.title}
                            className="absolute inset-0 w-full h-full object-cover rounded-lg transform group-hover:scale-110 transition-transform duration-500"
                          />
                          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                            <Play className="w-6 h-6 text-white" />
                          </div>
                        </>
                      )}
                    </>
                  )}
                </div>

                <div className="flex flex-col flex-1 overflow-x-hidden">
                  {/* 歌曲信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-[#E4E6EB] group-hover:text-white transition-colors">
                        {item.status === 40 ? t('generateFailed') : item.title}
                      </span>
                      {item.status === 40 ? (
                        <span
                          className={cn(
                            'px-2 py-0.5 text-xs rounded-full',
                            'bg-red-500/20 text-red-400',
                            'transition-all duration-300'
                          )}
                        >
                          {t('failed')}
                        </span>
                      ) : (
                        <span
                          className={cn(
                            'px-2 py-0.5 text-xs rounded-full text-[#9DA3B4]',
                            'bg-[#2A2D36]/50 group-hover:bg-gradient-to-r from-[#9D5BF0]/20 to-[#F5567C]/20',
                            'transition-all duration-300'
                          )}
                        >
                          {item.inputType === '20'
                            ? t('inspiration')
                            : t('defaultStyle')}
                        </span>
                      )}
                    </div>

                    {/* Prompt预览 */}
                    <Tooltip.Provider delayDuration={200}>
                      <Tooltip.Root>
                        <Tooltip.Trigger asChild>
                          <div className="text-xs text-[#9DA3B4] truncate mt-1 cursor-help">
                            {item.status === 40
                              ? t('lyricsViolation')
                              : item.prompt}
                          </div>
                        </Tooltip.Trigger>
                        <Tooltip.Portal>
                          <Tooltip.Content
                            className={cn(
                              'max-w-[400px] p-4 rounded-lg',
                              'bg-[#1C1F26]/95 backdrop-blur-sm',
                              'shadow-xl shadow-[#9D5BF0]/10',
                              'text-xs text-[#E4E6EB]',
                              'border border-[#2A2D36]/50',
                              'animate-in fade-in-0 zoom-in-95 duration-200'
                            )}
                            side="top"
                            sideOffset={5}
                          >
                            <div className="whitespace-pre-wrap">
                              {item.status === 40
                                ? t('lyricsViolation')
                                : item.prompt}
                            </div>
                            <Tooltip.Arrow className="fill-[#1C1F26]/95" />
                          </Tooltip.Content>
                        </Tooltip.Portal>
                      </Tooltip.Root>
                    </Tooltip.Provider>

                    {item.status !== 40 && (
                      <div className="text-xs text-[#9DA3B4] truncate mt-1">
                        {item.tags || t('noTags')}
                      </div>
                    )}
                  </div>

                  {/* 下载按钮 */}
                  <div key={index} className="relative z-10 flex gap-2 mt-2">
                    {item.status !== 40 && (
                      <>
                        {item?.cld2AudioUrl?.includes('.mp3') ||
                        item?.cld2AudioUrl?.includes('.m4a') ? (
                          <div
                            onClick={() =>
                              !downloadingStates[item.cld2AudioUrl] &&
                              handleDownload(
                                item.cld2AudioUrl,
                                'audio',
                                item.title
                              )
                            }
                            className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm bg-gray-800 hover:bg-gray-700 transition-colors ${
                              downloadingStates[item.cld2AudioUrl]
                                ? 'cursor-wait'
                                : 'cursor-pointer'
                            }`}
                          >
                            {downloadingStates[item.cld2AudioUrl] ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <Download className="w-4 h-4" />
                            )}
                            <span>
                              {downloadingStates[item.cld2AudioUrl]
                                ? t('downloading')
                                : t('audio')}
                            </span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm bg-gray-800/50">
                            <Loader2 className="w-4 h-4 animate-spin" />
                            {item?.progress}%<span>{t('audio')}</span>
                          </div>
                        )}

                        {/* 分享按钮只在存在 mp3 文件时显示 */}
                        {(item?.cld2AudioUrl?.includes('.mp3') ||
                          item?.cld2AudioUrl?.includes('.m4a')) && (
                          <div
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedItem(item)
                              handleShare(item)
                            }}
                            className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm bg-gray-800 hover:bg-gray-700 transition-colors cursor-pointer"
                          >
                            {isSharing && selectedItem?.id === item.id ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <Share2 className="w-4 h-4" />
                            )}
                            <span>
                              {isSharing && selectedItem?.id === item.id
                                ? t('sharing')
                                : t('share.share')}
                            </span>
                          </div>
                        )}
                      </>
                    )}
                    {/* 删除按钮 */}
                    <div
                      onClick={() =>
                        handleDeleteMusic(item.id, item?.api_provider_id || '')
                      }
                      className="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm bg-gray-800 hover:bg-gray-700 transition-colors cursor-pointer"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>{t('admin.users.delete')}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400 px-4">
            {t('noMusic')}
          </div>
        )}
      </div>

      {showLoginModal && (
        <ShowLoginModal
          onClose={() => setShowLoginModal(false)}
          title={t('unlockDownload')}
          desc={t('loginPrompt')}
          needBottomArea={false}
        />
      )}

      {showPaymentModal && (
        <PaymentModal onClose={() => setShowPaymentModal(false)} />
      )}

      <AlertDialog
        open={showConfirmation}
        onOpenChange={(open) => setShowConfirmation(open)}
      >
        <AlertDialogContent className="bg-[#1a1a24] border border-gray-800/50 shadow-xl">
          <AlertDialogHeader>
            <AlertDialogTitle />
          </AlertDialogHeader>
          <AlertDialogDescription className="text-gray-300">
            {t('confirmDeleteMusic')}
          </AlertDialogDescription>
          <AlertDialogFooter className="gap-2">
            <AlertDialogCancel className="bg-[#23232f] hover:bg-[#2a2a38] text-gray-300 border-gray-800">
              {t('common.confirmation.cancel')}
            </AlertDialogCancel>
            <Button
              variant="error"
              onClick={deleteMusic}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              {t('common.confirmation.confirm')}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <ShareMusicModal
        open={showShareModal}
        onOpenChange={setShowShareModal}
        shareItem={shareItem}
      />
    </div>
  )
}
