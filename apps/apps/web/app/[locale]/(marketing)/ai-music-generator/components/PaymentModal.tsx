import { useTranslations } from 'next-intl'
import { X } from 'lucide-react'
import PricingSection from '../../(home)/components/PricingSection'

interface Props {
  onClose: () => void
}

export default function ShowLoginModal({ onClose }: Props) {
  const t = useTranslations()

  return (
    <div
      className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div
        className="bg-gray-800/90 border border-gray-700/50 rounded-2xl 
          w-full max-w-[1200px] max-h-[90vh] md:max-h-[90vh] relative transform transition-all 
          flex flex-col overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 标题和关闭按钮容器 - 固定在顶部 */}
        <div className="flex items-center justify-between p-4 md:p-6 bg-gray-800/95 z-10 border-b border-gray-700/30">
          <h3 className="text-xl md:text-2xl font-bold text-white">
            {t('unlockDownload')}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
            aria-label="Close"
          >
            <X className="h-5 w-5 md:h-6 md:w-6" />
          </button>
        </div>

        {/* 内容区域 - 可滚动 */}
        <div className="flex-1 overflow-y-auto p-4 md:p-8 pt-4">
          <div className="mx-auto">
            <div className="w-full bg-red-500/10 border border-red-500/20 rounded-xl px-3 md:px-4 py-3 flex items-center justify-center mb-4">
              <p className="text-center font-semibold text-sm md:text-base text-purple-300">
                <span className="text-red-500">{t('notSubscriber')}</span>
              </p>
            </div>

            <div className="mt-4 md:mt-6">
              <PricingSection needTitle={true} className="leading-none" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
