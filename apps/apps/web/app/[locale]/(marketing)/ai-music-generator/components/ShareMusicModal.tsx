'use client'
//
import { useState } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { Check, Copy, ExternalLink, Share2 } from 'lucide-react'
import { Button } from '@ui/components/button'
import { Input } from '@ui/components/input'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import { cn } from '@ui/lib'
import toast from 'react-hot-toast'

// 添加社交平台图标
import {
  FacebookIcon,
  XIcon,
  LinkedinIcon,
  WhatsappIcon,
  RedditIcon,
  TelegramIcon,
  PinterestIcon,
  EmailIcon,
  WeiboIcon,
} from 'react-share'

interface TaskItem {
  id: string
  inputType: string
  prompt: string
  title: string
  tags: string
  clipId: string
  progress: number
  continueClipId: string
  status: number
  cld2AudioUrl: string
  cld2VideoUrl: string
  progressMsg: string
  cld2ImageUrl: string
}

interface ShareMusicModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  shareItem: TaskItem | null
}

export default function ShareMusicModal({
  open,
  onOpenChange,
  shareItem,
}: ShareMusicModalProps) {
  const t = useTranslations()
  const locale = useLocale()
  const [isCopied, setIsCopied] = useState(false)

  const copyShareLink = (item: TaskItem) => {
    // 使用ID生成分享链接
    const shareUrl =
      locale === 'en'
        ? `${window.location.origin}/music/${item.id}`
        : `${window.location.origin}/${locale}/music/${item.id}`
    navigator.clipboard.writeText(shareUrl)
    setIsCopied(true)
    setTimeout(() => setIsCopied(false), 2000)
    toast.success(t('linkCopied') || '链接已复制')
  }

  const openSharePage = (item: TaskItem) => {
    // 使用ID生成分享链接
    const shareUrl =
      locale === 'en'
        ? `${window.location.origin}/music/${item.id}`
        : `${window.location.origin}/${locale}/music/${item.id}`
    window.open(shareUrl, '_blank')
  }

  // 处理Web Share API分享
  const handleWebShare = async (item: TaskItem) => {
    if (!item) return

    const shareUrl =
      locale === 'en'
        ? `${window.location.origin}/music/${item.id}`
        : `${window.location.origin}/${locale}/music/${item.id}`

    // 使用Web Share API（如果支持）
    if (navigator.share) {
      try {
        await navigator.share({
          title: item.title,
          text: t('checkOutThisMusic'),
          url: shareUrl,
        })
      } catch (err) {
        console.error('Share error:', err)
        // 回退到复制链接
        copyShareLink(item)
      }
    } else {
      // 不支持Share API，复制链接
      copyShareLink(item)
    }
  }

  // 生成分享链接
  const getShareUrl = (item: TaskItem) => {
    return locale === 'en'
      ? `${window.location.origin}/music/${item.id}`
      : `${window.location.origin}/${locale}/music/${item.id}`
  }

  // 社交平台分享处理函数
  const handleSocialShare = (platform: string, item: TaskItem) => {
    if (!item) return

    const shareUrl = getShareUrl(item)
    const title = item.title
    const text = t('checkOutThisMusic')

    const urls = {
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
        shareUrl
      )}`,
      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(
        shareUrl
      )}&text=${encodeURIComponent(`${title} - ${text}`)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
        shareUrl
      )}`,
      whatsapp: `https://wa.me/?text=${encodeURIComponent(
        `${title} - ${text} ${shareUrl}`
      )}`,
      reddit: `https://www.reddit.com/submit?url=${encodeURIComponent(
        shareUrl
      )}&title=${encodeURIComponent(title)}`,
      telegram: `https://t.me/share/url?url=${encodeURIComponent(
        shareUrl
      )}&text=${encodeURIComponent(`${title} - ${text}`)}`,
      pinterest: `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(
        shareUrl
      )}&description=${encodeURIComponent(`${title} - ${text}`)}`,
      email: `mailto:?subject=${encodeURIComponent(
        title
      )}&body=${encodeURIComponent(`${text} ${shareUrl}`)}`,
      weibo: `http://service.weibo.com/share/share.php?url=${encodeURIComponent(
        shareUrl
      )}&title=${encodeURIComponent(`${title} - ${text}`)}`,
    }

    // 打开分享窗口
    const width = 550
    const height = 400
    const left = (window.screen.width - width) / 2
    const top = (window.screen.height - height) / 2
    window.open(
      urls[platform as keyof typeof urls],
      'share',
      `toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=${width}, height=${height}, top=${top}, left=${left}`
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={cn(
          'bg-[#1a1a24] border border-gray-800/50 shadow-xl',
          'max-w-lg w-[calc(100%-32px)]',
          'rounded-lg mx-auto overflow-hidden max-h-[90vh] flex flex-col p-0'
        )}
      >
        {/* 固定头部 */}
        <div className="p-4 sm:p-6 border-b border-gray-800/50">
          <DialogHeader className="mb-0">
            <DialogTitle
              className={cn('font-semibold text-white', 'text-lg sm:text-xl')}
            >
              {t('shareMusic')}
            </DialogTitle>
            <DialogDescription className="text-gray-400 text-sm">
              {t('shareMusicDesc')}
            </DialogDescription>
          </DialogHeader>
        </div>

        {/* 可滚动内容区域 */}
        <div className="flex-1 overflow-y-auto p-4 sm:p-6">
          {shareItem && (
            <div className="space-y-4">
              {/* 音乐卡片预览 */}
              <div
                className={cn(
                  'bg-[#23232f] rounded-lg p-3 sm:p-4',
                  'flex items-center space-x-3 sm:space-x-4'
                )}
              >
                {/* 封面图片 */}
                <div
                  className={cn(
                    'flex-shrink-0 overflow-hidden rounded-lg',
                    'w-12 h-12 sm:w-16 sm:h-16'
                  )}
                >
                  {shareItem.cld2ImageUrl ? (
                    <img
                      src={shareItem.cld2ImageUrl}
                      alt={shareItem.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div
                      className={cn(
                        'bg-[#2A2D36] w-full h-full',
                        'flex items-center justify-center'
                      )}
                    >
                      <span className="text-gray-500 text-xs">
                        {t('noImage')}
                      </span>
                    </div>
                  )}
                </div>

                {/* 音乐信息 */}
                <div className="flex-1 min-w-0">
                  <h3 className="text-white font-medium break-words text-sm sm:text-base">
                    {shareItem.title}
                  </h3>
                  <p className="text-gray-400 mt-1 text-xs sm:text-sm">
                    <span
                      className={cn(
                        'bg-[#2A2D36] rounded-full',
                        'text-[10px] sm:text-xs',
                        'px-1.5 py-0.5 sm:px-2 sm:py-0.5',
                        'inline-block'
                      )}
                    >
                      {shareItem.inputType === '20'
                        ? t('inspiration')
                        : t('defaultStyle')}
                    </span>
                    <span className="ml-2 inline-block break-words">
                      {shareItem.tags || t('noTags')}
                    </span>
                  </p>
                  <div className="text-gray-500 break-words mt-1 text-[10px] sm:text-xs">
                    {t('musicId')}: {shareItem.clipId}
                  </div>
                </div>
              </div>

              {/* 社交平台分享按钮 */}
              <div className="space-y-2">
                <label className="text-gray-400 text-xs sm:text-sm">
                  {t('socialShare')}
                </label>
                <div className="flex flex-wrap gap-2 sm:gap-3">
                  <div
                    className="cursor-pointer flex items-center justify-center"
                    onClick={() => handleSocialShare('facebook', shareItem)}
                    title="Facebook"
                  >
                    <FacebookIcon round size={36} />
                  </div>
                  <div
                    className="cursor-pointer flex items-center justify-center"
                    onClick={() => handleSocialShare('twitter', shareItem)}
                    title="Twitter/X"
                  >
                    <XIcon round size={36} />
                  </div>
                  <div
                    className="cursor-pointer flex items-center justify-center"
                    onClick={() => handleSocialShare('linkedin', shareItem)}
                    title="LinkedIn"
                  >
                    <LinkedinIcon round size={36} />
                  </div>
                  <div
                    className="cursor-pointer flex items-center justify-center"
                    onClick={() => handleSocialShare('whatsapp', shareItem)}
                    title="WhatsApp"
                  >
                    <WhatsappIcon round size={36} />
                  </div>
                  <div
                    className="cursor-pointer flex items-center justify-center"
                    onClick={() => handleSocialShare('reddit', shareItem)}
                    title="Reddit"
                  >
                    <RedditIcon round size={36} />
                  </div>
                  <div
                    className="cursor-pointer flex items-center justify-center"
                    onClick={() => handleSocialShare('telegram', shareItem)}
                    title="Telegram"
                  >
                    <TelegramIcon round size={36} />
                  </div>
                  <div
                    className="cursor-pointer flex items-center justify-center"
                    onClick={() => handleSocialShare('pinterest', shareItem)}
                    title="Pinterest"
                  >
                    <PinterestIcon round size={36} />
                  </div>
                  <div
                    className="cursor-pointer flex items-center justify-center"
                    onClick={() => handleSocialShare('email', shareItem)}
                    title="Email"
                  >
                    <EmailIcon round size={36} />
                  </div>
                  <div
                    className="cursor-pointer flex items-center justify-center"
                    onClick={() => handleSocialShare('weibo', shareItem)}
                    title="微博"
                  >
                    <WeiboIcon round size={36} />
                  </div>
                </div>
              </div>

              {/* 分享链接 */}
              <div className="space-y-2">
                <label
                  htmlFor="share-link"
                  className="text-gray-400 text-xs sm:text-sm"
                >
                  {t('shareLink')}
                </label>
                <div className="flex gap-2">
                  <Input
                    id="share-link"
                    value={
                      locale === 'en'
                        ? `${window.location.origin}/music/${shareItem.id}`
                        : `${window.location.origin}/${locale}/music/${shareItem.id}`
                    }
                    readOnly
                    className="bg-[#23232f] border-gray-700 text-gray-300 text-xs sm:text-sm h-8 sm:h-10 break-all"
                  />
                  <Button
                    onClick={() => copyShareLink(shareItem)}
                    variant="outline"
                    className="bg-[#23232f] border-gray-700 hover:bg-[#2a2a38] h-8 sm:h-10 px-2 sm:px-3 flex-shrink-0"
                  >
                    {isCopied ? (
                      <Check className="h-3 w-3 sm:h-4 sm:w-4" />
                    ) : (
                      <Copy className="h-3 w-3 sm:h-4 sm:w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 固定底部按钮区域 */}
        <div className="p-4 sm:p-6 border-t border-gray-800/50">
          <div className="flex flex-col gap-3 justify-end w-full">
            <Button
              variant="outline"
              className="bg-[#23232f] border-gray-700 hover:bg-[#2a2a38] text-gray-300 h-9 sm:h-10 w-full"
              onClick={() => onOpenChange(false)}
            >
              <span className="whitespace-nowrap">
                {t('common.confirmation.cancel')}
              </span>
            </Button>

            <Button
              className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white h-9 sm:h-10 w-full"
              onClick={() => shareItem && handleWebShare(shareItem)}
            >
              <Share2 className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="whitespace-nowrap">{t('share.share')}</span>
            </Button>

            <Button
              className="bg-gradient-to-r from-[#9D5BF0] to-[#F5567C] hover:from-[#8D4BE0] hover:to-[#E5466C] text-white h-9 sm:h-10 w-full"
              onClick={() => shareItem && openSharePage(shareItem)}
            >
              <ExternalLink className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="whitespace-nowrap">{t('openSharePage')}</span>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
