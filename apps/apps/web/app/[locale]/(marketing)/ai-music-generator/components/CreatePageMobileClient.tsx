'use client'

import { But<PERSON> } from '@ui/components/button'
import { cn } from '@ui/lib'
import { useEffect, useRef, useState } from 'react'
import MusicEdit, { TaskResponse } from '../../(home)/components/MusicEdit'
import { MusicPlayer } from './MusicPlayer'
import * as Tooltip from '@radix-ui/react-tooltip'
import { getPresetResponse } from './json/presetResponses'
import {
  Download,
  Loader2,
  Music,
  Share2,
  Trash2,
  User,
  XOctagon,
  AlertTriangle,
  WifiOff,
  Sparkles,
} from 'lucide-react'
import { useTranslations } from 'next-intl'
import { getUserFromClientCookies } from '../../../../utils/client-cookies'
import ShowLoginModal from '@shared/components/ShowLoginModal'
import { getUserId, isUserLoggedIn } from '../../../../utils/lib'
import Cookies from 'js-cookie'
import PaymentModal from './PaymentModal'
import toast from 'react-hot-toast'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogCancel,
  AlertDialogDescription,
} from '@ui/components/alert-dialog'
import ShareMusicModal from './ShareMusicModal'
import MusicShowcase from '../../(home)/components/MusicShowcase2/MusicShowcase'

interface Props {
  batchId?: string
  activeTab?: string
}
//

interface TaskItem {
  id: string
  inputType: string
  prompt: string
  title: string
  tags: string
  clipId: string
  progress: number
  continueClipId: string
  status: number
  cld2AudioUrl: string
  cld2VideoUrl: string
  progressMsg: string
  cld2ImageUrl: string
}

export default function CreatePageMobileClient({ batchId, activeTab }: Props) {
  const t = useTranslations()
  const user = getUserFromClientCookies()
  const [tab, setTab] = useState(activeTab || 'create') // 'create' or 'myWorks'
  const [selectedSong, setSelectedSong] = useState<string | null>(null)
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [batchIdItems, setBatchIdItems] = useState<any>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [userMusics, setUserMusics] = useState<TaskItem[]>([])
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [currentId, setCurrentId] = useState<string | null>(null)
  const [viewportHeight, setViewportHeight] = useState('100vh')
  const [showShareModal, setShowShareModal] = useState(false)
  const [shareItem, setShareItem] = useState<TaskItem | null>(null)
  const [isSharing, setIsSharing] = useState(false)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const providerIdRef = useRef<string>('')
  const [providerId, setProviderId] = useState<string | null>(null)

  const [downloadingStates, setDownloadingStates] = useState<
    Record<string, boolean>
  >({})

  const currentSelectItem = useRef<any>(null)

  // iOS Safari 视口高度修复
  useEffect(() => {
    // 初始设置
    updateViewportHeight()

    // 添加事件监听器
    window.addEventListener('resize', updateViewportHeight)
    window.addEventListener('orientationchange', updateViewportHeight)

    // 清理函数
    return () => {
      window.removeEventListener('resize', updateViewportHeight)
      window.removeEventListener('orientationchange', updateViewportHeight)
    }
  }, [])

  // 更新视口高度函数
  const updateViewportHeight = () => {
    // 使用 window.innerHeight 代替 100vh
    setViewportHeight(`${window.innerHeight}px`)
  }

  // 判断是否是模拟的数据，主页上面的
  const isPresetId = (id: string) => {
    return /^1074794245686558(00[1-8])$/.test(id)
  }

  const fetchUserMusics = async () => {
    if (isUserLoggedIn() === false) return

    try {
      // 添加分页参数
      const response = await fetch(
        `/api/music/myMusics?user_id=${Cookies.get(
          'userEmail'
        )}&pageNum=1&pageSize=100`
      )

      if (!response.ok) throw new Error('Failed to fetch user music history')
      const data = await response.json()

      const items = data.data
      setUserMusics(items || [])
    } catch (err) {
      console.error('Error fetching user music history:', err)
    }
  }

  // 单独处理用户音乐历史
  useEffect(() => {
    fetchUserMusics()

    // 为未完成的音乐设置定时刷新
    const refreshInterval = setInterval(() => {
      if (tab === 'myWorks') {
        fetchUserMusics()
      }
    }, 15000) // 每15秒刷新一次

    return () => clearInterval(refreshInterval)
  }, [tab])

  const handleParams = async () => {
    console.log('batchId-1', batchId)

    if (!batchId) return

    const checkUrlsComplete = (items: any[]) => {
      return items.every(
        (item) =>
          item.cld2AudioUrl?.includes('.mp3') ||
          item.cld2AudioUrl?.includes('.m4a')
      )
    }

    let isFirstLoad = true

    const pollData = async () => {
      if (isFirstLoad) {
        setIsLoading(true)
        isFirstLoad = false
      }

      try {
        let data: any

        // 检查是否是预设 ID
        if (isPresetId(batchId)) {
          data = getPresetResponse(batchId)
        } else {
          // 如果不是预设 ID，则正常请求 API

          // 这块要优化，要确定这个任务到底是哪家的
          const providerId =
            batchId.length === 32 ? 'kie' : providerIdRef.current

          const response = await fetch(
            `/api/music/status?taskBatchId=${batchId}&user_id=${getUserId()}&providerId=${providerId}`
          )

          if (!response.ok) {
            throw new Error('Failed to fetch music data')
          }
          data = await response.json()
        }

        setIsLoading(false)

        console.log('response-2', data)
        const items = data?.data?.items ?? []

        if (items.length > 0) {
          setBatchIdItems(items)

          if (currentSelectItem.current) {
            // 找到当前选中歌曲的更新数据
            const updatedSelectedItem = items.find(
              (item: any) => item.id === currentSelectItem.current.id
            )
            if (updatedSelectedItem) {
              setSelectedItem(updatedSelectedItem)
            }
          } else {
            // 只有在没有选中歌曲时才设置第一首
            currentSelectItem.current = items[0]
            setSelectedItem(items[0])
          }

          // 检查是否所有 URL 都已生成
          if (!checkUrlsComplete(items)) {
            // 如果未完成，10秒后重新请求
            setTimeout(pollData, 10000)
          }
        } else {
          setError('No music items found')
          setBatchIdItems([])
          setSelectedItem(null)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        console.error('Error fetching music data:', err)
        setBatchIdItems([])
        setSelectedItem(null)
      } finally {
        if (isFirstLoad) {
          setIsLoading(false)
        }
      }
    }

    // 开始轮询
    pollData()
  }

  useEffect(() => {
    handleParams()
    return () => {}
  }, [])

  const handleDownload = async (
    url: string,
    fileType: 'audio' | 'video',
    title: string
  ) => {
    if (!user) {
      setShowLoginModal(true)
      return
    }

    const membershipStatus = Cookies.get('membershipStatus')
    if (membershipStatus !== 'active') {
      setShowPaymentModal(true)
      return
    }
    // 设置对应 URL 的下载状态为 loading
    setDownloadingStates((prev) => ({ ...prev, [url]: true }))

    try {
      const response = await fetch('/api/music/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      })

      if (!response.ok) {
        throw new Error('Download failed')
      }

      const now = new Date()
      const timestamp = `${now.getFullYear()}${String(
        now.getMonth() + 1
      ).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(
        now.getHours()
      ).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`

      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = `${title}_${timestamp}.${
        fileType === 'audio' ? 'mp3' : 'mp4'
      }`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Download error:', error)
    } finally {
      // 无论成功还是失败，都清除 loading 状态
      setDownloadingStates((prev) => ({ ...prev, [url]: false }))
    }
  }

  const handleDeleteMusic = async (id: string, api_provider_id: string) => {
    setCurrentId(id)
    setProviderId(api_provider_id)
    setShowConfirmation(true)
  }

  const deleteMusic = async () => {
    try {
      const response = await fetch(
        `/api/music/deleteById?id=${currentId}&providerId=${providerId}`
      )
      if (!response.ok) throw new Error('Failed to delete music')
      const data = await response.json()
      toast.success(t('deleteSuccess'))
      setShowConfirmation(false)
      fetchUserMusics()
    } catch (err) {
      console.error('Error deleting music:', err)
      toast.error(t('deleteFailed'))
    }
  }

  const handleShare = async (item: TaskItem) => {
    if (!user) {
      setShowLoginModal(true)
      return
    }

    const membershipStatus = Cookies.get('membershipStatus')
    if (membershipStatus !== 'active') {
      setShowPaymentModal(true)
      return
    }

    // 设置分享中状态
    setIsSharing(true)

    try {
      // 调用分享API
      const response = await fetch('/api/music/share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clipId: item.clipId,
          title: item.title,
          prompt: item.prompt,
          tags: item.tags,
          inputType: item.inputType,
          status: item.status,
          cld2AudioUrl: item.cld2AudioUrl,
          cld2ImageUrl: item.cld2ImageUrl,
          progressMsg: item.progressMsg,
          userInfo: user,
        }),
      })

      if (!response.ok) {
        throw new Error('Share failed')
      }

      const data = await response.json()

      // 将返回的id作为分享ID
      const shareId = data.id

      // 在share对话框中显示的歌曲信息，我们保存原始信息并添加共享ID
      const updatedSelectedItem = {
        ...item,
        id: shareId, // 使用返回的UUID作为ID
      }

      // 设置当前要分享的音乐项
      setShareItem(updatedSelectedItem)
      // 打开分享模态窗口
      setShowShareModal(true)
    } catch (error) {
      console.error('Share error:', error)
      toast.error(t('shareFailed') || '分享失败')
    } finally {
      setIsSharing(false)
    }
  }

  const renderFailureIcon = () => {
    const type = 'broken'
    const icons = {
      broken: <XOctagon className="h-4 w-4 text-red-500" />,
      alert: <AlertTriangle className="h-4 w-4 text-red-500" />,
      disconnected: <WifiOff className="h-4 w-4 text-red-500" />,
      ai: <Sparkles className="h-4 w-4 text-purple-500" />,
    }

    // 修复TypeScript错误，确保类型安全
    return icons[type as keyof typeof icons] || icons.broken
  }

  const uniqueData = [...batchIdItems, ...userMusics].filter(
    (item, index, self) => index === self.findIndex((t) => t.id === item.id)
  )

  const renderContent = () => {
    if (tab === 'create') {
      return (
        <div className="flex-1 overflow-auto">
          <MusicEdit batchId={batchId} />
          <div className="mt-10">
            <MusicShowcase isInMusicGenerator={true} />
          </div>
        </div>
      )
    }

    // 我的作品标签
    if (tab === 'myWorks') {
      if (isLoading) {
        return (
          <div className="flex items-center justify-center flex-1">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500" />
          </div>
        )
      }

      if (uniqueData.length === 0) {
        return (
          <div className="flex items-center justify-center flex-1 text-gray-400 px-4 text-sm">
            {t('noMusic')}
          </div>
        )
      }

      return (
        <div className="flex flex-col h-full">
          {/* 列表区域 */}
          <div className="flex-1 overflow-auto bg-[#1C1F26]">
            {uniqueData.map((item: any, index: number) => (
              <div
                key={item.id}
                className={cn(
                  'flex items-center gap-3 p-3 cursor-pointer border-b border-[#2A2D36]/50 transition-all duration-300',
                  'hover:bg-gradient-to-r hover:from-[#2A2D36]/30 hover:to-[#1C1F26]/30',
                  'group relative',
                  selectedSong === item.id &&
                    'bg-gradient-to-r from-[#9D5BF0]/10 to-[#F5567C]/10'
                )}
                onClick={() => {
                  setSelectedSong(item.id)
                  setSelectedItem(uniqueData[index])
                  currentSelectItem.current = uniqueData[index]
                }}
              >
                {/* 封面图片 */}
                <div className="relative w-10 h-10 rounded-lg flex-shrink-0 overflow-hidden group-hover:shadow-lg group-hover:shadow-red-500/10 transition-shadow">
                  {item.status === 40 ? (
                    <div className="absolute inset-0 bg-red-500/10 flex items-center justify-center rounded-lg">
                      <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center">
                        <svg
                          className="w-3 h-3 text-red-500"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M18 6L6 18" />
                          <path d="M6 6l12 12" />
                        </svg>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="absolute inset-0 bg-[#2A2D36] animate-pulse" />
                      {item.cld2ImageUrl && (
                        <img
                          src={item.cld2ImageUrl}
                          alt={item.title}
                          className="absolute inset-0 w-full h-full object-cover rounded-lg transform group-hover:scale-110 transition-transform duration-500"
                        />
                      )}
                    </>
                  )}
                </div>

                <div className="flex flex-col flex-1 overflow-x-hidden">
                  {/* 歌曲信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium text-[#E4E6EB] group-hover:text-white transition-colors">
                        {item.status === 40 ? t('generateFailed') : item.title}
                      </span>
                      {item.status === 40 ? (
                        <span
                          className={cn(
                            'px-1.5 py-0.5 text-[10px] rounded-full',
                            'bg-red-500/20 text-red-400',
                            'transition-all duration-300'
                          )}
                        >
                          {t('failed')}
                        </span>
                      ) : (
                        <span
                          className={cn(
                            'px-1.5 py-0.5 text-[10px] rounded-full text-[#9DA3B4]',
                            'bg-[#2A2D36]/50 group-hover:bg-gradient-to-r from-[#9D5BF0]/20 to-[#F5567C]/20',
                            'transition-all duration-300'
                          )}
                        >
                          {item.inputType === '20'
                            ? t('inspiration')
                            : t('defaultStyle')}
                        </span>
                      )}
                    </div>

                    {/* Prompt预览 */}
                    <Tooltip.Provider delayDuration={200}>
                      <Tooltip.Root>
                        <Tooltip.Trigger asChild>
                          <div className="text-[10px] text-[#9DA3B4] truncate mt-1 cursor-help">
                            {item.status === 40
                              ? t('lyricsViolation')
                              : item.prompt}
                          </div>
                        </Tooltip.Trigger>
                        <Tooltip.Portal>
                          <Tooltip.Content
                            className={cn(
                              'max-w-[280px] p-3 rounded-lg',
                              'bg-[#1C1F26]/95 backdrop-blur-sm',
                              'shadow-xl shadow-[#9D5BF0]/10',
                              'text-[10px] text-[#E4E6EB]',
                              'border border-[#2A2D36]/50',
                              'animate-in fade-in-0 zoom-in-95 duration-200'
                            )}
                            side="top"
                            sideOffset={5}
                          >
                            <div className="whitespace-pre-wrap">
                              {item.status === 40
                                ? t('lyricsViolation')
                                : item.prompt}
                            </div>
                            <Tooltip.Arrow className="fill-[#1C1F26]/95" />
                          </Tooltip.Content>
                        </Tooltip.Portal>
                      </Tooltip.Root>
                    </Tooltip.Provider>
                  </div>

                  {/* 操作按钮 - 移动端更紧凑 */}
                  <div key={index} className="relative z-10 flex gap-1 mt-1">
                    {item.status !== 40 && (
                      <>
                        {item?.cld2AudioUrl?.includes('.mp3') ||
                        item?.cld2AudioUrl?.includes('.m4a') ? (
                          <div
                            onClick={(e) => {
                              e.stopPropagation()
                              !downloadingStates[item.cld2AudioUrl] &&
                                handleDownload(
                                  item.cld2AudioUrl,
                                  'audio',
                                  item.title
                                )
                            }}
                            className={`flex items-center gap-1 px-2 py-1 rounded-md text-[10px] bg-gray-800 hover:bg-gray-700 transition-colors ${
                              downloadingStates[item.cld2AudioUrl]
                                ? 'cursor-wait'
                                : 'cursor-pointer'
                            }`}
                          >
                            {downloadingStates[item.cld2AudioUrl] ? (
                              <Loader2 className="w-3 h-3 animate-spin" />
                            ) : (
                              <Download className="w-3 h-3" />
                            )}
                            <span>
                              {downloadingStates[item.cld2AudioUrl]
                                ? t('downloading')
                                : t('audio')}
                            </span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 px-2 py-1 rounded-md text-[10px] bg-gray-800/50">
                            <Loader2 className="w-3 h-3 animate-spin" />
                            {item?.progress}%<span>{t('audio')}</span>
                          </div>
                        )}

                        {/* 分享按钮 */}
                        {(item?.cld2AudioUrl?.includes('.mp3') ||
                          item?.cld2AudioUrl?.includes('.m4a')) && (
                          <div
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedItem(item)
                              handleShare(item)
                            }}
                            className="flex items-center gap-1 px-2 py-1 rounded-md text-[10px] bg-gray-800 hover:bg-gray-700 transition-colors cursor-pointer"
                          >
                            {isSharing && selectedItem?.id === item.id ? (
                              <Loader2 className="w-3 h-3 animate-spin" />
                            ) : (
                              <Share2 className="w-3 h-3" />
                            )}
                            <span>
                              {isSharing && selectedItem?.id === item.id
                                ? t('sharing')
                                : t('share.share')}
                            </span>
                          </div>
                        )}
                      </>
                    )}
                    {/* 删除按钮 */}
                    <div
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteMusic(item.id, item?.api_provider_id)
                      }}
                      className="flex items-center gap-1 px-2 py-1 rounded-md text-[10px] bg-gray-800 hover:bg-gray-700 transition-colors cursor-pointer"
                    >
                      <Trash2 className="w-3 h-3" />
                      <span>{t('admin.users.delete')}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 播放器区域 */}
          {selectedItem && (
            <div className="fixed left-0 right-0 bottom-14 z-20 border-t border-gray-800 bg-[#1C1F26]">
              {selectedItem.status === 40 ? (
                <div className="flex flex-col items-center justify-center h-28">
                  <div className="relative w-16 h-16 rounded-full flex items-center justify-center bg-gradient-to-br from-red-500/10 to-red-600/5">
                    <div className="absolute inset-1 rounded-full border border-red-500/10" />
                    <div className="w-10 h-10 rounded-full bg-red-500/10 flex items-center justify-center">
                      {renderFailureIcon()}
                    </div>
                  </div>
                  <div className="mt-2 text-red-400 text-xs">
                    {t('generateFailed')}
                  </div>
                </div>
              ) : (
                <MusicPlayer
                  audioUrl={selectedItem.cld2AudioUrl}
                  coverUrl={selectedItem.cld2ImageUrl}
                  title={selectedItem.title}
                  artist={'Unknown Artist'}
                  isMobile={true}
                  isCollapsed={isCollapsed}
                  setIsCollapsed={setIsCollapsed}
                  onClose={() => {
                    setSelectedSong('')
                    setSelectedItem(null)
                  }}
                />
              )}
            </div>
          )}
        </div>
      )
    }
  }

  return (
    <div
      className="flex flex-col overflow-hidden"
      style={{ height: viewportHeight }}
    >
      {/* 主内容区域 */}
      <div
        className={`flex-1 overflow-auto ${
          // isCollapsed ? 'pb-32' : 'pb-60'
          selectedItem ? (isCollapsed ? 'mb-28' : 'mb-40') : 'mb-16'
        }`}
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          overflowY: 'auto',
        }}
      >
        {renderContent()}
      </div>

      {/* 底部导航栏 */}
      <div className="fixed bottom-0 left-0 right-0 flex h-14 border-t border-gray-800 bg-[#161922] z-30">
        <button
          className={cn(
            'flex-1 flex flex-col items-center justify-center',
            tab === 'create' ? 'text-purple-500' : 'text-gray-400'
          )}
          onClick={() => setTab('create')}
        >
          <Music className="h-5 w-5" />
          <span className="text-[10px] mt-1">{t('musicCreation')}</span>
        </button>

        <button
          className={cn(
            'flex-1 flex flex-col items-center justify-center',
            tab === 'myWorks' ? 'text-purple-500' : 'text-gray-400'
          )}
          onClick={() => setTab('myWorks')}
        >
          <User className="h-5 w-5" />
          <span className="text-[10px] mt-1">{t('myWorks')}</span>
        </button>
      </div>

      {/* 模态框 */}
      {showLoginModal && (
        <ShowLoginModal
          onClose={() => setShowLoginModal(false)}
          title={t('unlockDownload')}
          desc={t('loginPrompt')}
          needBottomArea={false}
        />
      )}
      {showPaymentModal && (
        <PaymentModal onClose={() => setShowPaymentModal(false)} />
      )}
      <AlertDialog
        open={showConfirmation}
        onOpenChange={(open) => setShowConfirmation(open)}
      >
        <AlertDialogContent className="bg-[#1a1a24] border border-gray-800/50 shadow-xl">
          <AlertDialogHeader>
            <AlertDialogTitle></AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogDescription className="text-gray-300 text-sm">
            {t('confirmDeleteMusic')}
          </AlertDialogDescription>
          <AlertDialogFooter className="gap-2">
            <AlertDialogCancel className="bg-[#23232f] hover:bg-[#2a2a38] text-gray-300 border-gray-800 text-sm">
              {t('common.confirmation.cancel')}
            </AlertDialogCancel>
            <Button
              variant="error"
              onClick={deleteMusic}
              className="bg-red-500 hover:bg-red-600 text-white text-sm"
            >
              {t('common.confirmation.confirm')}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 分享弹窗 */}
      <ShareMusicModal
        open={showShareModal}
        onOpenChange={setShowShareModal}
        shareItem={shareItem}
      />
    </div>
  )
}
