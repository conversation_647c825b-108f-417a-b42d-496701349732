'use client'
import { useEffect, useRef, useState } from 'react'
import {
  Pause,
  Play,
  SkipBack,
  SkipForward,
  Volume2,
  X,
  ChevronUp,
  ChevronDown,
} from 'lucide-react'
import { cn } from '@ui/lib'
import { useTranslations } from 'next-intl'

interface MusicPlayerProps {
  audioUrl?: string
  coverUrl?: string
  title?: string
  artist?: string
  isMobile?: boolean
  autoPlay?: boolean
  lyrics?: string
  isCollapsed: boolean
  setIsCollapsed: (collapsed: boolean) => void
  onClose?: () => void
}

export function MusicPlayer({
  audioUrl,
  coverUrl,
  title = 'Unknown Title',
  artist = 'Unknown Artist',
  isMobile = false,
  autoPlay = false,
  lyrics = '',
  isCollapsed = false,
  setIsCollapsed,
  onClose,
}: MusicPlayerProps) {
  const t = useTranslations()
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(0.5)
  const [showLyrics, setShowLyrics] = useState(true)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const lyricsRef = useRef<HTMLDivElement>(null)

  // 当 audioUrl 改变时重置播放状态
  useEffect(() => {
    setIsPlaying(false)
    setCurrentTime(0)
    if (audioRef.current) {
      audioRef.current.currentTime = 0
    }
  }, [audioUrl])

  // 添加自动播放效果
  useEffect(() => {
    if (autoPlay && audioRef.current && audioUrl) {
      audioRef.current
        .play()
        .then(() => {
          setIsPlaying(true)
        })
        .catch((error) => {
          console.error('Auto play failed:', error)
        })
    }
  }, [autoPlay, audioUrl])

  // 添加歌词滚动效果
  useEffect(() => {
    if (showLyrics && lyricsRef.current) {
      const currentLyric = lyricsRef.current.querySelector('.current-lyric')
      if (currentLyric) {
        currentLyric.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        })
      }
    }
  }, [currentTime, showLyrics])

  const togglePlay = () => {
    if (!audioRef.current || !audioUrl) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play()
    }
    setIsPlaying(!isPlaying)
  }

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration)
    }
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = Number(e.target.value)
    setCurrentTime(time)
    if (audioRef.current) {
      audioRef.current.currentTime = time
    }
  }

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value)
    setVolume(value)
    if (audioRef.current) {
      audioRef.current.volume = value
    }
  }

  const formatTime = (time: number) => {
    if (!time || isNaN(time) || !isFinite(time) || time < 0)
      return `${t('generating')}...`
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }
  // 音频播放结束时的处理
  const handleEnded = () => {
    setIsPlaying(false)
    setCurrentTime(0)
    if (audioRef.current) {
      audioRef.current.currentTime = 0
    }
  }

  // 解析歌词
  const parseLyrics = (lyrics: string) => {
    if (!lyrics) return []
    return lyrics
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line)
  }

  // 关闭播放器
  const handleClose = () => {
    if (audioRef.current) {
      audioRef.current.pause()
    }
    setIsPlaying(false)
    if (onClose) {
      onClose()
    }
  }

  // 切换折叠状态
  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed)
  }

  // 手机端的UI渲染
  if (isMobile) {
    return (
      <div
        className={cn(
          'bottom-0 left-0 right-0 bg-gray-900/95 backdrop-blur-md border-t border-gray-800 transition-all duration-300 shadow-lg',
          isCollapsed ? 'h-12' : 'h-24'
        )}
      >
        {/* 关闭按钮 */}
        <div className="absolute right-2 top-3 flex items-center gap-2 z-10">
          <button
            onClick={toggleCollapse}
            className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-white"
          >
            {isCollapsed ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </button>
          <button
            onClick={handleClose}
            className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-white"
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        {isCollapsed ? (
          // 折叠状态下只显示最小内容
          <div className="flex h-full w-full items-center px-4">
            <div className="relative h-8 w-8 flex-shrink-0 overflow-hidden rounded-lg mr-3">
              {coverUrl ? (
                <img
                  src={coverUrl}
                  alt={title}
                  className="h-full w-full object-cover"
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-gray-800">
                  <Volume2 className="h-4 w-4 text-gray-400" />
                </div>
              )}

              {/* 播放按钮浮动在封面上 */}
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  togglePlay()
                }}
                disabled={!audioUrl}
                className={cn(
                  'absolute inset-0 flex items-center justify-center rounded-lg',
                  'bg-black/40 backdrop-blur-sm',
                  audioUrl ? 'hover:bg-black/60' : 'cursor-not-allowed'
                )}
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4 text-white" />
                ) : (
                  <Play className="h-4 w-4 text-white" />
                )}
              </button>
            </div>
            <div className="flex-1 min-w-0 pr-14">
              <h3 className="text-xs font-medium text-white truncate">
                {title}
              </h3>
            </div>
          </div>
        ) : (
          // 展开状态
          <div className="flex h-full w-full items-center px-4">
            {/* 封面图片 */}
            <div className="h-16 w-16 flex-shrink-0 overflow-hidden rounded-lg mr-3">
              {coverUrl ? (
                <img
                  src={coverUrl}
                  alt={title}
                  className="h-full w-full object-cover"
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-gray-800">
                  <Volume2 className="h-8 w-8 text-gray-400" />
                </div>
              )}
            </div>

            {/* 控制区域 */}
            <div className="flex-1 min-w-0">
              {/* 标题艺术家 */}
              <div className="mb-1">
                <h3 className="text-sm font-medium text-white truncate">
                  {title}
                </h3>
                <p className="text-xs text-gray-400 truncate">{artist}</p>
              </div>

              {/* 进度条和播放控制 */}
              <div className="flex items-center">
                <button
                  onClick={togglePlay}
                  disabled={!audioUrl}
                  className={cn(
                    'mr-2 h-8 w-8 flex-shrink-0 rounded-full flex items-center justify-center',
                    audioUrl
                      ? 'bg-purple-500 hover:bg-purple-600'
                      : 'bg-gray-700 cursor-not-allowed'
                  )}
                >
                  {isPlaying ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                </button>

                <div className="flex-1 space-y-1">
                  <input
                    type="range"
                    min={0}
                    max={duration || 100}
                    value={currentTime}
                    onChange={handleSeek}
                    className="h-1 w-full appearance-none rounded-lg bg-gray-600 cursor-pointer"
                    style={{
                      backgroundImage: `linear-gradient(to right, rgb(168 85 247) ${
                        (currentTime / (duration || 1)) * 100
                      }%, rgb(75 85 99) ${
                        (currentTime / (duration || 1)) * 100
                      }%)`,
                    }}
                  />
                  <div className="flex justify-between text-[10px] text-gray-400">
                    <span>{formatTime(currentTime)}</span>
                    <span>
                      {duration && !isNaN(duration)
                        ? formatTime(duration)
                        : '0:00'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 音频元素 */}
        {audioUrl && (
          <audio
            ref={audioRef}
            src={audioUrl}
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            onEnded={handleEnded}
          />
        )}
      </div>
    )
  }

  // 桌面端的原始UI
  return (
    <div className="flex flex-col h-full p-6">
      {/* 封面区域 */}
      <div className="flex items-center justify-center mt-4 mb-4">
        <div className={cn('relative w-24 h-24', isPlaying && 'scale-105')}>
          {coverUrl ? (
            <div className="relative w-full h-full">
              {/* 黑胶唱片底座 */}
              <div
                className={cn(
                  'absolute inset-0 rounded-full bg-gray-900',
                  "before:content-[''] before:absolute before:inset-0 before:rounded-full",
                  'before:bg-gradient-to-br before:from-gray-800 before:to-gray-950',
                  'shadow-xl'
                )}
              />
              {/* 唱片主体 - 确保完美圆形 */}
              <div
                className={cn(
                  'absolute inset-2 rounded-full bg-gray-900',
                  'flex items-center justify-center',
                  isPlaying && 'animate-spin-slow'
                )}
              >
                <img
                  src={coverUrl}
                  alt="Album Cover"
                  className="w-16 h-16 rounded-full"
                />
              </div>
              {/* 唱臂 */}
              <div
                className={cn(
                  'absolute -top-2 -right-2 w-12 h-2',
                  'origin-right transition-transform duration-1000',
                  isPlaying ? 'rotate-12' : '-rotate-12'
                )}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-gray-700 to-gray-800 rounded-full" />
                <div className="absolute left-0 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-gray-300" />
              </div>
            </div>
          ) : (
            <div className="w-full h-full rounded-full bg-gradient-to-br from-purple-600/20 to-blue-600/20 flex items-center justify-center">
              <Volume2 className="w-10 h-10 text-gray-400" />
            </div>
          )}
        </div>
      </div>

      {/* 歌词区域 */}
      {showLyrics && lyrics && (
        <div className="flex items-center justify-center my-2 flex-1">
          <div
            ref={lyricsRef}
            className="w-full max-w-xl h-[calc(100vh-32rem)] text-center space-y-2 overflow-y-auto bg-gray-900/50 rounded-lg p-4 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800"
          >
            {parseLyrics(lyrics).map((line, index) => (
              <div
                key={index}
                className={cn(
                  'text-base text-gray-200 transition-colors duration-300',
                  'hover:text-white'
                )}
              >
                {line}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 底部区域：歌名、作者、播放控制、音量、音频元素 */}
      <div className="mt-auto space-y-4">
        {/* 歌名、作者 */}
        <div className="text-center">
          <h3 className="text-lg font-semibold text-white/90">{title}</h3>
          <p className="text-sm text-gray-400">{artist}</p>
        </div>
        {/* 进度条 */}
        <div className="space-y-2">
          <div className="relative">
            <input
              type="range"
              min={0}
              max={duration || 100}
              value={currentTime}
              onChange={handleSeek}
              className="w-full h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
              style={{
                backgroundImage: `linear-gradient(to right, rgb(168 85 247) ${
                  (currentTime / (duration || 1)) * 100
                }%, rgb(75 85 99) ${(currentTime / (duration || 1)) * 100}%)`,
              }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-400">
            <span>{formatTime(currentTime)}</span>
            <span>
              {duration && !isNaN(duration) ? formatTime(duration) : '0:00'}
            </span>
          </div>
        </div>
        {/* 控制按钮 */}
        <div className="flex items-center justify-center gap-6">
          <button
            className="text-gray-400 hover:text-white transition-colors"
            onClick={() => {
              if (audioRef.current) {
                audioRef.current.currentTime = Math.max(
                  0,
                  audioRef.current.currentTime - 10
                )
              }
            }}
          >
            <SkipBack className="w-5 h-5" />
          </button>

          <button
            onClick={togglePlay}
            disabled={!audioUrl}
            className={cn(
              'w-12 h-12 rounded-full flex items-center justify-center transition-all',
              audioUrl
                ? 'bg-purple-500 hover:bg-purple-600'
                : 'bg-gray-700 cursor-not-allowed'
            )}
          >
            {isPlaying ? (
              <Pause className="w-6 h-6" />
            ) : (
              <Play className="w-6 h-6" />
            )}
          </button>

          <button
            className="text-gray-400 hover:text-white transition-colors"
            onClick={() => {
              if (audioRef.current) {
                audioRef.current.currentTime = Math.min(
                  duration,
                  audioRef.current.currentTime + 10
                )
              }
            }}
          >
            <SkipForward className="w-5 h-5" />
          </button>
        </div>
        {/* 音量控制 */}
        <div className="flex items-center gap-2 justify-center">
          <Volume2 className="w-4 h-4 text-gray-400" />
          <input
            type="range"
            min={0}
            max={1}
            step={0.01}
            value={volume}
            onChange={handleVolumeChange}
            className="w-24 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
            style={{
              backgroundImage: `linear-gradient(to right, rgb(168 85 247) ${
                volume * 100
              }%, rgb(75 85 99) ${volume * 100}%)`,
            }}
          />
        </div>
        {/* 音频元素 */}
        {audioUrl && (
          <audio
            ref={audioRef}
            src={audioUrl}
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            onEnded={handleEnded}
          />
        )}
      </div>
    </div>
  )
}
