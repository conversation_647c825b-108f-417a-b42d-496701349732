// app/[locale]/(main)/create/page.tsx (Server Component)
import { getTranslations } from 'next-intl/server'
import CreatePageClient from '../components/CreatePageClient'
import { LeftNav } from '../../components/LeftNav'
import ClientWrapper from '../components/ClientWrapper'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('blogtitle'),
    keywords: ['AI Music', 'Music Generation', 'Create Music'],
  }
}

// export default function CreatePage({ params }: any) {
//   return (
//     <div className='px-2 w-full pt-32 h-[100vh]'>
//       <CreatePageClient batchId={params.batchId} />
//     </div>
//   )
// }

// export default function CreatePage({ params }: any) {
//   return (
//     <div className="w-full h-[100vh]">
//       {/* <LeftNav> */}
//       <div className="px-2 pt-24 h-[100vh]">
//         <CreatePageClient batchId={params.batchId} />
//       </div>
//       {/* </LeftNav> */}
//     </div>
//   )
// }

export default async function CreatePage({ params }: any) {
  const t = await getTranslations()
  return (
    <div className="w-full h-screen">
      <h1 className="sr-only">{t('create2')}</h1>
      {/* 如果需要 LeftNav，请取消注释 */}
      {/* <LeftNav> */}

      <ClientWrapper batchId={params.batchId} />
      {/* </LeftNav> */}
    </div>
  )
}
