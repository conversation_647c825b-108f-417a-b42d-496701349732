// app/[locale]/(main)/create/page.tsx (Server Component)
import { getTranslations } from 'next-intl/server'
import CreatePageClient from './components/CreatePageClient'
import Head from 'next/head'
import { LeftNav } from '../components/LeftNav'
import { Metadata } from 'next'
import { detectMobileDevice } from '@/utils/lib'
import ClientWrapper from './components/ClientWrapper'

//
// export default function CreatePage() {
//   return (
//     <div className='px-2 w-full pt-24 h-[100vh]'>
//       <CreatePageClient />
//     </div>
//   )
// }

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('createPageTitle'),
    description: t('createPageDesc'),
    keywords: t('createPageKeywords'),
  }
}

export default async function CreatePage() {
  const t = await getTranslations()
  return (
    <div className="w-full h-screen">
      <h1 className="sr-only">{t('create2')}</h1>
      {/* 如果需要 LeftNav，请取消注释 */}
      {/* <LeftNav> */}

      <ClientWrapper />
      {/* </LeftNav> */}
    </div>
  )
}
