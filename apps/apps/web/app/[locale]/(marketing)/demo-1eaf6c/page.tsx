'use client'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useTranslations } from 'next-intl'
import { useState, useEffect } from 'react'
import { useRouter } from '@shared/hooks/router'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import { LoginForm } from '@saas/auth/components/LoginForm'
import crypto from 'crypto'
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react'

interface PaymentData {
  api_key: string
  api_sig: string
  pm_id: string
  order_id: string
  description: string
  amount: string
  currency: string
  return_url: string
  notify_url: string
}

interface OrderData extends Omit<PaymentData, 'api_sig'> {}

export default function DemoPage() {
  const user = getUserFromClientCookies()
  const router = useRouter()
  const t = useTranslations()
  const [showLoginDialog, setShowLoginDialog] = useState<boolean>(false)
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [paymentId, setPaymentId] = useState<string | null>(null)
  const [showTimeoutModal, setShowTimeoutModal] = useState(false)

  useEffect(() => {
    let intervalId: NodeJS.Timeout
    let timeoutId: NodeJS.Timeout
    const startTime = Date.now()
    const TIMEOUT_DURATION = 5 * 60 * 1000 // 5分钟

    const checkSubscriptionStatus = async () => {
      if (!paymentId) return

      try {
        const response = await fetch(
          `/api/membership/check-status?orderId=${paymentId}`
        )
        const data = (await response.json()) as any

        if (data.status === 'succeeded' && data.data) {
          clearInterval(intervalId)
          clearTimeout(timeoutId)
          setIsLoading(false)
          setPaymentId(null)
          setShowSuccessModal(true)
        } else if (data.status === 'failed') {
          clearInterval(intervalId)
          clearTimeout(timeoutId)
          setIsLoading(false)
          setPaymentId(null)
          alert(data.message || 'Payment failed')
        } else if (Date.now() - startTime >= TIMEOUT_DURATION) {
          clearInterval(intervalId)
          clearTimeout(timeoutId)
          setIsLoading(false)
          setShowTimeoutModal(true)
        }
      } catch (error) {
        console.error('Error checking subscription status:', error)
      }
    }

    if (paymentId && isLoading) {
      intervalId = setInterval(checkSubscriptionStatus, 5000)
      checkSubscriptionStatus()

      // 设置5分钟超时
      timeoutId = setTimeout(() => {
        clearInterval(intervalId)
        setIsLoading(false)
        setShowTimeoutModal(true)
      }, TIMEOUT_DURATION)
    }

    return () => {
      clearInterval(intervalId)
      clearTimeout(timeoutId)
    }
  }, [paymentId, isLoading])

  // 正确的签名生成函数
  const generateSignature = (data: OrderData, secretKey: string): string => {
    const signStr = [
      data.api_key,
      data.pm_id,
      data.amount,
      data.currency,
      data.order_id,
      secretKey,
    ].join('|')

    return crypto.createHash('md5').update(signStr).digest('hex')
  }

  // 初始化支付数据
  useEffect(() => {
    const orderData: OrderData = {
      // api_key: 'live_797b9ec13fc21e9c',
      api_key: 'sandbox_902916da98f5a156',
      // pm_id: 'creditcard_kr',
      pm_id: 'payssion_test',
      order_id: `ORD${Date.now()}`, // 生成唯一订单号
      description: '测试产品购买',
      amount: '100',
      currency: 'KRW',
      return_url: `http://sandbox.payssion.com/demo/afterpayment`, // 支付成功后的跳转页面
      // notify_url: `https://www.aimakesong.com/api/webhooks/payment`,
    }

    const secretKey = 'pFu6wDiBeWuWrJyB9YNS0gKYorfgpo3P'
    // const secretKey = '********************************' // 沙箱环境
    const api_sig = generateSignature(orderData, secretKey)

    setPaymentData({
      ...orderData,
      api_sig,
    })
  }, [])

  const handlePayment = (): void => {
    if (!user) {
      setShowLoginDialog(true)
      return
    }

    if (!paymentData) {
      console.error('Payment data not initialized')
      return
    }
    setIsLoading(true)
    setPaymentId(paymentData.order_id)
    // 创建并提交支付表单
    const form = document.createElement('form')
    form.method = 'POST'
    // form.action = 'https://www.payssion.com/payment/create.html'
    form.action = 'http://sandbox.payssion.com/payment/create.html' // 沙箱环境，生产环境使用 https://www.payssion.com/payment/create.html
    form.target = '_blank'

    Object.entries(paymentData).forEach(([key, value]) => {
      const input = document.createElement('input')
      input.type = 'hidden'
      input.name = key
      input.value = value
      form.appendChild(input)
    })
    console.log('paymentData', paymentData, form)

    document.body.appendChild(form)
    form.submit()
    document.body.removeChild(form)
  }

  return (
    <div className="container max-w-3xl pt-32 pb-16">
      <div className="mb-12 text-balance pt-8 text-center">
        <button
          className="rounded-full bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
          onClick={handlePayment}
        >
          点击支付
        </button>
      </div>

      <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
        <DialogContent
          className="
                w-full 
                bg-[#1A1B1E] 
                border-[#2D2E32]
                shadow-xl
                pb-0
              "
        >
          <DialogHeader>
            <DialogTitle className="text-white">
              {t('loginForFreeTrial')}
            </DialogTitle>
          </DialogHeader>
          <LoginForm needBackground={false} />
        </DialogContent>
      </Dialog>

      {/* 加载中遮罩 */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#1a1d24] border border-gray-800 rounded-xl p-6 flex flex-col items-center">
            <Loader2 className="w-8 h-8 text-purple-400 animate-spin" />
            <p className="mt-4 text-gray-300">{t('processingSubscription')}</p>
          </div>
        </div>
      )}

      {/* 订阅成功模态框 */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#1a1d24] border border-gray-800 rounded-xl p-8 max-w-md w-full mx-4">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {t('subscriptionSuccessful')}
              </h3>
              <p className="text-gray-400 text-center mb-6">
                {t('thankYouForSubscribing')} {t('accessToPremiumFeatures')}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 w-full">
                <button
                  onClick={() => {
                    setShowSuccessModal(false)
                    router.push('/auth/login')
                  }}
                  className="flex-1 py-3 px-4 rounded-lg font-medium bg-gray-800 text-white hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
                >
                  {t('common.menu.profile')}
                </button>
                <button
                  onClick={() => {
                    setShowSuccessModal(false)
                    router.push('/ai-music-generator')
                  }}
                  className="flex-1 py-3 px-4 rounded-lg font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-opacity flex items-center justify-center gap-2"
                >
                  {t('createMusic')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 超时提示模态框 */}
      {showTimeoutModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#1a1d24] border border-gray-800 rounded-xl p-8 max-w-md w-full mx-4">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mb-4">
                <AlertCircle className="w-8 h-8 text-yellow-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {t('paymentProcessing')}
              </h3>
              <p className="text-gray-400 text-center mb-6">
                {t('paymentProcessingDesc')}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 w-full">
                <button
                  onClick={() => {
                    setShowTimeoutModal(false)
                    setPaymentId(null)
                    router.push('/auth/login')
                  }}
                  className="flex-1 py-3 px-4 rounded-lg font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-opacity flex items-center justify-center gap-2"
                >
                  {t('goToProfile')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
