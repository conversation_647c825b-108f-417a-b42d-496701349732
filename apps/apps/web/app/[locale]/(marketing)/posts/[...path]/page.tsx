import { redirect } from '@i18n/routing'
import { PostContent } from '@marketing/blog/components/PostContent'
import {
  getActivePathFromUrlParam,
  getLocalizedDocumentWithFallback,
} from '@shared/lib/content'
import { allPosts } from 'content-collections'
import { getLocale } from 'next-intl/server'

type Params = {
  path: string
  locale: string
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params

  const { path } = params

  const locale = await getLocale()
  const activePath = getActivePathFromUrlParam(path)
  const page = getLocalizedDocumentWithFallback<any>(
    allPosts,
    activePath,
    locale
  )
  console.log('🚀 ~ page:', page)

  return {
    title: page?.title,
    description: page?.description,
    openGraph: {
      title: page?.title,
    },
  }
}

export default async function BlogPostPage(props: { params: Promise<Params> }) {
  const params = await props.params

  const { path } = params

  const locale = await getLocale()
  const activePath = getActivePathFromUrlParam(path)
  const page = getLocalizedDocumentWithFallback(allPosts, activePath, locale)

  if (!page) {
    redirect({ href: '/', locale })
  }

  const { title, date, description, body } = page

  return (
    <div className="container pt-32 pb-24">
      <div className="mb-12 text-center space-y-1">
        <h1 className="font-bold text-4xl">{title}</h1>
        <p className="text-gray-300">Last Updated: {date}</p>
        <p className="mx-auto max-w-2xl text-gray-300">{description}</p>
      </div>

      <PostContent content={body} />
    </div>
  )
}
