import { <PERSON>, Sparkles, <PERSON>, Check } from 'lucide-react'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('aboutT'),
    description: t('aboutD'),
  }
}

export default async function AboutUs() {
  const t = await getTranslations()
  return (
    <main className="px-8 pt-40 pb-24 bg-black">
      {/* Main Content */}
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="mb-16">
          <h1 className="text-2xl md:text-4xl font-bold text-purple-500 mb-6">
            {t('aboutSeoTitle')}
          </h1>

          <p className="text-gray-300 mb-8 text-lg">{t('welcomeDesc')}</p>

          <div className="mb-10">
            <h3 className="text-2xl font-bold mb-4 flex items-center">
              <span className="mr-2">🚀</span>
              <span className="text-purple-500">{t('ourMission')}</span>
            </h3>
            <p className="text-gray-300 text-lg">{t('missionDesc')}</p>
          </div>

          <div className="mb-10">
            <h3 className="text-2xl font-bold mb-4 flex items-center">
              <span className="mr-2">🎵</span>
              <span className="text-purple-500">{t('whatWeOffer')}</span>
            </h3>
            <ul className="space-y-4 text-lg">
              <li className="flex items-center gap-3">
                <div className="w-2 h-2 rounded-full bg-purple-500 flex-shrink-0"></div>
                <span className="text-gray-300">
                  <strong>{t('aiSongGenerator')}</strong> –{' '}
                  {t('aiSongGeneratorDesc')}
                </span>
              </li>
              <li className="flex items-center gap-3">
                <div className="w-2 h-2 rounded-full bg-purple-500 flex-shrink-0"></div>
                <span className="text-gray-300">
                  <strong>{t('vocalRemoverOnline')}</strong> –{' '}
                  {t('vocalRemoverOnlineDesc')}
                </span>
              </li>
              <li className="flex items-center gap-3">
                <div className="w-2 h-2 rounded-full bg-purple-500 flex-shrink-0"></div>
                <span className="text-gray-300">
                  <strong>{t('supportIsolateVocals')}</strong> –{' '}
                  {t('aiLyricsGeneratorDesc')}
                </span>
              </li>
              <li className="flex items-center gap-3">
                <div className="w-2 h-2 rounded-full bg-purple-500 flex-shrink-0"></div>
                <span className="text-gray-300">
                  <strong>{t('textToSongTechnology')}</strong> –{' '}
                  {t('textToSongTechnologyDesc')}
                </span>
              </li>
            </ul>
          </div>

          <div className="mb-10">
            <h3 className="text-2xl font-bold mb-4 flex items-center">
              <span className="mr-2">🌍</span>
              <span className="text-purple-500">{t('whoWeServe')}</span>
            </h3>
            <p className="text-gray-300 text-lg">{t('whoWeServeDesc')}</p>
          </div>
        </div>

        {/* Why Choose Us Section - Card Style */}
        <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-8 text-center">
          {t('whyChooseUs')}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          {/* Card 1 */}
          <div className="bg-gray-900/60 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-purple-500/20">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full bg-purple-900/50 flex items-center justify-center mr-4">
                <Sparkles className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="text-xl font-bold text-white">
                {t('aiPoweredCreativity')}
              </h3>
            </div>
            <p className="text-gray-300">{t('aiPoweredCreativityDesc')}</p>
          </div>

          {/* Card 2 */}
          <div className="bg-gray-900/60 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-purple-500/20">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full bg-purple-900/50 flex items-center justify-center mr-4">
                <Clock className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="text-xl font-bold text-white">
                {t('easyAndFast')}
              </h3>
            </div>
            <p className="text-gray-300">{t('easyAndFastDesc')}</p>
          </div>

          {/* Card 3 */}
          <div className="bg-gray-900/60 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-purple-500/20">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full bg-purple-900/50 flex items-center justify-center mr-4">
                <Music className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="text-xl font-bold text-white">
                {t('multiGenreSupport')}
              </h3>
            </div>
            <p className="text-gray-300">{t('multiGenreSupportDesc')}</p>
          </div>

          {/* Card 4 */}
          <div className="bg-gray-900/60 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-purple-500/20">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full bg-purple-900/50 flex items-center justify-center mr-4">
                <Check className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="text-xl font-bold text-white">
                {t('freeAndPremiumOptions')}
              </h3>
            </div>
            <p className="text-gray-300">{t('freeAndPremiumOptionsDesc')}</p>
          </div>
        </div>

        {/* Call to Action */}
        <div className="flex justify-center">
          <Link href="/ai-music-generator">
            <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-3 rounded-full text-lg font-medium hover:opacity-90 transition-opacity">
              {t('freeAISongGen')}
            </button>
          </Link>
        </div>
      </div>
    </main>
  )
}
