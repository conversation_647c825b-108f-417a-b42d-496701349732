'use client'
import { useTranslations } from 'next-intl'
import { useState, useRef, useEffect } from 'react'
import toast from 'react-hot-toast'

interface StemTrackProps {
  stem: {
    id: string
    title: string
    status: number
    progress: number
    progressMsg: string
    cld2AudioUrl: string
    cld2ImageUrl: string
    tags: string
    errorMessage?: string
  }
  title: string
}

export function CoverTrack({ stem, title }: StemTrackProps) {
  const t = useTranslations()
  const [isPlaying, setIsPlaying] = useState(false)
  const [playbackProgress, setPlaybackProgress] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [audioDuration, setAudioDuration] = useState(0)
  const audioRef = useRef<HTMLAudioElement>(null)

  // Format time to MM:SS
  const formatTime = (seconds: number): string => {
    if (!seconds || !isFinite(seconds) || isNaN(seconds)) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Handle play/pause toggle
  const togglePlayPause = () => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play().catch((err) => {
        console.error('Failed to play audio:', err)
      })
    }
  }

  // Update the isPlaying state when audio plays or pauses
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)
    const handleEnded = () => {
      setIsPlaying(false)
      setPlaybackProgress(0)
      setCurrentTime(0)
    }

    // Audio time update handler
    const handleTimeUpdate = () => {
      if (audio) {
        const current = audio.currentTime
        setCurrentTime(current)
        if (
          isFinite(audio.duration) &&
          !isNaN(audio.duration) &&
          audio.duration > 0
        ) {
          setPlaybackProgress((current / audio.duration) * 100)
        } else {
          setPlaybackProgress(0)
        }
      }
    }

    // Set duration when metadata is loaded
    const handleLoadedMetadata = () => {
      if (audio) {
        if (isFinite(audio.duration) && !isNaN(audio.duration)) {
          setAudioDuration(audio.duration)
        } else {
          console.warn('Invalid audio duration detected')
          setAudioDuration(0)
        }
      }
    }

    // Handle errors in audio loading
    const handleError = (e: Event) => {
      console.error('Error loading audio:', e)
      setAudioDuration(0)
      setPlaybackProgress(0)
    }

    audio.addEventListener('play', handlePlay)
    audio.addEventListener('pause', handlePause)
    audio.addEventListener('ended', handleEnded)
    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('error', handleError)

    return () => {
      audio.removeEventListener('play', handlePlay)
      audio.removeEventListener('pause', handlePause)
      audio.removeEventListener('ended', handleEnded)
      audio.removeEventListener('timeupdate', handleTimeUpdate)
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
      audio.removeEventListener('error', handleError)
    }
  }, [])

  // Handle progress bar change
  const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newProgress = parseInt(e.target.value)
    setPlaybackProgress(newProgress)
    if (audioRef.current) {
      const time = (newProgress / 100) * audioRef.current.duration
      audioRef.current.currentTime = time
    }
  }

  // 使用useEffect检测错误状态并显示toast
  useEffect(() => {
    if (stem.status === 40 && stem.errorMessage) {
      toast.error(stem.errorMessage)
    }
  }, [stem.status, stem.errorMessage])

  // 修改逻辑：
  // 1. 如果状态不是30且不是20，或者没有音频URL，显示加载占位符和进度条
  // 2. 如果状态是20且有音频URL，显示可播放的界面，但仍然在底部显示进度
  // 3. 只有当状态为30或进度=100%时，才显示完整的播放器界面，没有进度显示

  // 获取背景图片URL，如果没有则使用默认图片
  const coverImageUrl = stem.cld2ImageUrl || '/songCover/old.webp'

  // 格式化标签显示
  const tagsDisplay = stem.tags || ''

  // 判断是否显示下载按钮（原唱不显示下载按钮
  const showDownloadButton = stem.id !== 'original'

  // 渲染播放控制组件
  const renderPlaybackControls = () => {
    // 始终显示进度条，但根据状态设置不同样式
    const progressBarClasses =
      isPlaying || playbackProgress > 0
        ? 'bg-gradient-to-r from-purple-500 to-pink-500'
        : 'bg-gray-600'

    // 检查音频时长是否有效
    const isValidDuration =
      isFinite(audioDuration) && !isNaN(audioDuration) && audioDuration > 0

    return (
      <div className="mt-3">
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-400">
            {formatTime(currentTime)}
          </span>
          <div className="flex-1 h-1 bg-gray-800 rounded-full overflow-hidden relative group">
            {/* 背景进度条 */}
            <div
              className={`h-full transition-all duration-100 ${progressBarClasses}`}
              style={{ width: `${playbackProgress}%` }}
            />

            {/* 拖动滑块的轨道 */}
            <input
              type="range"
              min="0"
              max="100"
              value={playbackProgress}
              onChange={handleProgressChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
            />

            {/* 进度小球 */}
            <div
              className="absolute top-1/2 h-3 w-3 rounded-full bg-white shadow-md transform -translate-y-1/2 pointer-events-none transition-all duration-100"
              style={{ left: `calc(${playbackProgress}% - 6px)` }}
            />
          </div>
          {isValidDuration && (
            <span className="text-xs text-gray-400">
              {formatTime(audioDuration)}
            </span>
          )}
        </div>
      </div>
    )
  }

  // 情况1: 仍在处理中，没有可播放的音频
  if ((stem.status !== 30 && stem.status !== 20) || !stem.cld2AudioUrl) {
    return (
      <div className="bg-gray-900 rounded-lg p-3 backdrop-blur-sm transition-all">
        {/* Loading placeholder */}
        <div className="flex items-center gap-3 mb-3">
          <div className="bg-gray-700 rounded-lg h-12 w-12 flex-shrink-0 animate-pulse"></div>
          <div className="flex-1 space-y-2">
            <div className="bg-gray-700 h-4 w-3/4 rounded animate-pulse"></div>
            <div className="bg-gray-700 h-3 w-1/2 rounded animate-pulse"></div>
          </div>
        </div>

        {/* Progress indicator */}
        <div className="mt-4">
          <div className="h-1.5 bg-gray-800 rounded-full overflow-hidden">
            <div
              className={`h-full transition-all duration-500 ${
                stem.status === 40
                  ? 'bg-red-500'
                  : 'bg-gradient-to-r from-purple-500 to-pink-500'
              }`}
              style={{ width: `${stem.progress}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <div className="flex items-center">
              {stem.status !== 40 ? (
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-purple-500"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              ) : (
                <svg
                  className="-ml-1 mr-2 h-4 w-4 text-red-500"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
              )}
              <span>{stem.status === 40 ? t('error') : t('coverAbout')}</span>
            </div>
            <span>{stem.progress}%</span>
          </div>
        </div>
      </div>
    )
  }

  // 情况2: 有可播放音频，但进度未达到100%
  if (stem.progress < 100) {
    return (
      <div className="bg-gray-900 rounded-lg p-3">
        <div className="flex items-center gap-3">
          <div className="bg-gray-800 rounded-lg h-12 w-12 flex items-center justify-center relative overflow-hidden">
            <div
              className="absolute inset-0 bg-cover bg-center opacity-50"
              style={{ backgroundImage: `url('${coverImageUrl}')` }}
            ></div>
            <audio ref={audioRef} src={stem.cld2AudioUrl} preload="metadata" />
            <button
              onClick={togglePlayPause}
              className="text-white hover:text-purple-400 transition-colors relative z-10 bg-black/30 p-2 rounded-full hover:bg-black/50"
            >
              {isPlaying ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="6" y="4" width="4" height="16"></rect>
                  <rect x="14" y="4" width="4" height="16"></rect>
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polygon points="5 3 19 12 5 21 5 3"></polygon>
                </svg>
              )}
            </button>
          </div>
          <div className="flex-1">
            <div className="font-medium text-white">{title}</div>
            <div className="text-gray-400 text-sm">{tagsDisplay}</div>
          </div>
          {showDownloadButton && (
            <div className="flex gap-2">
              <a
                href={stem.cld2AudioUrl}
                download={`${title}.mp3`}
                className="bg-gray-800 hover:bg-gray-700 p-2 rounded-lg text-gray-300 flex items-center gap-1"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                {t('audio')}
              </a>
            </div>
          )}
        </div>

        {/* 播放进度条 */}
        {renderPlaybackControls()}

        {/* 在底部显示生成进度 */}
        <div className="mt-3">
          <div className="h-1 bg-gray-800 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-500"
              style={{ width: `${stem.progress}%` }}
            />
          </div>
          <div className="flex justify-end text-xs text-gray-400 mt-1">
            <span>{stem.progress}%</span>
          </div>
        </div>
      </div>
    )
  }

  // 情况3: 完成的曲目，显示常规播放器界面
  return (
    <div className="bg-gray-900 rounded-lg p-3">
      <div className="flex items-center gap-3">
        <div className="bg-gray-800 rounded-lg h-12 w-12 flex items-center justify-center relative overflow-hidden">
          <div
            className="absolute inset-0 bg-cover bg-center opacity-50"
            style={{ backgroundImage: `url('${coverImageUrl}')` }}
          ></div>
          <audio ref={audioRef} src={stem.cld2AudioUrl} preload="metadata" />
          <button
            onClick={togglePlayPause}
            className="text-white hover:text-purple-400 transition-colors relative z-10 bg-black/30 p-2 rounded-full hover:bg-black/50"
          >
            {isPlaying ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="6" y="4" width="4" height="16"></rect>
                <rect x="14" y="4" width="4" height="16"></rect>
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
            )}
          </button>
        </div>
        <div className="flex-1">
          <div className="font-medium text-white">{title}</div>
          <div className="text-gray-400 text-sm">{tagsDisplay}</div>
        </div>
        {showDownloadButton && (
          <div className="flex gap-2">
            <a
              href={stem.cld2AudioUrl}
              download={`${title}.mp3`}
              className="bg-gray-800 hover:bg-gray-700 p-2 rounded-lg text-gray-300 flex items-center gap-1"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              {t('audio')}
            </a>
          </div>
        )}
      </div>

      {/* 播放进度条 */}
      {renderPlaybackControls()}
    </div>
  )
}
