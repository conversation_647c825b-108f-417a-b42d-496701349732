import { getTranslations } from 'next-intl/server'
import { Music, Zap, Award, Clock } from 'lucide-react'
import Link from 'next/link'

export default async function FeatureSection() {
  const t = await getTranslations()

  const features = [
    {
      icon: <Music className="h-8 w-8 text-purple-400" />,
      title: 'coverFeature1Title',
      description: 'coverFeature1Desc',
    },
    {
      icon: <Zap className="h-8 w-8 text-purple-400" />,
      title: 'coverFeature2Title',
      description: 'coverFeature2Desc',
    },
    {
      icon: <Award className="h-8 w-8 text-purple-400" />,
      title: 'coverFeature3Title',
      description: 'coverFeature3Desc',
    },
    {
      icon: <Clock className="h-8 w-8 text-purple-400" />,
      title: 'coverFeature4Title',
      description: 'coverFeature4Desc',
    },
  ]

  return (
    <div className="max-w-6xl mx-auto px-4 py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-4">
          {t('coverFeaturesTitle')}
        </h2>
        <p className="text-gray-300 max-w-2xl mx-auto">
          {t('coverFeaturesSubtitle')}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {features.map((feature, index) => (
          <div
            key={index}
            className="p-6 bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800 hover:border-purple-500/30 transition-all duration-300"
          >
            <div className="flex items-center justify-center w-14 h-14 rounded-full bg-purple-900/20 mb-6 mx-auto">
              {feature.icon}
            </div>
            <h3 className="text-xl font-semibold text-purple-400 mb-3 text-center">
              {t(feature.title)}
            </h3>
            <p className="text-gray-400 text-center">
              {t(feature.description)}
            </p>
          </div>
        ))}
      </div>
    </div>
  )
}
