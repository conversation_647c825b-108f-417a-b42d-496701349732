import { getTranslations } from 'next-intl/server'
import { Upload, Music, Download, Palette } from 'lucide-react'

const HowTo = async () => {
  const t = await getTranslations()
  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-8 md:py-16">
      <div className="text-center mb-8 md:mb-16">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-purple-500 mb-4 md:mb-6">
          {t('howToCoverTitle')}
        </h2>

        <p className="text-gray-300 text-base md:text-lg max-w-2xl mx-auto px-2">
          {t('coverIntro')}
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-8 items-stretch">
        {/* 左侧图片 */}
        <div className="w-full lg:w-1/2 flex justify-center lg:justify-start">
          <div className="relative rounded-xl overflow-hidden shadow-2xl shadow-purple-500/20 w-full h-full flex items-center">
            <img
              src="/songCover/ai-cover-song.webp"
              alt="AI Song Cover Demo"
              className="w-full h-full object-cover rounded-xl transform transition-transform duration-500 hover:scale-105"
              style={{ objectPosition: 'center', minHeight: '100%' }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-purple-900/60 to-transparent pointer-events-none"></div>
          </div>
        </div>

        {/* 右侧步骤说明 */}
        <div className="w-full lg:w-1/2">
          <div className="grid gap-6 md:gap-8">
            {/* Step 1 */}
            <div className="relative p-4 bg-gray-900/80 backdrop-blur rounded-xl border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/20 flex-shrink-0">
                  <Upload className="w-5 h-5 md:w-6 md:h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg md:text-xl font-bold text-purple-400 mb-2">
                    {t('step1Title')}
                  </h3>
                  <ul className="space-y-2 text-sm md:text-base text-gray-300">
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                      <span className="flex-1">{t('step1Action')}</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                      <span className="flex-1">{t('step1Desc')}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Step 2 */}
            <div className="relative p-4 bg-gray-900/80 backdrop-blur rounded-xl border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/20 flex-shrink-0">
                  <Palette className="w-5 h-5 md:w-6 md:h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg md:text-xl font-bold text-purple-400 mb-2">
                    {t('step2TitleCover')}
                  </h3>
                  <ul className="space-y-2 text-sm md:text-base text-gray-300">
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                      <span className="flex-1">{t('step2ActionCover')}</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                      <span className="flex-1">{t('step2DescCover')}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Step 3 */}
            <div className="relative p-4 bg-gray-900/80 backdrop-blur rounded-xl border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/20 flex-shrink-0">
                  <Music className="w-5 h-5 md:w-6 md:h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg md:text-xl font-bold text-purple-400 mb-2">
                    {t('step3TitleCover')}
                  </h3>
                  <ul className="space-y-2 text-sm md:text-base text-gray-300">
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                      <span className="flex-1">{t('step3ActionCover')}</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                      <span className="flex-1">{t('step3DescCover')}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Step 4 */}
            <div className="relative p-4 bg-gray-900/80 backdrop-blur rounded-xl border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/20 flex-shrink-0">
                  <Download className="w-5 h-5 md:w-6 md:h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg md:text-xl font-bold text-purple-400 mb-2">
                    {t('step4Title')}
                  </h3>
                  <ul className="space-y-2 text-sm md:text-base text-gray-300">
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                      <span className="flex-1">{t('step4Action')}</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex-shrink-0 mt-1.5"></span>
                      <span className="flex-1">{t('step4Desc')}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HowTo
