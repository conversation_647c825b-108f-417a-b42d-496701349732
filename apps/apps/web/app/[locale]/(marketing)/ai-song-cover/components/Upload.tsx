'use client'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useTranslations } from 'next-intl'
import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import { LoginForm } from '@saas/auth/components/LoginForm'
import { detectMobileDevice } from '@/utils/lib'
import { supabase } from '@/lib/supabaseClient'
import toast from 'react-hot-toast'

interface UploadProps {
  isUploading: boolean
  error: string
  onUpload: (file: File) => void
  onFileUploaded?: (fileUrl: string, originalName: string, file: File) => void
}

export function Upload({
  isUploading,
  onUpload,
  onFileUploaded,
  error,
}: UploadProps) {
  const t = useTranslations()
  const user = getUserFromClientCookies()
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)

  // 上传音频到 Supabase storage
  const uploadAudioToStorage = async (file: File) => {
    try {
      setUploadProgress(0)

      // 创建一个唯一的文件名，使用安全字符
      const fileExt = file.name.split('.').pop() || 'mp3'

      // 生成一个不含中文和特殊字符的安全文件名
      const safeFileName = `audio_${Date.now()}_${Math.floor(
        Math.random() * 1000
      )}.${fileExt}`

      // 检查文件大小，避免上传空文件或过大文件
      if (file.size < 1000) {
        throw new Error('文件大小异常小，可能已损坏')
      }

      if (file.size > 10 * 1024 * 1024) {
        throw new Error('文件大小超过10MB限制')
      }

      // 进度模拟
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + 10
        })
      }, 300)

      // 上传到 Supabase storage
      const { data, error } = await supabase.storage
        .from('make-song')
        .upload(safeFileName, file, {
          contentType: file.type, // 使用文件原始的内容类型
          cacheControl: '3600',
          upsert: false,
        })

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (error) {
        throw error
      }

      // 获取音频的公共访问 URL
      const {
        data: { publicUrl },
      } = supabase.storage.from('make-song').getPublicUrl(`${safeFileName}`)

      // 如果提供了onFileUploaded回调，则调用它
      if (onFileUploaded) {
        onFileUploaded(publicUrl, file.name, file)
      }

      return {
        url: publicUrl,
        fileName: safeFileName,
        originalName: file.name,
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : '上传文件失败')
      throw error
    } finally {
      // 确保进度条最终为0（重置）或100（完成）
      setTimeout(() => {
        setUploadProgress(0)
      }, 1000)
    }
  }

  const handleUpload = async (file: File) => {
    if (!user) {
      setShowLoginDialog(true)
      return
    }

    try {
      onUpload(file) // 先调用原来的onUpload通知父组件文件被选择

      // 如果提供了onFileUploaded回调，上传文件到Supabase
      if (onFileUploaded) {
        await uploadAudioToStorage(file)
      }
    } catch (err) {
      console.error('文件处理失败:', err)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const file = e.dataTransfer.files[0]
    if (file) handleUpload(file)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) handleUpload(file)
  }

  return (
    <>
      <div
        className="border-2 border-dashed border-gray-600 rounded-lg p-12 text-center"
        onDragOver={(e) => e.preventDefault()}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id="file"
          className="hidden"
          onChange={handleChange}
          accept="audio/*"
          disabled={isUploading}
        />
        <label
          htmlFor="file"
          className="cursor-pointer text-gray-400 hover:text-white"
        >
          <div className="space-y-2">
            <div className="text-4xl">🎵</div>
            <div>
              {detectMobileDevice().isMobile
                ? t('uploadPromptMobile')
                : t('uploadPrompt')}
            </div>
            <div className="text-sm text-gray-500">{t('supportedFormats')}</div>

            {uploadProgress > 0 && (
              <div className="mt-4">
                <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <div className="text-sm text-gray-400 mt-1">
                  {uploadProgress < 100 ? '上传中...' : '上传完成!'}
                </div>
              </div>
            )}
          </div>
        </label>
        {error && <div className="mt-4 text-red-500">{error}</div>}
      </div>

      <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
        <DialogContent
          className="
          w-full 
          pb-0
          bg-[#1A1B1E] 
          border-[#2D2E32]
          shadow-xl
        "
        >
          <DialogHeader>
            <DialogTitle className="text-white">
              {t('loginForFreeTrial')}
            </DialogTitle>
          </DialogHeader>
          <LoginForm needBackground={false} />
        </DialogContent>
      </Dialog>
    </>
  )
}
