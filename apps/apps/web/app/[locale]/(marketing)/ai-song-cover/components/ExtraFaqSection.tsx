import { getTranslations } from 'next-intl/server'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@ui/components/accordion'

export default async function ExtraFaqSection() {
  const t = await getTranslations()

  const faqs = [
    {
      question: 'faqCover1Question',
      answer: 'faqCover1Answer',
    },
    {
      question: 'faqCover2Question',
      answer: 'faqCover2Answer',
    },
    {
      question: 'faqCover3Question',
      answer: 'faqCover3Answer',
    },
    {
      question: 'faqCover4Question',
      answer: 'faqCover4Answer',
    },
    {
      question: 'faqCover5Question',
      answer: 'faqCover5Answer',
    },
  ]

  return (
    <section className="max-w-4xl mx-auto px-4 py-16 bg-gray-950/30">
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-4">
          {t('coverFaqSectionTitle')}
        </h2>
      </div>

      <div className="space-y-4">
        <Accordion type="single" collapsible className="space-y-4">
          {faqs.map((faq, index) => (
            <AccordionItem
              key={index}
              value={`extra-faq-${index}`}
              className="border border-gray-800 rounded-lg overflow-hidden bg-gray-900/60 backdrop-blur-sm px-1"
            >
              <AccordionTrigger className="text-left py-5 px-5 hover:bg-gray-800/30 text-gray-200 hover:text-white text-lg font-medium">
                {t(faq.question)}
              </AccordionTrigger>
              <AccordionContent className="px-5 pb-5 pt-2 text-gray-400">
                {t(faq.answer)}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
      <div className="mt-10 text-center">
        <p className="text-gray-400 mb-4">{t('stillHaveQuestions')}</p>
        <a
          href="/contact"
          className="text-purple-400 hover:text-purple-300 font-medium underline"
        >
          {t('contactSupport')}
        </a>
      </div>
    </section>
  )
}
