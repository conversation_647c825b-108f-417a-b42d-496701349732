'use client'

import { useTranslations } from 'next-intl'
import { useState } from 'react'

interface StyleSelectorProps {
  selectedStyle: string
  onSelectStyle: (style: string) => void
}

// 音乐风格数据
const musicStyles = [
  { id: 'pop', name: 'Pop', icon: '🎵' },
  { id: 'rock', name: 'Rock', icon: '🎸' },
  { id: 'edm', name: 'E<PERSON>', icon: '🎧' },
  { id: 'rnb', name: 'R&B', icon: '🎹' },
  { id: 'jazz', name: 'Jazz', icon: '🎷' },
  { id: 'classical', name: 'Classical', icon: '🎻' },
  { id: 'country', name: 'Country', icon: '🤠' },
  { id: 'metal', name: 'Metal', icon: '🔥' },
  { id: 'folk', name: 'Folk', icon: '🪕' },
  { id: 'hiphop', name: 'Hip Hop', icon: '🎤' },
  { id: 'reggae', name: '<PERSON>ga<PERSON>', icon: '🌴' },
  { id: 'blues', name: 'Blues', icon: '🎺' },
]

export function StyleSelector({
  selectedStyle,
  onSelectStyle,
}: StyleSelectorProps) {
  const t = useTranslations()
  const [searchTerm, setSearchTerm] = useState('')

  const filteredStyles = musicStyles.filter((style) =>
    style.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="border border-gray-700 rounded-lg p-4 bg-gray-800/50">
      <h3 className="text-lg font-medium text-purple-400 mb-2">
        {t('selectStyle')}
      </h3>

      <div className="relative mb-3">
        <input
          type="text"
          placeholder={t('searchStyles')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full bg-gray-900 border border-gray-700 rounded-lg py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
        />
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700">
        {filteredStyles.map((style) => (
          <button
            key={style.id}
            onClick={() => onSelectStyle(style.id)}
            className={`px-3 py-2 rounded-lg flex items-center gap-2 transition-colors
              ${
                selectedStyle === style.id
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-900 text-gray-300 hover:bg-gray-800'
              }`}
          >
            <span>{style.icon}</span>
            <span className="truncate">{style.name}</span>
          </button>
        ))}
      </div>
    </div>
  )
}
