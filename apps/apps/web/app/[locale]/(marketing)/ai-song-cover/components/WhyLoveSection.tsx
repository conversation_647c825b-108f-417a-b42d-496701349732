import { getTranslations } from 'next-intl/server'
import { Mic, Music, Sliders, Share2 } from 'lucide-react'

export default async function WhyLoveSection() {
  const t = await getTranslations()

  const features = [
    {
      icon: <Mic className="h-8 w-8 text-purple-400" />,
      title: 'coverWhyLovePoint1',
      description: 'coverWhyLoveDesc1',
    },
    {
      icon: <Music className="h-8 w-8 text-purple-400" />,
      title: 'coverWhyLovePoint2',
      description: 'coverWhyLoveDesc2',
    },
    {
      icon: <Sliders className="h-8 w-8 text-purple-400" />,
      title: 'coverWhyLovePoint3',
      description: 'coverWhyLoveDesc3',
    },
    {
      icon: <Share2 className="h-8 w-8 text-purple-400" />,
      title: 'coverWhyLovePoint4',
      description: 'coverWhyLoveDesc4',
    },
  ]

  return (
    <div className="max-w-6xl mx-auto px-4 py-16 bg-gray-900/40">
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-6">
          {t('coverWhyLoveTitle')}
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {features.map((feature, index) => (
          <div
            key={index}
            className="p-6 bg-gray-800/60 backdrop-blur-sm rounded-xl border border-gray-700 hover:border-purple-500/20 transition-all duration-300 flex"
          >
            <div className="flex-shrink-0 mr-4">
              <div className="w-14 h-14 rounded-full bg-purple-900/30 flex items-center justify-center">
                {feature.icon}
              </div>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-purple-400 mb-2">
                {t(feature.title)}
              </h3>
              <p className="text-gray-300">{t(feature.description)}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
