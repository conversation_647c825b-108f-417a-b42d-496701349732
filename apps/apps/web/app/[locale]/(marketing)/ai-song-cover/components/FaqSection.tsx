import { getTranslations } from 'next-intl/server'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@ui/components/accordion'

export default async function FaqSection() {
  const t = await getTranslations()

  const faqs = [
    {
      id: '01',
      question: t('faqCover1Question'),
      answer: t('faqCover1Answer'),
    },
    {
      id: '02',
      question: t('faqCover2Question'),
      answer: t('faqCover2Answer'),
    },
    {
      id: '03',
      question: t('faqCover3Question'),
      answer: t('faqCover3Answer'),
    },
    {
      id: '04',
      question: t('faqCover4Question'),
      answer: t('faqCover4Answer'),
    },
    {
      id: '05',
      question: t('faqCover5Question'),
      answer: t('faqCover5Answer'),
    },
  ]

  return (
    <section className="max-w-4xl mx-auto px-4 py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-4">
          {t('coverFaqSectionTitle')}
        </h2>
        <p className="text-gray-300 max-w-2xl mx-auto">
          {t('coverFaqSubtitle')}
        </p>
      </div>

      <div className="grid gap-6 max-w-4xl mx-auto">
        {faqs.map((faq) => (
          <div
            key={faq.id}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 hover:border-purple-500/50 transition-colors"
          >
            <h3 className="text-xl font-semibold mb-4 text-white">
              <span className="text-purple-400">{faq.id}. </span>
              {faq.question}
            </h3>
            <p className="text-gray-400 leading-relaxed">{faq.answer}</p>
          </div>
        ))}
      </div>

      <div className="text-center mt-12 text-gray-400">
        <p>
          {t('pricingFaq.support.text')}{' '}
          <a
            href="mailto:<EMAIL>"
            className="text-purple-400 hover:text-purple-300 transition-colors"
          >
            <EMAIL>
          </a>
        </p>
      </div>
    </section>
  )
}
