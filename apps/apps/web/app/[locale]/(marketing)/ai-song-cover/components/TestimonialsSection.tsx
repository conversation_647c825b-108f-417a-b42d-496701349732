import { getTranslations } from 'next-intl/server'
import Image from 'next/image'

export default async function TestimonialsSection() {
  const t = await getTranslations()

  const testimonials = [
    {
      id: 1,
      text: 'coverTestimonial1',
      name: '<PERSON>',
      role: 'Music Enthusiast',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah',
    },
    {
      id: 2,
      text: 'coverTestimonial2',
      name: '<PERSON>',
      role: 'Content Creator',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=<PERSON>',
    },
    {
      id: 3,
      text: 'coverTestimonial3',
      name: '<PERSON>',
      role: 'Aspiring <PERSON>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=<PERSON>',
    },
    {
      id: 4,
      text: 'coverTestimonial4',
      name: '<PERSON>',
      role: 'Social Media Influencer',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=<PERSON>',
    },
    {
      id: 5,
      text: 'coverTestimonial5',
      name: '<PERSON>',
      role: 'Music Producer',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Rachel',
    },
  ]

  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-purple-500 mb-6">
          {t('coverTestimonialsTitle')}
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {testimonials.map((testimonial) => (
          <div
            key={testimonial.id}
            className="p-6 bg-gray-800/60 backdrop-blur-sm rounded-xl border border-gray-700 hover:border-purple-500/20 transition-all duration-300"
          >
            <div className="flex items-center gap-4">
              {/* 头像区域 */}
              <div className="relative">
                <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-r from-purple-500 to-pink-500 p-[2px]">
                  <div className="w-full h-full rounded-full overflow-hidden bg-gray-800">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                {/* 在线状态指示器 */}
                <div className="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-green-400 border-2 border-gray-800"></div>
              </div>
              {/* 用户信息 */}
              <div>
                <div className="font-semibold text-purple-400">
                  {testimonial.name}
                </div>
                <div className="text-sm text-gray-400">{testimonial.role}</div>
              </div>
            </div>
            <div>
              <p className="text-gray-400 my-4 mb-0">"{t(testimonial.text)}"</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
