'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { Upload } from './Upload'
import { WaveformLoader } from './WaveformLoader'
import { CoverTrack } from './CoverTrack'
import { detectMobileDevice, getUserId } from '../../../../utils/lib'
import { useTranslations } from 'next-intl'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { POINTS_CONFIG } from '../../../../../constants'
import { validateUserForGeneration } from '@/utils/userValidation'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import PricingSection from '../../(home)/components/PricingSection'
import CoverMusicEdit from '../../(home)/components/MusicEdit/CoverMusicEdit'
import { consumeUserPoints } from '@/utils/pointsService'
import toast from 'react-hot-toast'
import { supabase } from '@/lib/supabaseClient'
import { Divide } from 'lucide-react'

interface StemResult {
  id: string
  title: string
  status: number
  progress: number
  progressMsg: string
  cld2AudioUrl: string
}

export async function splitAudio(
  fileUrl: string,
  userId: string,
  options: {
    customMode: boolean
    instrumental: boolean
    title?: string
    prompt?: string
    style?: string
  }
) {
  try {
    // 直接使用传入的fileUrl，不再需要上传文件
    console.log('使用已上传文件的URL:', fileUrl)

    // 准备请求体
    const requestBody: any = {
      uploadUrl: fileUrl, // 使用上传后获得的公开可访问URL
      customMode: options.customMode,
      instrumental: options.instrumental,
      model: 'V3_5', // 使用默认模型版本
      callBackUrl: 'https://love-song-pro.vercel.app/api/music/status',
    }

    // 根据customMode和instrumental添加其他必要参数
    if (options.customMode) {
      // 自定义模式
      if (options.instrumental) {
        // 纯伴奏模式需要style和title
        if (!options.style || !options.title) {
          throw new Error('自定义纯伴奏模式需要提供style和title')
        }
        requestBody.style = options.style
        requestBody.title = options.title
      } else {
        // 非纯伴奏模式需要style、prompt和title
        if (!options.style || !options.prompt || !options.title) {
          throw new Error('自定义模式需要提供style、prompt和title')
        }
        requestBody.style = options.style
        requestBody.prompt = options.prompt
        requestBody.title = options.title
      }
    } else {
      // 非自定义模式只需要prompt
      if (!options.prompt) {
        throw new Error('非自定义模式需要提供prompt')
      }
      requestBody.prompt = options.prompt
    }

    const headers = {
      'Content-Type': 'application/json',
      Authorization: 'Bearer b8e434f8bb38f9f2fb2b3803bab5ab3c',
      Accept: 'application/json',
      'x-userId': userId || '413564',
    }
    const API_BASE_URL = 'https://kieai.erweima.ai/api/v1/generate/upload-cover'

    const response = await fetch(API_BASE_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody),
    })

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error processing audio:', error)
    toast.error(
      error instanceof Error
        ? error.message
        : 'Error processing audio, please try again later.'
    )
    return {
      code: 500,
      msg: error instanceof Error ? error.message : 'Error processing audio',
    }
  }
}

export default function SplitMusicClient() {
  const t = useTranslations()
  const [isUploading, setIsUploading] = useState(false)
  const [taskBatchId, setTaskBatchId] = useState<string>('')
  const [stems, setStems] = useState<StemResult[]>([])
  const [originalAudio, setOriginalAudio] = useState<string>('')
  const [error, setError] = useState('')
  const [currentFile, setCurrentFile] = useState<File | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isProcessed, setIsProcessed] = useState(false)
  const [payDialogTitle, setPayDialogTitle] = useState('')
  const user = getUserFromClientCookies()
  const userInfoRef = useRef(null)
  const [originalName, setOriginalName] = useState('')
  const pollInterval = useRef<NodeJS.Timeout | null>(null)
  const [coverValues, setCoverValues] = useState({
    generationMode: 'default' as 'default' | 'custom',
    isInstrumentalOnly: false,
    selectedStyles: [] as string[],
    title: '',
    prompt: '',
  })

  // 添加回调处理相关的状态
  const [callbackStatus, setCallbackStatus] = useState<string>('')
  const [generatedTracks, setGeneratedTracks] = useState<any[]>([])

  // 添加一个状态来存储上传后的文件URL
  const [uploadedFileUrl, setUploadedFileUrl] = useState<string>('')

  const [progressState, setProgressState] = useState<Record<string, number>>({})

  // 添加一个ref来跟踪进度，避免状态更新延迟问题
  const progressRef = useRef<Record<string, number>>({})

  useEffect(() => {
    return () => {
      if (originalAudio) {
        URL.revokeObjectURL(originalAudio)
      }
      stopPolling()
    }
  }, [originalAudio])

  const handleFileUpload = async (file: File) => {
    if (file.size > 10 * 1024 * 1024) {
      setError(t('errorFileSize'))
      return
    }

    setIsUploading(true)
    setError('')
    setCurrentFile(file)
    setIsProcessed(false)
    setStems([])

    try {
      const localUrl = URL.createObjectURL(file)
      setOriginalAudio(localUrl)
    } catch (err) {
      setError(t('errorLoadFile'))
    } finally {
      setIsUploading(false)
    }
  }

  // 处理文件上传完成后的回调
  const handleFileUploaded = (
    fileUrl: string,
    originalName: string,
    file: File
  ) => {
    console.log('文件上传完成，URL:', fileUrl)
    setUploadedFileUrl(fileUrl)
    setOriginalName(originalName)
  }

  const handleRemoveVocals = async () => {
    // 验证用户权限
    if (user) {
      const validationResult: any = await validateUserForGeneration(
        user,
        POINTS_CONFIG.MUSIC_GENERATION
      )
      console.log('🚀 ~ validationResult:', validationResult)
      if (!validationResult.success) {
        // 弹支付的弹窗（poe写，要大方优雅）
        // 我希望弹窗界面是一个组件，支持传入一个message展示信息，其他地方也可以调用
        // 展示之后页面的布局
        // 头部： 提示内容为 validationResult.message
        // 内容： 套餐（就是之前写的组件复用）

        // isMobile
        if (detectMobileDevice().isMobile) {
          showMobileSubscriptionPrompt(validationResult.message)
        } else {
          setPayDialogTitle(validationResult.message)
        }

        return
      }

      userInfoRef.current = validationResult.userInfo
    }
    if (!currentFile) {
      setError(t('errorNoFile'))
      return
    }

    // 检查是否有上传后的URL
    if (!uploadedFileUrl) {
      setError('请等待文件上传完成')
      return
    }

    // 检查必要的值是否存在
    if (coverValues.generationMode === 'custom' && !coverValues.title) {
      setError(t('errorNoTitle') || '请输入标题')
      return
    }

    if (
      coverValues.generationMode === 'custom' &&
      coverValues.selectedStyles.length === 0
    ) {
      setError(t('errorNoStyles') || '请选择至少一种风格')
      return
    }

    if (!coverValues.isInstrumentalOnly && !coverValues.prompt.trim()) {
      setError(t('errorNoPrompt') || '请输入描述或歌词')
      return
    }

    setIsProcessing(true)
    setError('')

    try {
      const userId = getUserId()

      // 使用上传后的文件URL调用splitAudio函数
      const result = await splitAudio(uploadedFileUrl, userId, {
        customMode: coverValues.generationMode === 'custom',
        instrumental: coverValues.isInstrumentalOnly,
        title: coverValues.title,
        prompt: coverValues.prompt,
        style: coverValues.selectedStyles.join(','),
      })

      if (result.code === 200) {
        setTaskBatchId(result.data.taskId)
        startPolling(result.data.taskId)

        // 显示成功消息，使用翻译键
        toast.success(t('songCoverSubmitted'))

        // 消耗积分
        if (user && userInfoRef.current) {
          const consumeResult = await consumeUserPoints(
            {
              ...user,
              // @ts-ignore
              id: userInfoRef.current.id,
            },
            POINTS_CONFIG.MUSIC_GENERATION
          )
          console.log('消耗积分成功', consumeResult)
        }
      } else {
        setError(result.msg || t('errorProcessing'))
        setIsProcessing(false)
      }
    } catch (err) {
      console.error('处理音频时出错:', err)
      setError(err instanceof Error ? err.message : t('errorProcessingRetry'))
      setIsProcessing(false)
    }
  }

  const startPolling = (batchId: string) => {
    stopPolling()

    // 重置进度引用
    progressRef.current = {}

    // 设置轮询间隔
    pollInterval.current = setInterval(() => checkProgress(batchId), 4000)
  }

  const checkProgress = async (batchId: string) => {
    try {
      const response = await fetch(
        `/api/music/cover-status?taskBatchId=${batchId}&user_id=${getUserId()}`,
        {
          cache: 'no-store',
        }
      )

      if (!response.ok) {
        throw new Error(t('errorFetchStatus'))
      }

      const result = await response.json()

      if (result.code === 200) {
        // 如果结果中有items
        if (result.data && result.data.items && result.data.items.length > 0) {
          // 处理每个item并更新进度
          const updatedStems = result.data.items.map((item: any) => {
            // 任务已完成
            if (item.status === 30) {
              progressRef.current[item.id] = 100
              return {
                ...item,
                progress: 100,
              }
            }
            // 任务部分完成，音频已可播放，继续叠加进度
            else if (item.status === 20 && item.cld2AudioUrl) {
              // 获取当前进度，如果没有则从85%开始
              const currentProgress = progressRef.current[item.id] || 85

              // 缓慢增加进度，每次0.3-0.7%
              const increment = 0.3 + Math.random() * 0.4

              // 确保不超过95%
              progressRef.current[item.id] = Math.min(
                currentProgress + increment,
                95
              )

              // 返回带有进度的item
              return {
                ...item,
                progress: Math.floor(progressRef.current[item.id]),
                // 确保音频URL可用
                cld2AudioUrl: item.cld2AudioUrl,
              }
            }
            // 任务出错
            else if (item.status === 40) {
              return item
            }
            // 任务处理中 - 实现平滑递增进度
            else {
              // 初始化进度值为15%，如果没有记录
              if (!progressRef.current[item.id]) {
                progressRef.current[item.id] = 15
              } else {
                // 根据当前进度确定增量 - 让增量随进度增加而减少
                let increment = 0
                const currentProgress = progressRef.current[item.id]

                if (currentProgress < 30) {
                  // 初始阶段增加4-6%
                  increment = 4 + Math.random() * 2
                } else if (currentProgress < 50) {
                  // 中期增加3-5%
                  increment = 3 + Math.random() * 2
                } else if (currentProgress < 70) {
                  // 后期增加2-4%
                  increment = 2 + Math.random() * 2
                } else if (currentProgress < 85) {
                  // 接近完成增加1-2%
                  increment = 1 + Math.random()
                } else {
                  // 最后阶段增加0.5-1%
                  increment = 0.5 + Math.random() * 0.5
                }

                // 确保不超过90%，为完成预留空间
                progressRef.current[item.id] = Math.min(
                  currentProgress + increment,
                  90
                )
              }

              // 将更新后的进度应用到item
              return {
                ...item,
                progress: Math.floor(progressRef.current[item.id]),
              }
            }
          })

          // 更新stems状态
          setStems(updatedStems)

          // 同步更新progressState状态，保持UI一致
          setProgressState({ ...progressRef.current })

          // 检查是否有部分完成的任务，显示播放器但继续轮询
          if (
            result.data.items.some(
              (item: any) => item.status === 20 && item.cld2AudioUrl
            )
          ) {
            // 部分完成，显示播放器但继续轮询
            setIsProcessed(true) // 允许显示播放器
            // 继续处理，但允许用户查看部分结果
            // 不设置setIsProcessing(false)，因为我们只想修改进度计算
          }

          // 检查是否所有任务完成
          if (result.data.items.every((item: any) => item.status === 30)) {
            stopPolling()
            setIsProcessed(true)
            setIsProcessing(false)

            // 确保所有进度都显示为100%
            const completedProgressState: Record<string, number> = {}
            result.data.items.forEach((item: any) => {
              completedProgressState[item.id] = 100
            })
            progressRef.current = completedProgressState
            setProgressState(completedProgressState)
          }

          // 检查是否所有任务出错
          if (result.data.items.every((item: any) => item.status === 40)) {
            toast.error(
              t(result.data.items[0].errorMessage) || t('checkCoverFileFormat')
            )
            stopPolling()
            setIsProcessed(true)
            setIsProcessing(false)
            setStems([])
            setOriginalAudio('')
            setCurrentFile(null)
          }
        }
      }
    } catch (err) {
      console.error('Progress check error:', err)
      setError(t('errorCheckProgress'))
      stopPolling()
    }
  }

  const stopPolling = () => {
    if (pollInterval.current) {
      clearInterval(pollInterval.current)
      pollInterval.current = null
    }
  }

  useEffect(() => {
    return () => stopPolling()
  }, [])

  // 处理CoverMusicEdit值变化的回调函数
  const handleCoverValuesChange = useCallback(
    (values: {
      generationMode: 'default' | 'custom'
      isInstrumentalOnly: boolean
      selectedStyles: string[]
      title: string
      prompt: string
    }) => {
      setCoverValues(values)
    },
    []
  )

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:gap-6 md:space-y-0">
        <div className="d1 md:w-1/2">
          <div className="relative">
            {currentFile ? (
              <div className="border-2 border-dashed border-gray-700 rounded-lg p-8 text-center">
                <p className="text-gray-300 mb-2">
                  {t('currentFile')} {currentFile.name}
                </p>
                <p className="text-gray-500 text-sm">
                  {t('fileSize')}{' '}
                  {(currentFile.size / (1024 * 1024)).toFixed(2)}
                  MB
                </p>
                {uploadedFileUrl ? (
                  <p className="text-green-500 mt-2">
                    ✓ {t('fileUploaded') || '文件已上传'}
                  </p>
                ) : (
                  <p className="text-yellow-500 mt-2">
                    {t('fileUploading') || '文件上传中，请稍候...'}
                  </p>
                )}
              </div>
            ) : (
              <Upload
                isUploading={isUploading}
                onUpload={handleFileUpload}
                onFileUploaded={handleFileUploaded}
                error={error}
              />
            )}
            <CoverMusicEdit onValuesChange={handleCoverValuesChange} />
            {(isUploading || isProcessing) && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm rounded-lg">
                <WaveformLoader />
              </div>
            )}
          </div>

          <div className="flex justify-center">
            <button
              onClick={handleRemoveVocals}
              disabled={
                !currentFile || isProcessing || isUploading || !uploadedFileUrl
              }
              className={`w-full mt-4 py-4 rounded-lg font-medium transition-opacity
        ${
          !currentFile || isProcessing || isUploading || !uploadedFileUrl
            ? 'bg-gray-600 cursor-not-allowed'
            : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90'
        }`}
            >
              {isProcessing ? t('common.processing') : t('songCover1')}
            </button>
          </div>
        </div>
        <div className="d2 md:w-1/2 border-2 border-dashed border-gray-600 rounded-lg p-4 flex flex-col items-start justify-start pt-6">
          {stems.length > 0 ? (
            <div className="space-y-6 w-full">
              <div className="border border-gray-700 rounded-lg p-4 bg-gray-800/50">
                <h3 className="text-lg font-medium text-purple-400 mb-2">
                  {t('titleOriginalTrack')}
                </h3>
                <CoverTrack
                  key="original"
                  title={originalName}
                  stem={{
                    id: 'original',
                    title: t('originalTrackTitle'),
                    status: 30,
                    progress: 100,
                    progressMsg: 'productionCompleted',
                    cld2AudioUrl: originalAudio,
                  }}
                />
              </div>

              <div className="border border-gray-700 rounded-lg p-4 bg-gray-800/50">
                <h3 className="text-lg font-medium text-purple-400 mb-2">
                  {t('titleSeparatedTracks')}
                </h3>
                <div className="space-y-4">
                  {stems.map((stem) => (
                    <CoverTrack
                      key={stem.id}
                      stem={stem}
                      title={coverValues.title || originalName}
                    />
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full w-full mt-6">
              <img
                src="/songCover/old.webp"
                alt="ai-cover-song"
                className="mx-auto max-w-[150px] w-full opacity-80 object-contain"
              />
              <p className="text-center mt-4 text-gray-300 text-base">
                {t('songCoverIntro')}
              </p>
            </div>
          )}
        </div>
      </div>

      <Dialog
        open={!!payDialogTitle}
        onOpenChange={(open) =>
          setPayDialogTitle((title) => (open ? title : ''))
        }
      >
        <DialogContent
          className="
          max-w-[1200px] 
          w-full 
          h-[920px]
          bg-[#1A1B1E] 
          border-[#2D2E32]
          shadow-xl
        "
        >
          <DialogHeader>
            <DialogTitle className="text-white">
              {t('insufficientCredits')}
            </DialogTitle>
          </DialogHeader>
          <div className="w-full bg-red-500/10 border border-red-500/20 rounded-xl p-4">
            <p className="text-center font-semibold text-xl text-purple-300">
              <span className="text-red-500">{payDialogTitle}</span>
            </p>
          </div>
          <PricingSection needTitle={true} className="leading-none" />
        </DialogContent>
      </Dialog>
    </div>
  )
}
function showMobileSubscriptionPrompt(message: any) {
  throw new Error('Function not implemented.')
}
