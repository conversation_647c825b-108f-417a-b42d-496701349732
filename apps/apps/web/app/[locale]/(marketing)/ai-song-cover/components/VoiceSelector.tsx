'use client'

import { useTranslations } from 'next-intl'
import { useState } from 'react'

interface VoiceSelectorProps {
  selectedVoice: string
  onSelectVoice: (voice: string) => void
}

// AI歌手声音数据
const aiVoices = [
  { id: 'male1', name: '<PERSON> (Male)', gender: 'male' },
  { id: 'male2', name: '<PERSON> (Male)', gender: 'male' },
  { id: 'male3', name: '<PERSON> (Male)', gender: 'male' },
  { id: 'female1', name: '<PERSON> (Female)', gender: 'female' },
  { id: 'female2', name: '<PERSON> (Female)', gender: 'female' },
  { id: 'female3', name: '<PERSON> (Female)', gender: 'female' },
  { id: 'nonbinary1', name: '<PERSON> (Non-binary)', gender: 'nonbinary' },
  { id: 'child1', name: '<PERSON> (Child)', gender: 'child' },
  { id: 'robotic1', name: '<PERSON> (Robotic)', gender: 'robotic' },
  { id: '', name: '<PERSON> (Detect)', gender: 'auto' },
]

export function VoiceSelector({
  selectedVoice,
  onSelectVoice,
}: VoiceSelectorProps) {
  const t = useTranslations()
  const [activeFilter, setActiveFilter] = useState<string>('all')

  const filteredVoices =
    activeFilter === 'all'
      ? aiVoices
      : aiVoices.filter((voice) => voice.gender === activeFilter)

  return (
    <div className="border border-gray-700 rounded-lg p-4 bg-gray-800/50">
      <h3 className="text-lg font-medium text-purple-400 mb-2">
        {t('selectVoice')}
      </h3>

      <div className="flex flex-wrap gap-2 mb-3">
        <button
          onClick={() => setActiveFilter('all')}
          className={`px-3 py-1 rounded-md text-sm ${
            activeFilter === 'all'
              ? 'bg-purple-600 text-white'
              : 'bg-gray-900 text-gray-300 hover:bg-gray-800'
          }`}
        >
          {t('allVoices')}
        </button>
        <button
          onClick={() => setActiveFilter('male')}
          className={`px-3 py-1 rounded-md text-sm ${
            activeFilter === 'male'
              ? 'bg-purple-600 text-white'
              : 'bg-gray-900 text-gray-300 hover:bg-gray-800'
          }`}
        >
          {t('maleVoices')}
        </button>
        <button
          onClick={() => setActiveFilter('female')}
          className={`px-3 py-1 rounded-md text-sm ${
            activeFilter === 'female'
              ? 'bg-purple-600 text-white'
              : 'bg-gray-900 text-gray-300 hover:bg-gray-800'
          }`}
        >
          {t('femaleVoices')}
        </button>
        <button
          onClick={() => setActiveFilter('auto')}
          className={`px-3 py-1 rounded-md text-sm ${
            activeFilter === 'auto'
              ? 'bg-purple-600 text-white'
              : 'bg-gray-900 text-gray-300 hover:bg-gray-800'
          }`}
        >
          {t('autoVoice')}
        </button>
      </div>

      <div className="grid grid-cols-1 gap-2 max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700">
        {filteredVoices.map((voice) => (
          <button
            key={voice.id}
            onClick={() => onSelectVoice(voice.id)}
            className={`px-4 py-3 rounded-lg flex items-center justify-between transition-colors
              ${
                selectedVoice === voice.id
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-900 text-gray-300 hover:bg-gray-800'
              }`}
          >
            <span className="font-medium">{voice.name}</span>
            {selectedVoice === voice.id && (
              <span className="w-3 h-3 bg-white rounded-full"></span>
            )}
          </button>
        ))}
      </div>
    </div>
  )
}
