import { useTranslations } from 'next-intl'
import { getTranslations } from 'next-intl/server'
import HowTo from './components/HowTo'
import FeatureSection from './components/FeatureSection'
import FaqSection from './components/FaqSection'
import StatsSection from './components/StatsSection'
import TestimonialsSection from './components/TestimonialsSection'
import WhyLoveSection from './components/WhyLoveSection'
import CoverMusicLoader from './components/CoverMusicLoader'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('coverPageTitle'),
    description: t('coverPageDesc'),
    keywords: t('coverPageKeywords'),
  }
}

export default function CreatePage() {
  const t = useTranslations()

  return (
    <div className="w-full min-h-screen bg-gradient-to-b from-gray-900 to-[#120724]">
      {/* <LeftNav> */}
      <div className="px-6 pb-8 h-full overflow-y-auto">
        <div className=" mx-auto">
          {/* 标题区域 */}
          <div className="space-y-4 mb-12 mt-24">
            <h1 className="mx-auto text-white max-4xl text-balance font-bold text-3xl sm:text-4xl md:text-5xl lg:text-7xl sm:pb-8 xl:pb-8  animate-floating text-center">
              {t('coverAI')}
            </h1>
            <p className="text-lg font-bold text-white mb-4 px-2 text-center">
              {t('coverMusicDesc')}
            </p>
          </div>
          {/* 主内容区域 */}
          <div id="create-cover-section" className="max-w-6xl mx-auto">
            <CoverMusicLoader />
          </div>
        </div>

        {/* 新增统计区域 */}
        <StatsSection />

        {/* 为什么喜欢我们的AI歌曲翻唱工具 */}
        <WhyLoveSection />

        {/* 使用指南区域 */}
        <HowTo />

        {/* 功能特点区域 */}
        <FeatureSection />

        {/* 用户评价区域 */}
        <TestimonialsSection />

        {/* 常见问题区域 */}
        <FaqSection />
      </div>
      {/* </LeftNav> */}
    </div>
  )
}
