'use client'

import { useState } from 'react'
import CryptoPayment from '../(home)/components/CryptoPayment'
import { toast } from 'react-hot-toast'
import { useWeb3 } from '@/hooks/useWeb3'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'

interface Plan {
  id: number
  price: number
  credits: string | number
  description: string
  popular?: boolean
}

export default function PaymentPage() {
  const router = useRouter()
  const { switchNetwork } = useWeb3()
  const t = useTranslations()
  const locale = useLocale()

  // 创建计划数据，使用翻译字符串
  const plans: Plan[] = [
    {
      id: 1,
      price: 8.9,
      credits: 100,
      description: t('poemCreationInfo', { count: 33 }),
    },
    {
      id: 2,
      price: 15.9,
      credits: 900,
      description: t('poemCreationInfo', { count: 300 }),
      popular: true,
    },
    {
      id: 3,
      price: 39.5,
      credits: t('unlimited'),
      description: t('unlimitedPoemCreationInfo'),
    },
  ]

  const [selectedPlan, setSelectedPlan] = useState<Plan>(plans[1])

  const user = getUserFromClientCookies()

  const handleSuccess = async (txHash: string) => {
    console.log('Payment successful:', txHash)
    console.log('faith=============user.email - 用户支付成功', user?.email)

    if (!user?.email) {
      toast.error('User not authenticated')
      return
    }

    // 调用支付处理接口
    const response = await fetch('/api/webhooks/crypto', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        internalOrderId: selectedPlan.id, // 会员等级ID
        txHash: txHash, // 交易哈希
        email: user.email, // 用户邮箱
      }),
    })

    const data = await response.json()

    if (data.success) {
      toast.success('Payment processed successfully!')
      // 可以添加成功后的跳转
      router.push('/')
    } else {
      // 处理错误情况
      throw new Error(data.error)
    }

    toast.success('Payment successful!')
  }

  const handleError = (error: string) => {
    console.error('Payment failed:', error)
    toast.error('Payment failed: ' + error)
  }

  const handleBeforePayment = async () => {
    try {
      await switchNetwork(56)
      return true
    } catch (error) {
      toast.error('Failed to switch network. Please switch to BSC manually.')
      return false
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 pt-20">
      <div className="grid md:grid-cols-3 gap-6 mb-12 mt-12">
        {plans.map((plan) => (
          <div
            key={plan.id}
            className={`relative rounded-2xl p-8 transition-all duration-300 ${
              selectedPlan.id === plan.id
                ? 'bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-2 border-purple-400'
                : 'bg-[#1a1b1f] border border-gray-700 hover:border-purple-400/50'
            }`}
          >
            {plan.popular && (
              <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-medium px-4 py-1 rounded-full">
                  {t('popularPlan')}
                </span>
              </div>
            )}

            <div className="text-center">
              <div className="text-sm font-medium text-gray-400 mb-2">
                {t('credits')}
              </div>
              <div
                className={`text-4xl font-bold mb-2 ${
                  selectedPlan.id === plan.id ? 'text-purple-500' : 'text-white'
                }`}
              >
                {plan.credits}
              </div>
              <div className="text-2xl font-bold text-white mb-4">
                {plan.price} {t('usdtPrice')}
              </div>
              <div className="text-gray-400 text-sm mb-6">
                {plan.description}
              </div>
              <button
                onClick={() => setSelectedPlan(plan)}
                className={`w-full py-3 px-6 rounded-xl font-medium transition-colors ${
                  selectedPlan.id === plan.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                    : 'bg-transparent text-white border border-purple-400/50 hover:border-purple-400'
                }`}
              >
                {selectedPlan.id === plan.id ? t('selected') : t('selectPlan')}
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="max-w-md mx-auto">
        <CryptoPayment
          amount={selectedPlan.price}
          network={56}
          receiverAddress="******************************************"
          onSuccess={handleSuccess}
          onError={handleError}
          onBeforePayment={handleBeforePayment}
        />
      </div>

      <div className="text-center text-sm text-gray-400 mt-8">
        {t('disclaimer')}
        <br />
        {t('creditsCostInfo')}
      </div>
    </div>
  )
}
