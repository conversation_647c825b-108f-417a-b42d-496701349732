import React from 'react'
import { useTranslations } from 'next-intl'

const FaqSection = () => {
  const t = useTranslations()

  const faqs = [
    {
      id: '01',
      question: t('pricingFaq.payment.question'),
      answer: t('pricingFaq.payment.answer'),
    },
    {
      id: '02',
      question: t('pricingFaq.credits.question'),
      answer: t('pricingFaq.credits.answer'),
    },
    {
      id: '03',
      question: t('pricingFaq.subscription.question'),
      answer: t('pricingFaq.subscription.answer'),
    },
    {
      id: '04',
      question: t('pricingFaq.runningJobs.question'),
      answer: t('pricingFaq.runningJobs.answer'),
    },
    {
      id: '05',
      question: t('pricingFaq.license.question'),
      answer: t('pricingFaq.license.answer'),
    },
    {
      id: '06',
      question: t('pricingFaq.freeGen.question'),
      answer: t('pricingFaq.freeGen.answer'),
    },
    {
      id: '07',
      question: t('pricingFaq.storage.question'),
      answer: t('pricingFaq.storage.answer'),
    },
    {
      id: '08',
      question: t('pricingFaq.private.question'),
      answer: t('pricingFaq.private.answer'),
    },
  ]

  return (
    <section className="max-w-7xl mx-auto px-4 py-16">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 text-purple-500 !leading-tight">
          {t('pricingFaq.title')}
        </h2>
        <p className="text-gray-400 text-lg md:text-xl">
          {t('pricingFaq.subtitle')}
        </p>
      </div>

      <div className="grid gap-6 max-w-4xl mx-auto">
        {faqs.map((faq) => (
          <div
            key={faq.id}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 hover:border-purple-500/50 transition-colors"
          >
            <h3 className="text-xl font-semibold mb-4 text-white">
              <span className="text-purple-400">{faq.id}. </span>
              {faq.question}
            </h3>
            <p className="text-gray-400 leading-relaxed">{faq.answer}</p>
          </div>
        ))}
      </div>
    </section>
  )
}

export default FaqSection
