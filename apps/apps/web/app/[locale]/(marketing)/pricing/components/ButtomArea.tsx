import { ArrowRight } from 'lucide-react'
import { useTranslations } from 'next-intl'

const HeroSection = () => {
  const t = useTranslations()

  return (
    <section className="relative flex items-center py-12 md:py-16 max-sm:px-3">
      <div className="max-w-7xl mx-auto px-4">
        <div className="max-w-2xl">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-purple-500 !leading-tight">
            {t('pricingBottomArea.title')}
          </h1>

          <p className="text-lg md:text-xl text-gray-400 mb-6 leading-relaxed">
            {t('pricingBottomArea.description')}
          </p>

          <a
            href="/ai-music-generator"
            className="inline-flex items-center gap-2 px-5 py-2.5 text-base font-semibold text-white bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-500 hover:to-blue-500 transition-all duration-200 transform hover:scale-105"
          >
            {t('pricingBottomArea.createButton')}
            <ArrowRight className="w-4 h-4" />
          </a>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
