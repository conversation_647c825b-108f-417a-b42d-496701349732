import PricingSection from '../(home)/components/PricingSection'
import FaqSection from './components/FaqSection'
import BottomArea from './components/ButtomArea'
import { getTranslations } from 'next-intl/server'
import CryptoPayment from '../(home)/components/CryptoPayment'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('pricingPageTitle'),
    description: t('pricingPageDesc'),
    keywords: t('pricingPageKeywords'),
  }
}

export default async function PricingPage() {
  const t = await getTranslations()
  return (
    <main className="px-x pt-40 pb-24">
      <PricingSection />
      <FaqSection />
      <BottomArea />
      <div className="text-center mt-12 text-gray-400">
        <p>
          {t('pricingFaq.support.text')}{' '}
          <a
            href="mailto:<EMAIL>"
            className="text-purple-400 hover:text-purple-300 transition-colors"
          >
            <EMAIL>
          </a>
        </p>
      </div>
    </main>
  )
}
