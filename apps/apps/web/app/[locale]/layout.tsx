import { Toaster } from '@ui/components/toaster'
import { cn } from '@ui/lib'
import { Provider as <PERSON><PERSON>Provider } from 'jotai'
import type { Metadata } from 'next'
import { NextIntlClientProvider } from 'next-intl'
import { getLocale, getMessages } from 'next-intl/server'
import { Poppins } from 'next/font/google'
import NextTopLoader from 'nextjs-toploader'

// @ts-ignore
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'

import { ApiClientProvider } from '@shared/components/ApiClientProvider'
import { GradientBackgroundWrapper } from '@shared/components/GradientBackgroundWrapper'
import { ThemeProvider } from 'next-themes'
import './globals.css'
import 'cropperjs/dist/cropper.css'

import Script from 'next/script'
// import { fetchUserInfo } from '../lib/user'
import { AnalyticsPro } from './components/Analytics'
import { config } from '@config'
const sansFont = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-sans',
})

export const metadata: Metadata = {
  metadataBase: new URL('https://www.aimakesong.com'),
  alternates: {
    languages: {
      'x-default': '/',
      en: '/',
      de: '/de',
      es: '/es',
      fr: '/fr',
      ko: '/ko',
      pt: '/pt',
      ru: '/ru',
      th: '/th',
      vi: '/vi',
      'zh-Hans': '/zh-cn', // 改用 zh-Hans 表示简体中文
      'zh-Hant-HK': '/zh-hk', // 改用 zh-Hant-HK 表示香港繁体
      'zh-Hant': '/zh-tw', // 改用 zh-Hant 表示繁体中文
    },
    canonical: 'https://www.aimakesong.com',
  },
  // 添加更多元数据
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://www.aimakesong.com',
    title: 'AiMakeSong | AI Music & Song Generator - Royalty Free Song Maker',
    description:
      'Free AI Music Generator. Create unlimited royalty-free songs instantly. Get 5 free credits daily for AI music generation, vocal removal, and lyrics creation.', // 强调免费特性
    siteName: 'AiMakeSong',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AiMakeSong | AI Music & Song Generator - Royalty Free Song Maker',
    description:
      'Free AI Music Generator. Create unlimited royalty-free songs instantly. Get 5 free credits daily for AI music generation.', // 强调免费特性
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const locale = await getLocale()
  const messages = await getMessages()

  // const userInfo = (await fetchUserInfo()) || []

  // console.log('faith=============userInfo', userInfo)

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <AnalyticsPro />
        {/* <Script
          id="cookieyes"
          src="https://cdn-cookieyes.com/client_data/47ccd9726d4a7ceee7531281/script.js"
          strategy="afterInteractive"
        />
        <style>{`
          div[data-radix-popper-content-wrapper] {
            z-index: 9999 !important;
          }
        `}</style> */}
      </head>
      <body
        className={cn(
          'min-h-screen bg-background font-sans text-foreground antialiased',
          sansFont.variable
        )}
      >
        {/* <iframe
          src="https://www.googletagmanager.com/ns.html?id=GTM-KNZ4JKML"
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        /> */}
        <NextTopLoader color="var(--colors-primary)" />
        <NextIntlClientProvider locale={locale} messages={messages}>
          <ThemeProvider
            attribute="class"
            disableTransitionOnChange
            enableSystem
            defaultTheme={config.ui.defaultTheme}
            themes={config.ui.enabledThemes}
          >
            <ApiClientProvider>
              {/* <JotaiProvider initialValues={[[userAtom, userInfo]]}> */}
              <JotaiProvider>
                <GradientBackgroundWrapper>
                  {children}
                </GradientBackgroundWrapper>
              </JotaiProvider>
            </ApiClientProvider>
          </ThemeProvider>
          <Toaster />
        </NextIntlClientProvider>
        {/* vercel speed */}
        <SpeedInsights />

        {/* vercel */}
        <Analytics />
      </body>
    </html>
  )
}
