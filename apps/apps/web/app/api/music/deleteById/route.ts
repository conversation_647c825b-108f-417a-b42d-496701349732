import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'
const API_TOKEN = 'sk-f52e6bfc417c41338e7c0cc21dbeefc9'
const USER_ID = '413564'
const BASE_URL = 'https://dzwlai.com/apiuser/_open/suno'

/**
 * 处理kie提供商的音乐删除逻辑
 * @param {string} id - 要删除的音频ID
 * @returns {Promise<NextResponse>} - API响应
 */
async function handleKieProviderDeletion(id: string) {
  try {
    // 查询任务以确定要更新的字段
    const { data: taskData, error: queryError } = await supabase
      .from('song_task_list')
      .select('*')
      .or(`audio1_id.eq.${id},audio2_id.eq.${id}`)
      .single()

    if (queryError) {
      console.error('Database query error:', queryError)
      return NextResponse.json(
        { error: 'Failed to query database' },
        { status: 500 }
      )
    }

    if (!taskData) {
      return NextResponse.json(
        { error: 'No matching record found' },
        { status: 404 }
      )
    }

    // 确定要更新的字段
    const updateFields: any = {}
    if (taskData.audio1_id === id) {
      updateFields.audioUrl_1 = null
      updateFields.imageUrl_1 = null
    } else if (taskData.audio2_id === id) {
      updateFields.audioUrl_2 = null
      updateFields.imageUrl_2 = null
    }

    // 更新数据库记录
    const { error: updateError } = await supabase
      .from('song_task_list')
      .update(updateFields)
      .match({ id: taskData.id })

    if (updateError) {
      console.error('Database update error:', updateError)
      return NextResponse.json(
        { error: 'Failed to update database' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      code: 200,
      msg: 'Successfully deleted',
      providerId: 'kie',
    })
  } catch (error) {
    console.error('Error in kie provider deletion:', error)
    return NextResponse.json(
      {
        error: 'Failed to process kie provider deletion',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

export async function GET(req: Request) {
  try {
    // 获取 URL 参数
    const { searchParams } = new URL(req.url)
    const userId = searchParams.get('user_id') || USER_ID
    const providerId = searchParams.get('providerId')
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'id is required' }, { status: 400 })
    }

    // 使用封装的方法处理kie提供商
    if (providerId === 'kie') {
      return await handleKieProviderDeletion(id)
    }

    const response = await fetch(`${BASE_URL}/music/deleteById?id=${id}`, {
      method: 'GET',
      headers: {
        'x-token': API_TOKEN,
        'x-userId': userId,
        'Content-Type': 'application/json',
      },
    })

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch music list' },
      { status: 500 }
    )
  }
}
