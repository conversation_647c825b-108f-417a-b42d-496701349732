import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'
import {
  musicGenerationService,
  apiProviderRouter,
  createMockResponse,
  type GenerateMusicRequest,
  type RoutingContext,
  type ApiProvider,
} from './service'

// 常量配置 - API相关配置信息（保持向后兼容）
const API_CONFIG = {
  TOKEN: 'sk-f52e6bfc417c41338e7c0cc21dbeefc9', // 默认令牌（向后兼容）
  DEFAULT_USER_ID: '413564', // 默认用户ID（用于未登录用户）
  BASE_URL: 'https://dzwlai.com/apiuser/_open/suno', // 默认API基础URL（向后兼容）
  MIN_POINTS_REQUIRED: 3, // 生成音乐所需的最低积分
} as const
import { encryptVerification } from '../../../utils'
//
const API_TOKEN = 'sk-f52e6bfc417c41338e7c0cc21dbeefc9'
const USER_ID = '413564'
const BASE_URL = 'https://dzwlai.com/apiuser/_open/suno'

// 处理音乐标签的转换
const processMusicTags = (tags: string | undefined): string => {
  if (!tags) return ''

  // 定义需要转换的标签映射
  const tagMappings: Record<string, string> = {
    'Женский голос': 'Female Voice',
    'Мужской голос': 'Male Voice',
    'Детский голос': 'Children Voice',
    Сопрано: 'Soprano',
    Альто: 'Alto',
    Тенор: 'Tenor',
    Бас: 'Bass',
    Сказочный: 'Fairy Tale',
  }

  let processedTags = tags
  // 遍历所有需要转换的标签
  Object.entries(tagMappings).forEach(([original, replacement]) => {
    if (processedTags.includes(original)) {
      processedTags = processedTags.replace(original, replacement)
    }
  })

  return processedTags
}

export const sendWeChatMessage = (openid: string, content: string) => {
  fetch('https://aimakesong.com/api/wechat/message', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ openid, productName: content }),
  }).catch((error) => {
    console.error('Failed to send WeChat notification:', error)
  })
}

// 验证来源域名是否合法
const isValidReferer = (referer: string): boolean => {
  return referer.includes('www.aimakesong.com') || referer.includes('3000')
}

// 检查是否为被阻止的用户ID
const isBlockedUserId = (userId: string): boolean => {
  const lowerUserId = userId.toLowerCase()
  // 阻止特定组织或域名的用户
  return lowerUserId.includes('gocheon') || lowerUserId.includes('goe.go.kr')
}

// 验证用户函数 - 检查用户是否存在且积分充足
const validateUser = async (userId: string) => {
  console.log('userId', userId)
  // 从数据库查询用户信息和积分
  const { data: userExists, error: userError } = await supabase
    .from('song_user')
    .select('id,points,membership_status,generator_num')
    .eq('email', userId)
    .single()
  console.log('userError', userError)
  console.log('userExists', userExists)
  // 如果用户不存在或查询出错，抛出异常
  if (!userExists || userError) {
    throw new Error('User not found or database error')
  }

  // 检查用户积分是否足够
  if (userExists.points < API_CONFIG.MIN_POINTS_REQUIRED) {
    throw new Error('Insufficient points')
  }

  return userExists
}

// 保存任务数据到数据库
const saveTaskData = async (
  body: GenerateMusicRequest,
  userId: string,
  provider: ApiProvider,
  data: any,
  userInfo: any
) => {
  // 根据输入类型选择对应的提示词
  const prompt =
    body.inputType === '10' ? body.gptDescriptionPrompt : body.prompt

  // 构建任务数据对象
  const taskData = {
    user_id: userId, // 用户ID
    prompt: prompt || '', // 提示词内容
    title:
      (body.title || prompt?.substring(0, 50) || 'Untitled') +
      ' #' +
      Math.floor(100000 + Math.random() * 900000), // 生成唯一标题（加随机数后缀）
    task_id: data?.taskBatchId, // 任务ID（稍后从API响应中获取）
    created_at: new Date().toISOString(), // 创建时间
    inputType: body.inputType, // 输入类型
    makeInstrumental: body.makeInstrumental, // 是否纯音乐
    continueClipId: body.continueClipId, // 续写片段ID
    continueAt: body.continueAt, // 续写时间点
    mvVersion: body.mvVersion, // 模型版本
    callbackUrl: body.callbackUrl, // 回调URL
    tags: body.tags || '', // 风格标签
    api_provider_id: provider.id, // 使用的API商家ID
  }

  // 将任务数据插入到数据库
  const { data: insertedData, error: insertError } = await supabase
    .from('song_task_list')
    .insert(taskData)
    .select()

  // 如果插入失败，记录错误日志
  if (insertError) {
    console.error('Failed to save task data:', insertError)
  }

  // generator_num +1
  if (userInfo && typeof userInfo.generator_num === 'number') {
    await supabase
      .from('song_user')
      .update({ generator_num: userInfo.generator_num + 1 })
      .eq('email', userId)
  }

  return insertedData
}

// 主要的POST请求处理函数
export async function POST(req: Request) {
  try {
    // 解析请求体数据
    const body: GenerateMusicRequest = await req.json()

    // 处理标签
    body.tags = processMusicTags(body.tags)

    // 获取请求头信息
    const country = req.headers.get('x-vercel-ip-country') || 'unknown' // 用户所在国家
    const referer = req.headers.get('referer') || 'unknown' // 请求来源
    const userId = body.user_id || '' // 用户ID

    // 记录请求详情（用于调试）
    console.log('Request details:', { country, referer, userId })

    // 1. 处理无用户ID的情况（返回模拟响应）
    // 对于未登录用户，返回一个模拟的成功响应
    // 获取原始prompt
    const originalPrompt = body.prompt || ''

    // 后端自己生成加密值
    const backendEncrypted = await encryptVerification(originalPrompt)

    // 从请求头获取前端传来的加密值
    const frontendEncrypted = req.headers.get('X-Prompt')

    console.log('frontendEncrypted', frontendEncrypted)
    console.log('backendEncrypted', backendEncrypted)

    // 验证加密值是否匹配
    if (body.makeInstrumental === 'false') {
      if (!frontendEncrypted || frontendEncrypted !== backendEncrypted) {
        console.error('Encryption mismatch:', {
          userId: body.user_id,
          frontend: frontendEncrypted,
          backend: backendEncrypted,
        })
        return NextResponse.json(
          { error: 'Fiail', msg: 'Failure' },
          { status: 403 }
        )
      }
    }

    // 检查用户ID是否为空
    if (!body.user_id) {
      return NextResponse.json(createMockResponse())
    }

    // 2. 检查被阻止的用户ID
    // 阻止特定组织或恶意用户的访问
    if (isBlockedUserId(userId)) {
      return NextResponse.json(
        { code: 509, msg: 'Access denied' },
        { status: 509 }
      )
    }

    // 3. 地区限制检查
    // 对特定国家/地区进行访问限制
    if (country === 'IN') {
      return NextResponse.json(
        { code: 403, msg: 'Service not available in your region' },
        { status: 403 }
      )
    }

    // 4. Referer验证
    // 确保请求来自合法的域名，防止API被滥用
    if (!isValidReferer(referer)) {
      console.log(`Blocked request with invalid referer: ${referer}`)
      return NextResponse.json(
        {
          error: 'Access forbidden: Invalid referer',
          msg: 'Access forbidden',
        },
        { status: 403 }
      )
    }

    // 5. 用户验证
    // 验证用户是否存在且有足够的积分
    let userInfo
    try {
      userInfo = await validateUser(userId)
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      return NextResponse.json(
        {
          code: 200,
          msg:
            errorMessage === 'User not found or database error'
              ? 'System busy' // 对用户友好的错误提示
              : errorMessage,
        },
        { status: 200 }
      )
    }

    // 6. 构建路由上下文并调用音乐生成服务
    const userType = apiProviderRouter.getUserType(userInfo)

    // 记录用户分类信息
    console.log(
      `User classification: userId=${userId}, points=${userInfo.points}, membershipStatus=${userInfo.membership_status}, userType=${userType}`
    )

    // 根据用户类型自动选择API提供商
    let preferredProvider = body.preferredProvider
    if (!preferredProvider) {
      if (userType === 'angel') {
        // preferredProvider = 'zhuge' // 天使用户使用suno_primary
        preferredProvider = 'kie' // 降低成本不用诸葛
        console.log('Angel user detected, using zhuge')
      } else if (userType === 'vip') {
        preferredProvider = 'kie' // VIP用户使用kie
        console.log('VIP user detected, using kie')
      }
      // 其他用户类型（free、premium）使用默认路由策略
    }

    const routingContext: RoutingContext = {
      userType,
      region: country,
      manualProviderId: preferredProvider,
      requestLoad: 1, // 可以根据实际情况计算
      isRetry: false,
    }

    // 调用音乐生成服务
    const result = await musicGenerationService.generateMusic(
      body,
      routingContext
    )

    if (!result.success) {
      return NextResponse.json(
        {
          code: result.error === 'No available API provider' ? 503 : 500,
          msg: result.error || 'Music generation failed',
        },
        { status: result.error === 'No available API provider' ? 503 : 500 }
      )
    }

    // 7. 保存任务数据到数据库
    // 记录用户的音乐生成请求，便于后续跟踪和管理
    await saveTaskData(body, userId, result.provider, result.data, userInfo)

    console.log('userInfo-userInfo', userInfo)
    console.log('body-body', body)

    // 调用发送消息函数
    sendWeChatMessage(
      'ooAOG6WFy1y3J-3WsjWMs1ZtlxYU',
      `${userInfo.id}、${userInfo.generator_num}、${body?.title?.slice(0, 10)} `
    )

    return NextResponse.json(result)
  } catch (error) {
    // 统一的错误处理
    console.error('Music generation error:', error)

    return NextResponse.json(
      {
        error: 'Failed to generate music', // 用户友好的错误信息
        details: error instanceof Error ? error.message : 'Unknown error', // 详细错误信息（用于调试）
      },
      { status: 500 }
    )
  }
}
