// API商家路由服务 - 封装多商家选择和路由逻辑

// 工具函数 - 创建模拟响应数据（用于未登录用户）
export const createMockResponse = () => ({
  code: 200,
  msg: '您已成功提交创作任务，目前剩余并发任务数为44',
  data: {
    taskBatchId: '1131267846660685825', // 模拟的任务批次ID
    taskStatus: 'create', // 任务状态
    items: [
      {
        id: '1924388190114873345', // 任务项ID
        inputType: '20', // 输入类型
        makeInstrumental: false, // 非纯音乐
        prompt: '你好 再见 起伏不定\n伴随着笑容 也有眼泪湿...', // 歌词内容
        title: '你好，再见', // 歌曲标题
        tags: 'Jazz Hip Hop,Blues,sb,sb,sb', // 风格标签
        progress: 0, // 进度百分比
        continueClipId: '', // 续写片段ID
        status: 0, // 状态码
        progressMsg: 'Waiting in queue...', // 进度消息
      },
      {
        id: '1924388190114873346',
        inputType: '20',
        makeInstrumental: false,
        prompt: '你好 再见 起伏不定\n伴随着笑容 也有眼泪湿...',
        title: '你好，再见',
        tags: 'Jazz Hip Hop,Blues',
        progress: 0,
        continueClipId: '',
        status: 0,
        progressMsg: 'Waiting in queue...sb',
      },
    ],
  },
})

// API商家配置 - 支持多个音乐生成服务商
export interface ApiProvider {
  id: string // 商家唯一标识
  name: string // 商家名称
  baseUrl: string // API基础URL
  token: string // 访问令牌
  headers: Record<string, string> // 请求头配置
  priority: number // 优先级（数字越小优先级越高）
  isActive: boolean // 是否启用
  userTypes: ('free' | 'premium' | 'vip' | 'angel')[] // 支持的用户类型
  maxConcurrent: number // 最大并发数
  costPerRequest: number // 每次请求成本
  qualityLevel: 'basic' | 'standard' | 'premium' | 'ultra' // 服务质量等级
  responseTimeMs: number // 平均响应时间
  successRate: number // 成功率（0-1）
  regions: string[] // 支持的地区
}

// 路由上下文
export interface RoutingContext {
  userType: 'free' | 'premium' | 'vip' | 'angel'
  region?: string
  manualProviderId?: string // 手动指定的商家ID
  requestLoad?: number // 当前请求负载
  isRetry?: boolean // 是否为重试请求
}

// API商家配置列表
const API_PROVIDERS: ApiProvider[] = [
  {
    id: 'zhuge',
    name: 'zhuge',
    baseUrl: 'https://dzwlai.com/apiuser/_open/suno/music/generate',
    token: 'sk-f52e6bfc417c41338e7c0cc21dbeefc9',
    headers: {
      'x-token': 'sk-f52e6bfc417c41338e7c0cc21dbeefc9',
      'Content-Type': 'application/json',
    },
    priority: 1,
    isActive: true,
    userTypes: ['premium', 'vip', 'angel'],
    maxConcurrent: 100,
    costPerRequest: 0.1,
    qualityLevel: 'premium',
    responseTimeMs: 3000,
    successRate: 0.95,
    regions: ['US', 'EU', 'CN'],
  },
  {
    id: 'kie',
    name: 'kie',
    baseUrl: 'https://kieai.erweima.ai/api/v1/generate',
    token: 'sk-f52e6bfc417c41338e7c0cc21dbeefc9',
    headers: {
      Authorization: 'Bearer b8e434f8bb38f9f2fb2b3803bab5ab3c',
      'Content-Type': 'application/json',
    },
    priority: 0, // 最高优先级给天使用户
    isActive: true,
    userTypes: ['angel'],
    maxConcurrent: 200,
    costPerRequest: 0.05, // 天使用户享受更低成本
    qualityLevel: 'ultra',
    responseTimeMs: 2000, // 更快响应
    successRate: 0.98, // 更高成功率
    regions: ['US', 'EU', 'CN'],
  },
  {
    id: 'suno_backup',
    name: 'Suno备用服务',
    baseUrl: 'https://backup-api.example.com/suno/music/generate',
    token: 'sk-backup-token-here',
    headers: {
      'x-token': 'sk-backup-token-here',
      'Content-Type': 'application/json',
    },
    priority: 2,
    isActive: true,
    userTypes: ['free', 'premium', 'vip', 'angel'],
    maxConcurrent: 50,
    costPerRequest: 0.05,
    qualityLevel: 'standard',
    responseTimeMs: 5000,
    successRate: 0.9,
    regions: ['CN', 'AS'],
  },
  {
    id: 'alternative_provider',
    name: '替代服务商',
    baseUrl: 'https://alternative-api.example.com/music/generate',
    token: 'sk-alternative-token',
    headers: {
      'API-Key': 'sk-alternative-token',
      'Client-ID': 'music-app',
      'Content-Type': 'application/json',
    },
    priority: 3,
    isActive: true,
    userTypes: ['free'],
    maxConcurrent: 30,
    costPerRequest: 0.03,
    qualityLevel: 'basic',
    responseTimeMs: 7000,
    successRate: 0.85,
    regions: ['AS', 'IN'],
  },
]

// 智能路由选择器
export class ApiProviderRouter {
  private providers = API_PROVIDERS

  /**
   * 获取用户类型
   * @param user 用户信息
   * @returns 用户类型
   */
  getUserType(user: any): 'free' | 'premium' | 'vip' | 'angel' {
    if (!user) return 'free'

    const points = user.points || 0
    const membershipStatus = user.membership_status

    // 积分 = 5 的时候是天使用户
    if (points == 5) {
      return 'angel'
    }

    // 积分 < 3 的是免费用户
    if (points < 3) {
      return 'free'
    }

    // membershipStatus = 'active' 并且积分 > 5 的是 vip 用户
    if (membershipStatus === 'active' && points > 5) {
      return 'vip'
    }

    // 其他情况为高级用户（积分 >= 3 但不满足其他条件）
    return 'premium'
  }
}

// 音乐生成请求接口
export interface GenerateMusicRequest {
  user_id?: string // 用户ID（可选）
  inputType: string // 输入类型：'10'=描述模式，'20'=自定义歌词模式
  makeInstrumental: string // 是否生成纯音乐版本
  title?: string // 歌曲标题（可选）
  continueClipId: string // 续写音频片段ID
  continueAt: string // 续写起始时间点
  mvVersion: string // 音频模型版本
  callbackUrl: string // 回调URL
  prompt?: string // 自定义歌词内容
  gptDescriptionPrompt?: string // AI描述生成提示词
  tags?: string // 音乐风格标签
  preferredProvider?: string // 手动指定的API商家ID
  taskBatchId?: string // 任务批次ID
  data?: any // 其他数据
}

// API调用结果接口
export interface ApiCallResult {
  success: boolean
  data?: any
  error?: string
  provider: ApiProvider
  responseTime?: number
  retryCount?: number
}

// 音乐生成服务类
export class MusicGenerationService {
  private router = new ApiProviderRouter()

  /**
   * 验证Kie API请求的有效性
   * @param kieRequest Kie格式的请求参数
   * @returns 验证结果和错误信息
   */
  private validateKieRequest(kieRequest: any): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // 验证必填字段
    if (!kieRequest.model) {
      errors.push('model is required')
    } else if (!['V3_5', 'V4', 'V4_5'].includes(kieRequest.model)) {
      errors.push('model must be one of: V3_5, V4, V4_5')
    }

    if (typeof kieRequest.customMode !== 'boolean') {
      errors.push('customMode is required and must be boolean')
    }

    if (typeof kieRequest.instrumental !== 'boolean') {
      errors.push('instrumental is required and must be boolean')
    }

    if (!kieRequest.callBackUrl) {
      errors.push('callBackUrl is required')
    }

    // 自定义模式下的验证
    if (kieRequest.customMode) {
      if (!kieRequest.style) {
        errors.push('style is required in custom mode')
      }
      if (!kieRequest.title) {
        errors.push('title is required in custom mode')
      }

      // 非器乐模式需要prompt
      if (!kieRequest.instrumental && !kieRequest.prompt) {
        errors.push(
          'prompt is required when instrumental is false in custom mode'
        )
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * 转换请求参数为Suno格式
   * @param request 通用请求参数
   * @returns Suno API格式的参数
   */
  private transformToSunoFormat(request: GenerateMusicRequest): any {
    // Suno使用原始格式，不需要转换
    request.callbackUrl =
      'https://www.aimakesong.com/api/music/success-callback'
    return request
  }

  /**
   * 转换请求参数为Kie格式
   * @param request 通用请求参数
   * @returns Kie API格式的参数
   */
  private transformToKieFormat(request: GenerateMusicRequest): any {
    // 处理model：映射版本号（必填）
    let model = 'V4' // 默认版本
    if (request.mvVersion) {
      const versionMap: Record<string, string> = {
        // 'chirp-v4-5': 'V4_5',
        'chirp-v4-5': 'V4',
        'chirp-v4': 'V4',
        'chirp-v3': 'V3_5',
        'chirp-v3-5': 'V3_5',
      }
      model = versionMap[request.mvVersion] || 'V3_5'
    }

    // 处理instrumental：将字符串转换为布尔值（必填）
    const instrumental = request.makeInstrumental === 'true'

    // 处理customMode：始终设置为true以获得更多控制（必填）
    const customMode = true

    // 处理title：自定义模式下必填，最大80字符
    let title = request.title || 'Untitled'
    if (title.length > 80) {
      title = title.substring(0, 80)
      console.warn(`Title truncated to 80 characters: ${title}`)
    }

    // 处理style：自定义模式下必填，根据模型设置字符限制
    let style = request.tags || 'Pop'
    const styleMaxLength = model === 'V4_5' ? 1000 : 200 // V4_5: 1000字符，V3_5和V4: 200字符
    if (style.length > styleMaxLength) {
      style = style.substring(0, styleMaxLength)
      console.warn(
        `Style truncated to ${styleMaxLength} characters for model ${model}: ${style}`
      )
    }

    if (instrumental) {
      style = request.gptDescriptionPrompt
    }

    // 处理prompt：自定义模式下，当instrumental为false时必填
    let prompt = ''
    if (!instrumental) {
      prompt = request.prompt || request.gptDescriptionPrompt || ''

      // 根据模型设置字符限制
      let promptMaxLength: number
      switch (model) {
        case 'V4_5':
          promptMaxLength = 5000
          break
        case 'V4':
        case 'V3_5':
        default:
          promptMaxLength = 3000
          break
      }

      if (prompt.length > promptMaxLength) {
        prompt = prompt.substring(0, promptMaxLength)
        console.warn(
          `Prompt truncated to ${promptMaxLength} characters for model ${model}`
        )
      }

      // 如果是非器乐模式但没有prompt，给出警告
      if (!prompt) {
        console.warn(
          'No prompt provided for non-instrumental mode, using default'
        )
        prompt = 'A beautiful melody'
      }
    }

    // 处理callBackUrl：必填
    const callBackUrl = 'https://www.aimakesong.com/api/music/success-callback'

    // 处理negativeTags：可选
    const negativeTags = '' // 可以根据需要从其他字段推导

    // 构建Kie API请求格式
    const kieRequest = {
      prompt: prompt,
      style: style,
      title: title,
      customMode: customMode,
      instrumental: instrumental,
      model: model,
      callBackUrl: callBackUrl,
      negativeTags: negativeTags,
    }

    // 如果有continueClipId，可能需要添加额外参数（根据Kie API文档）
    if (request.continueClipId) {
      ;(kieRequest as any).continueFrom = request.continueClipId
    }

    console.log(
      `Kie request validation: instrumental=${instrumental}, prompt.length=${prompt.length}, style.length=${style.length}, title.length=${title.length}`
    )

    // 验证请求参数的有效性
    const validationResult = this.validateKieRequest(kieRequest)
    if (!validationResult.isValid) {
      console.error(`Kie request validation failed:`, validationResult.errors)
      // 不抛出错误，而是记录警告，让API调用继续进行
      console.warn('Proceeding with potentially invalid Kie request')
    }

    return kieRequest
  }

  /**
   * 转换请求参数为通用格式（用于其他提供商）
   * @param request 通用请求参数
   * @returns 通用格式的参数
   */
  private transformToGenericFormat(request: GenerateMusicRequest): any {
    // 保持原始格式作为通用格式
    return request
  }

  /**
   * 根据API提供商转换请求参数
   * @param request 原始请求参数
   * @param providerId API提供商ID
   * @returns 转换后的请求参数
   */
  private transformRequestByProvider(
    request: GenerateMusicRequest,
    providerId: string
  ): any {
    console.log(`Transforming request for provider: ${providerId}`)

    switch (providerId) {
      case 'zhuge':
      case 'suno_backup':
        return this.transformToSunoFormat(request)

      case 'kie':
        return this.transformToKieFormat(request)

      default:
        return this.transformToGenericFormat(request)
    }
  }

  /**
   * 根据用户类型获取API配置
   * @param userType 用户类型
   * @param manualProviderId 手动指定的提供商ID
   * @returns API提供商配置
   */
  private getProviderByUserType(
    userType: string,
    manualProviderId?: string
  ): ApiProvider | null {
    // 如果手动指定了提供商，优先使用
    if (manualProviderId) {
      const provider = API_PROVIDERS.find(
        (p) => p.id === manualProviderId && p.isActive
      )
      if (provider) return provider
    }

    // 根据用户类型选择默认提供商
    switch (userType) {
      case 'angel':
        return API_PROVIDERS.find((p) => p.id === 'zhuge' && p.isActive) || null
      case 'vip':
        return API_PROVIDERS.find((p) => p.id === 'kie' && p.isActive) || null
      case 'premium':
      case 'free':
      default:
        return (
          API_PROVIDERS.find(
            (p) => p.userTypes.includes(userType as any) && p.isActive
          ) || null
        )
    }
  }

  /**
   * 调用API生成音乐（简化版本）
   * @param request 音乐生成请求
   * @param context 路由上下文
   * @returns API调用结果
   */
  async generateMusic(
    request: GenerateMusicRequest,
    context: RoutingContext
  ): Promise<ApiCallResult> {
    const startTime = Date.now()

    // 直接根据用户类型获取API配置
    const provider = this.getProviderByUserType(
      context.userType,
      context.manualProviderId
    )

    if (!provider) {
      return {
        success: false,
        error: `No available API provider for user type: ${context.userType}`,
        provider: {} as ApiProvider,
      }
    }

    try {
      console.log(
        `Direct API call: ${provider.name} (${provider.id}) for user type: ${context.userType}`
      )

      // 根据API提供商转换请求参数
      const transformedRequest = this.transformRequestByProvider(
        request,
        provider.id
      )
      console.log(
        `Transformed request for ${provider.id}:`,
        JSON.stringify(transformedRequest, null, 2)
      )

      if (request.user_id?.includes('<EMAIL>')) {
        transformedRequest.model = 'V4_5'
      }

      // 使用提供商配置的headers，并添加用户ID
      const headers = {
        ...provider.headers,
        'x-userId': request.user_id || '',
      }

      console.log('faith=============transformedRequest', transformedRequest)

      // 直接调用API
      const response = await fetch(provider.baseUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(transformedRequest),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      // 如果是 kie provider，将 taskId 转换为 taskBatchId
      if (provider.name === 'kie' && data.data?.taskId) {
        data.data.taskBatchId = data.data.taskId
        delete data.data.taskId
      }

      const responseTime = Date.now() - startTime

      return {
        success: true,
        code: 200,
        msg: 'success',
        provider: {
          id: provider.id,
        },
        responseTime,
        ...data,
      }
    } catch (error) {
      console.error(`API call failed for provider ${provider.name}:`, error)

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        provider,
        responseTime: Date.now() - startTime,
        retryCount: 0,
      }
    }
  }

  /**
   * 获取路由器实例（用于外部配置）
   * @returns API路由器
   */
  getRouter(): ApiProviderRouter {
    return this.router
  }
}

// 导出服务实例
export const musicGenerationService = new MusicGenerationService()
export const apiProviderRouter = new ApiProviderRouter()
