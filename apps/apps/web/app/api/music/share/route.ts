import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'

export async function POST(req: Request) {
  try {
    const body = await req.json()
    const {
      clipId,
      title,
      prompt,
      tags,
      inputType,
      status,
      cld2AudioUrl,
      cld2ImageUrl,
      progressMsg,
      userInfo,
    } = body

    // 首先检查该音乐是否已经存在于数据库
    const { data: existingData } = await supabase
      .from('song_share')
      .select('clip_id')
      .eq('clip_id', clipId)
      .single()

    // 如果记录已存在，直接返回该记录的clip_id
    if (existingData) {
      return NextResponse.json({
        success: true,
        message: 'Music already shared',
        id: clipId,
      })
    }

    // 如果记录不存在，插入数据到song_share表
    const { data, error } = await supabase
      .from('song_share')
      .insert({
        clip_id: clipId,
        title,
        prompt,
        tags,
        input_type: inputType,
        status,
        cld2_audio_url: cld2AudioUrl,
        cld2_image_url: cld2ImageUrl,
        progress_msg: progressMsg,
        user_id: userInfo?.id || null,
        user_name: userInfo?.email || null,
        view_count: 0,
        share_count: 0,
        deleted: false,
      })
      .select('clip_id')
      .single()

    if (error) {
      console.error('Error sharing music:', error)
      return NextResponse.json(
        { error: 'Failed to share music' },
        { status: 500 }
      )
    }

    // 返回clip_id
    return NextResponse.json({
      success: true,
      message: 'Music shared successfully',
      id: clipId,
    })
  } catch (error) {
    console.error('Error in share API:', error)
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}
