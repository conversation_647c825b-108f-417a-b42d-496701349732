import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'

interface ZhugeItem {
  id: number
  cld2AudioUrl: string
  cld2ImageUrl: string
  status: number
  progress: number
  progressMsg: string
}

interface ZhugeData {
  code: number
  msg: string
  data: {
    taskBatchId: number
    taskStatus: string
    items: ZhugeItem[]
  }
}

interface KieItem {
  id: string
  audio_url: string
  stream_audio_url: string
  image_url: string
  prompt: string
  model_name: string
  title: string
  tags: string
  createTime: string
  duration: number
  cld2AudioUrl: string
  cld2ImageUrl: string
}

interface KieData {
  code: number
  msg: string
  data: {
    callbackType: string
    task_id: string
    data: KieItem[]
  }
}

interface ExtractedData {
  taskId: string | number
  items: {
    id: string | number
    audioUrl: string
    imageUrl: string
    title?: string
    tags?: string
    duration?: number
    cld2AudioUrl?: string
    cld2ImageUrl?: string
  }[]
}

function extractDataFromResponse(response: any): ExtractedData {
  // Check if it's a Kie response
  if ('task_id' in response.data) {
    const kieData = response as KieData
    return {
      taskId: kieData.data.task_id,
      items: kieData.data.data.map((item) => ({
        id: item.id,
        audioUrl: item.audio_url,
        imageUrl: item.image_url,
      })),
    }
  }

  // Otherwise it's a Zhuge response
  if ('taskBatchId' in response.data) {
    const zhugeData = response as ZhugeData
    return {
      taskId: zhugeData.data.taskBatchId,
      items: zhugeData.data.items.map((item) => ({
        id: item.id,
        audioUrl: item.cld2AudioUrl,
        imageUrl: item.cld2ImageUrl,
      })),
    }
  }

  // Default case - return empty data
  return {
    taskId: '',
    items: [],
  }
}

export async function POST(req: Request) {
  try {
    // Get the raw body data
    const body = await req.json()

    console.log(
      'Success Callback Received Data:',
      JSON.stringify(body, null, 2)
    )

    // Extract data using the helper function
    const extractedData = extractDataFromResponse(body)

    // Log the extracted data
    console.log('Extracted Data:', JSON.stringify(extractedData, null, 2))

    // taskStatus = finished 或者 callbackType = complete 时候，要更新表 song_task_list
    if (
      body.data.taskStatus === 'finished' ||
      body.data.callbackType === 'complete'
    ) {
      const { taskId, items } = extractedData
      console.log('🚀 ~ items:', items)

      // 0 未开始
      // 1 爬虫
      // 2 进行中
      // 3 已完成

      // 确保至少有两个项目
      if (items.length >= 2) {
        try {
          const updateData = {
            audioUrl_1: items[0].audioUrl,
            imageUrl_1: items[0].imageUrl,
            audioUrl_2: items[1].audioUrl,
            imageUrl_2: items[1].imageUrl,
            status: 3,
            updated_at: new Date().toISOString(),
            audio1_id: items[0].id,
            audio2_id: items[1].id,
          }

          console.log(
            'Updating database with data:',
            JSON.stringify(updateData, null, 2)
          )

          const { data, error } = await supabase
            .from('song_task_list')
            .update(updateData)
            .eq('task_id', taskId.toString())

          if (error) {
            console.error('Error updating database:', error)
          } else {
            console.log(
              'Successfully updated URLs in database for task:',
              taskId
            )
            console.log('Updated data:', JSON.stringify(data, null, 2))
          }
        } catch (error) {
          console.error('Error updating database:', error)
        }
      } else {
        console.warn('Not enough items to update URLs for task:', taskId)
      }
    }

    // Return a success response with extracted data
    return NextResponse.json({
      success: true,
      message: 'Callback received successfully',
      data: extractedData,
    })
  } catch (error) {
    console.error('Error processing callback:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process callback',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
