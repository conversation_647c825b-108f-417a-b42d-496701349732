// apps/web/app/api/music/generate-lyrics/route.ts
import { NextResponse } from 'next/server'

const CLAUDE_API_KEY = 'sk-4cba64337e3d7d7d98dc5253129fae310e5e7c0e730cAxDz'
const BASE_URL = 'https://api.gptsapi.net/v1/messages'

export async function POST(req: Request) {
  try {
    const {
      description,
      styles = [],
      era = 'Modern',
      region = 'English',
      tempo = 120,
      duration = 3,
    } = await req.json()

    // 验证必要参数
    if (!description) {
      return NextResponse.json(
        { error: 'Description is required' },
        { status: 400 }
      )
    }

    // 获取歌曲结构描述
    const getLyricsStructure = (minutes: number) => {
      switch (minutes) {
        case 1:
          return 'Create a short, impactful song with 1 verse and 1 chorus. Keep it concise and memorable.'
        case 2:
          return 'Create a song with 2 verses and 2 choruses. Include a bridge section for variety.'
        case 3:
          return 'Create a full song with 3 verses, 3 choruses, and a bridge. Make it complete and engaging.'
        default:
          return 'Create a song with 2 verses and 2 choruses.'
      }
    }

    // 获取速度术语
    const getTempoTerm = (bpm: number): string => {
      if (bpm <= 60) return 'Largo/Lento'
      if (bpm <= 108) return 'Andante'
      if (bpm <= 115) return 'Moderato'
      if (bpm <= 168) return 'Allegro'
      if (bpm <= 200) return 'Presto'
      return 'Prestissimo'
    }

    // 获取速度描述
    const getTempoDescription = (bpm: number): string => {
      if (bpm <= 60) return 'Very slow and stately'
      if (bpm <= 108) return 'At a walking pace'
      if (bpm <= 115) return 'Moderate speed'
      if (bpm <= 168) return 'Fast, quick and bright'
      if (bpm <= 200) return 'Very fast'
      return 'Extremely fast'
    }

    // 构建提示词
    const prompt = `You are a professional songwriter. Create lyrics for a song based on the following requirements:

Description: ${description}
Music Style: ${Array.isArray(styles) ? styles.join(', ') : styles}
Era: ${era}
Language/Region: ${region}
Tempo: ${tempo} BPM (${getTempoTerm(tempo)} - ${getTempoDescription(tempo)})
Duration: ${duration} minutes

${getLyricsStructure(duration)}

Requirements:
- Write lyrics in ${region} language
- Match the ${era} style and era characteristics
- Incorporate the specified music styles and elements
- Create engaging, emotional, and memorable lyrics
- Ensure the lyrics flow well with the specified tempo
- Make the song suitable for the specified duration
- Include proper verse, chorus, and bridge structure
- Use natural language and avoid forced rhymes
- Create lyrics that tell a story or convey emotion

Return ONLY the lyrics text, no explanations or formatting.`

    // 发送请求到Claude API
    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
        'x-api-key': CLAUDE_API_KEY,
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307',
        max_tokens: 5000,
        messages: [{ role: 'user', content: prompt }],
        temperature: 1.0,
      }),
    })

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`)
    }

    // 解析响应
    const data = await response.json()
    let lyricsText = ''

    // 提取文本内容
    if (Array.isArray(data?.content) && data.content[0]?.type === 'text') {
      lyricsText = data.content[0].text
    } else if (typeof data?.content === 'string') {
      lyricsText = data.content
    } else if (data?.content?.text) {
      lyricsText = data.content.text
    } else {
      lyricsText = data?.text || ''
    }

    if (!lyricsText.trim()) {
      return NextResponse.json(
        { error: 'No lyrics were generated', success: false },
        { status: 500 }
      )
    }

    // 构建歌曲数据
    const songData = {
      lyrics: lyricsText.trim(),
      metadata: {
        generatedAt: new Date().toISOString(),
        wordCount: lyricsText.split(/\s+/).length,
        lineCount: lyricsText.split('\n').filter((line) => line.trim()).length,
        parameters: {
          description,
          styles,
          era,
          region,
          tempo,
          duration,
        },
      },
    }

    return NextResponse.json({
      song: songData,
      success: true,
    })
  } catch (error: any) {
    console.error('Lyrics generation error:', error)
    return NextResponse.json(
      {
        error: `Failed to generate lyrics: ${error.message}`,
        success: false,
      },
      { status: 500 }
    )
  }
}
