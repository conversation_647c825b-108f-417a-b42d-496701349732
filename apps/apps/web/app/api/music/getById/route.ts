import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'

export async function GET(req: Request) {
  try {
    // 获取 URL 参数
    const { searchParams } = new URL(req.url)
    const clipId = searchParams.get('id')

    if (!clipId) {
      return NextResponse.json(
        { error: 'Music ID is required' },
        { status: 400 }
      )
    }

    const { data: shareData, error: shareError } = await supabase
      .from('song_share')
      .select('*')
      .eq('clip_id', clipId)
      .eq('deleted', false)
      .single()

    if (shareData && !shareError) {
      // 更新查看次数
      await supabase
        .from('song_share')
        .update({ view_count: (shareData.view_count || 0) + 1 })
        .eq('id', shareData.id)

      // 将数据转换为客户端期望的格式
      const formattedData = {
        id: shareData.clip_id, // 返回clip_id作为id
        inputType: shareData.input_type,
        prompt: shareData.prompt,
        title: shareData.title,
        tags: shareData.tags,
        clipId: shareData.clip_id,
        status: shareData.status,
        cld2AudioUrl: shareData.cld2_audio_url,
        cld2ImageUrl: shareData.cld2_image_url,
        progressMsg: shareData.progress_msg,
        user_id: shareData.user_id,
        user_name: shareData.user_name,
        created_at: shareData.created_at,
        view_count: (shareData.view_count || 0) + 1,
        share_count: shareData.share_count || 0,
      }

      return NextResponse.json({
        code: 0,
        data: formattedData,
      })
    }

    // 如果未找到记录
    return NextResponse.json({ error: 'Music not found' }, { status: 404 })
  } catch (error) {
    console.error('Error fetching music data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch music data' },
      { status: 500 }
    )
  }
}
