// apps/web/app/api/music/generateLyrics/route.ts
import { NextResponse } from 'next/server'
const API_TOKEN = 'sk-f52e6bfc417c41338e7c0cc21dbeefc9'
const USER_ID = '413564'
const BASE_URL = 'https://dzwlai.com/apiuser/_open/suno'

export async function POST(req: Request) {
  try {
    const body = await req.json()

    const response = await fetch(`${BASE_URL}/music/generateLyrics`, {
      method: 'POST',
      headers: {
        'x-token': API_TOKEN!,
        'x-userId': body.user_id || USER_ID,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to generate music' },
      { status: 500 }
    )
  }
}
