// 定义 zhuge 的响应格式
export interface ZhugeResponse {
  code: number
  msg: string
  data: {
    taskBatchId: string
    items: Array<{
      id: string
      progress: number
      status: number
      cld2AudioUrl: string
      progressMsg: string
      cld2ImageUrl: string
      inputType?: string
      makeInstrumental?: boolean
      prompt?: string
      title?: string
      tags?: string
      clipId?: string
      duration?: number
      waitNum?: number
      continueClipId?: string
      errorMessage?: string
    }>
    errorMessage?: string
  }
}

// 更新 kie 的响应格式
export interface KieResponse {
  code: number
  msg: string
  data: {
    taskId: string
    parentMusicId: string
    param: string
    response: {
      taskId: string
      sunoData: Array<{
        id: string
        audioUrl: string
        sourceAudioUrl: string
        streamAudioUrl: string
        sourceStreamAudioUrl: string
        imageUrl: string
        sourceImageUrl: string
        prompt: string
        modelName: string
        title: string
        tags: string
        createTime: number
        duration: number
      }>
    }
    status: string // 'PENDING' | 'TEXT_SUCCESS' | 'FIRST_SUCCESS' | 'SUCCESS' | 'CREATE_TASK_FAILED' | 'GENERATE_AUDIO_FAILED' | 'CALLBACK_EXCEPTION' | 'SENSITIVE_WORD_ERROR'
    type: string
    operationType: string
    errorCode: string | null
    errorMessage: string | null
    createTime: number
  }
}

// 转换函数：将 kie 的响应转换为 zhuge 的格式
export function transformKieToZhugeFormat(
  kieResponse: KieResponse
): ZhugeResponse {
  // 解析参数字符串
  let paramObj = {}
  try {
    if (kieResponse.data.param) {
      paramObj = JSON.parse(kieResponse.data.param)
    }
  } catch (error) {
    console.error('参数解析失败:', error)
  }

  // 状态映射
  let statusCode = 30 // 默认完成状态

  // 根据状态设置不同的状态码
  switch (kieResponse.data.status) {
    case 'PENDING':
      statusCode = 10 // 处理中
      break
    case 'TEXT_SUCCESS':
    case 'FIRST_SUCCESS':
      statusCode = 20 // 部分完成
      break
    case 'SUCCESS':
      statusCode = 30 // 全部完成
      break
    case 'CREATE_TASK_FAILED':
    case 'GENERATE_AUDIO_FAILED':
    case 'CALLBACK_EXCEPTION':
    case 'SENSITIVE_WORD_ERROR':
      statusCode = 40 // 错误状态
      break
    default:
      statusCode = 30 // 默认完成
  }

  // 处理特定错误消息
  let errorMessage = kieResponse.data.errorMessage || ''

  // 对于版权限制错误，设置自定义消息
  if (
    kieResponse.data.status === 'GENERATE_AUDIO_FAILED' &&
    kieResponse.data.errorCode == 413
  ) {
    errorMessage = 'copyright'
  }

  // 构建转换后的响应
  const items = []

  // 如果有sunoData，处理每个音轨数据
  if (
    kieResponse.data.response &&
    kieResponse.data.response.sunoData &&
    kieResponse.data.response.sunoData.length > 0
  ) {
    for (const track of kieResponse.data.response.sunoData) {
      items.push({
        id: track.id || '',
        inputType: '20',
        makeInstrumental: (paramObj as any).instrumental || false,
        prompt: track.prompt || (paramObj as any).prompt || '',
        title: track.title || (paramObj as any).title || '',
        tags: track.tags || (paramObj as any).tags || '',
        clipId: kieResponse.data.taskId,
        duration: track.duration || 0,
        progress: statusCode === 20 ? 60 : 100,
        waitNum: 0,
        continueClipId: '',
        status: statusCode,
        cld2AudioUrl:
          track.sourceAudioUrl || track.streamAudioUrl || track.audioUrl || '',
        cld2ImageUrl: track.sourceImageUrl || track.imageUrl || '',
        progressMsg:
          statusCode === 30
            ? 'productionCompleted'
            : statusCode === 40
            ? getErrorMsgByStatus(kieResponse.data.status, errorMessage)
            : 'processingTask',
        errorMessage: statusCode === 40 ? errorMessage : '',
      })
    }
  }
  // 如果没有sunoData或者sunoData为空，但是有状态，创建一个虚拟项来表示当前状态
  else if (kieResponse.data.status) {
    // 创建一个虚拟项来表示当前任务状态
    items.push({
      id: `pending-${kieResponse.data.taskId}`,
      inputType: '20',
      makeInstrumental: (paramObj as any).instrumental || false,
      prompt: (paramObj as any).prompt || '',
      title: (paramObj as any).title || '处理中的任务',
      tags: (paramObj as any).tags || '',
      clipId: kieResponse.data.taskId,
      duration: 0, // 未知时长
      progress:
        statusCode === 10
          ? 30
          : statusCode === 20
          ? 60
          : statusCode === 30
          ? 100
          : 0, // 根据状态显示进度
      waitNum: 0,
      continueClipId: '',
      status: statusCode,
      cld2AudioUrl: '', // 还没有音频
      cld2ImageUrl: '', // 还没有图片
      progressMsg:
        statusCode === 40
          ? getErrorMsgByStatus(kieResponse.data.status, errorMessage)
          : getProgressMessageByStatus(kieResponse.data.status),
      errorMessage: statusCode === 40 ? errorMessage : '',
    })
  }

  return {
    code: kieResponse.code,
    msg: kieResponse.msg,
    data: {
      taskBatchId: kieResponse.data.taskId,
      items: items,
      errorMessage: statusCode === 40 ? errorMessage : '', // 顶层也添加错误消息
    },
  }
}

// 根据状态获取进度消息
function getProgressMessageByStatus(status: string): string {
  switch (status) {
    case 'PENDING':
      return 'processingTask'
    case 'TEXT_SUCCESS':
      return 'lyricsGenerated'
    case 'FIRST_SUCCESS':
      return 'firstTrackGenerated'
    case 'SUCCESS':
      return 'productionCompleted'
    case 'CREATE_TASK_FAILED':
      return 'createTaskFailed'
    case 'GENERATE_AUDIO_FAILED':
      return 'audioGenerationFailed'
    case 'CALLBACK_EXCEPTION':
      return 'callbackException'
    case 'SENSITIVE_WORD_ERROR':
      return 'sensitiveWordError'
    default:
      return 'processingTask'
  }
}

// 根据错误状态和消息获取显示消息
function getErrorMsgByStatus(status: string, errorMessage: string): string {
  // 如果有特定的错误消息，优先使用它
  if (errorMessage) {
    return errorMessage
  }

  // 否则使用默认错误消息
  switch (status) {
    case 'CREATE_TASK_FAILED':
      return 'createTaskFailed'
    case 'GENERATE_AUDIO_FAILED':
      return 'audioGenerationFailed'
    case 'CALLBACK_EXCEPTION':
      return 'callbackException'
    case 'SENSITIVE_WORD_ERROR':
      return 'sensitiveWordError'
    default:
      return 'processingFailed'
  }
}
