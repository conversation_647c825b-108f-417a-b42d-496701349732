// apps/web/app/api/music/status/route.ts
import { NextResponse } from 'next/server'
import { transformKieToZhugeFormat } from './service'

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const taskBatchId = searchParams.get('taskBatchId')

    if (!taskBatchId) {
      return NextResponse.json(
        { error: 'TaskBatchId is required' },
        { status: 400 }
      )
    }

    // 使用 kie 的查询逻辑
    const response = await fetch(
      `https://kieai.erweima.ai/api/v1/generate/record-info?taskId=${taskBatchId}`,
      {
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer b8e434f8bb38f9f2fb2b3803bab5ab3c`,
        },
      }
    )
    const kieData = await response.json()
    console.log('kieData111', kieData)
    // 转换 kie 的响应格式为 zhuge 的格式
    const data = transformKieToZhugeFormat(kieData)

    return NextResponse.json(data)
  } catch (error) {
    console.error('Status query error:', error)
    return NextResponse.json(
      { error: 'Failed to query status' },
      { status: 500 }
    )
  }
}
