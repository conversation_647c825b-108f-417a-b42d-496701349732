import { NextResponse } from 'next/server'

const CLAUDE_API_KEY = 'sk-4cba64337e3d7d7d98dc5253129fae310e5e7c0e730cAxDz'
const BASE_URL = 'https://api.gptsapi.net/v1/messages'

export const maxDuration = 30

const DESCRIPTION_PROMPT = `Create a creative and vivid music description based on this title and its language:
"{title}"

Requirements:
1. Length: 50-200 characters
2. Include music style, mood, or scene description
3. Be creative and poetic
4. Keep the language style consistent with input language
5. No quotes or special characters

Expected output format: String only
Invalid: Any explanatory text or JSON format
Example: "A dreamy lo-fi melody, perfect for stargazing on summer nights"

Function: Return single description string`

export async function POST(req: Request) {
  try {
    const { title, lang = 'en' } = await req.json()

    // 验证必要参数
    if (!title) {
      return NextResponse.json({ error: 'Title is required' }, { status: 400 })
    }

    // 使用统一的提示词
    const prompt = DESCRIPTION_PROMPT.replace('{title}', title)

    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
        'x-api-key': CLAUDE_API_KEY,
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307',
        max_tokens: 200,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.9, // 稍微提高创造性
      }),
    })

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`)
    }

    const data = await response.json()
    const description = data.content[0].text.trim()

    return NextResponse.json({
      description: description,
      language: lang,
      success: true,
    })
  } catch (error) {
    console.error('Error generating music description:', error)
    return NextResponse.json(
      { error: 'Failed to generate music description' },
      { status: 500 }
    )
  }
}
