import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'
import { URL } from 'url'

// 添加一个辅助函数来统一设置CORS头
function createResponseWithCors(body: any, status: number = 200) {
  return new NextResponse(JSON.stringify(body), {
    status: status,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Content-Type': 'application/json',
    },
  })
}

// 反馈进度
const handleMoProgressMonitoring = async (
  endpoint: string,
  requestInfo: any
) => {
  if (endpoint === '/api/feed/v2') {
    try {
      // 解析响应数据
      let responseData
      if (typeof requestInfo.responseBody === 'string') {
        responseData = JSON.parse(requestInfo.responseBody)
      } else {
        responseData = requestInfo.responseBody
      }

      const clips = responseData?.clips || []
      if (!clips.length) {
        console.log('No clips found in response data')
        return
      }

      let task_pid = '' // 用Set来存储唯一的task_pid值
      // const allComplete = clips.every((clip: any) => clip.status === 'complete')

      // 为每个 clip 处理更新
      const updatePromises = clips.map(async (clip: any) => {
        if (!clip.id) {
          console.log('Clip without ID, skipping:', clip)
          return null
        }

        // 查找匹配的进度记录
        const { data: progressRecords, error: queryError } = await supabase
          .from('song_task_progress')
          .select('*')
          .eq('song_stask_id', clip.id)

        if (queryError) {
          console.error(
            `Error finding progress record for clip ${clip.id}:`,
            queryError
          )
          return null
        }

        if (!progressRecords || progressRecords.length === 0) {
          console.log(`No matching progress record found for clip ${clip.id}`)
          return null
        }

        // 如果有多条记录，发出警告
        if (progressRecords.length > 1) {
          console.warn(
            `Multiple progress records found for clip ${clip.id}. Using first one.`
          )
        }

        const progressRecord = progressRecords[0]
        console.log(
          `Found progress record for clip ${clip.id}:`,
          progressRecord
        )

        task_pid = progressRecord.task_pid

        // 准备更新数据
        const updateData: any = {
          updated_at: new Date().toISOString(),
          status: clip.status, // 保留当前状态，除非后面判断需要更改
          audio_url: clip.audio_url || progressRecord.audio_url || '',
          avatar_image_url: clip.image_url,
        }

        // 更新数据库
        const { data: updatedRecord, error: updateError } = await supabase
          .from('song_task_progress')
          .update(updateData)
          .eq('id', progressRecord.id)
          .select()

        if (updateError) {
          console.error(
            `Error updating progress record for clip ${clip.id}:`,
            updateError
          )
          return null
        }

        //
        console.log(
          `Successfully updated progress record for clip ${clip.id}:`,
          updatedRecord
        )
        return updatedRecord
      })

      // 等待所有更新完成
      const results = await Promise.all(updatePromises)

      // task_pid

      // 更新song_task_list的status为3
      // if (allComplete && task_pid) {
      //   await supabase
      //     .from('song_task_list')
      //     .update({ status: 3 })
      //     .eq('id', task_pid)
      //     .select()
      // }

      const successfulUpdates = results.filter((result) => result !== null)

      console.log(
        `Successfully updated ${successfulUpdates.length} of ${clips.length} clips`
      )
      return successfulUpdates
    } catch (error) {
      console.error('Error in handleMoProgressMonitoring:', error)
      return null
    }
  }
  return null
}
const handleGenerateStep = async (endpoint: string, requestInfo: any) => {
  if (endpoint === '/api/generate/v2-web/') {
    try {
      // 解析响应数据
      let responseData
      if (typeof requestInfo.responseBody === 'string') {
        responseData = JSON.parse(requestInfo.responseBody)
      } else {
        responseData = requestInfo.responseBody
      }
      const clips = responseData?.clips || []

      // 提取生成音乐的 prompt，可能为空
      const generationPrompt = responseData?.metadata?.prompt || ''
      const defaultTitle = (responseData?.clips[0]?.title || '').trim()

      console.log(`Generation prompt: "${generationPrompt}"`)
      console.log(`Default title: "${defaultTitle}"`)

      // 构建查询条件
      let query = supabase.from('song_task_list').select('*').eq('status', 0)

      // 添加 prompt 条件（如果有）
      // if (generationPrompt) {
      //   query = query.eq('prompt', generationPrompt)
      // }

      // 添加 title 条件（如果有）
      if (defaultTitle) {
        query = query.eq('title', defaultTitle)
      }

      // 执行查询
      const { data: matchingTasks, error } = await query

      if (error) {
        console.error('Error querying song_task_list:', error)
        return null
      }

      // 检查是否有多个匹配结果（bug 情况）
      if (matchingTasks && matchingTasks.length > 1) {
        console.error(
          'BUG DETECTED: Multiple matching tasks found:',
          matchingTasks
        )
        // 记录错误日志或发送警报

        // 决定如何处理这种情况 - 这里选择使用第一个匹配的任务
        console.log(
          'Proceeding with the first matching task:',
          matchingTasks[0]
        )
      }

      // 获取单个匹配的任务（如果有）
      const matchingTask =
        matchingTasks && matchingTasks.length > 0 ? matchingTasks[0] : null

      if (matchingTask) {
        console.log('Found matching task:', matchingTask)

        // 如果任务没有标题或标题为空，使用默认标题
        const taskTitle = matchingTask.title || defaultTitle

        // 更新任务信息
        const { data: updatedTask, error: updateError } = await supabase
          .from('song_task_list')
          .update({
            status: 2, // 更新状态为"进行中"
            task_id: responseData.id, // 主生成 ID
            updated_at: new Date().toISOString(),
          })
          .eq('id', matchingTask.id)
          .select()

        const progressRecords = []

        for (const clip of clips) {
          // 准备进度记录数据
          const progressRecord = {
            task_pid: matchingTask.id, // 关联到主任务
            song_stask_id: clip.id, // 当前 clip 的 ID
            status: 'streaming', // 初始状态，例如 0 表示"待处理"
            title: clip.title || defaultTitle,
            prompt: clip.metadata.prompt,
            created_at: new Date().toISOString(),
            tags: clip.tags,
            major_model_version: clip.model_name,
          }

          // 插入进度记录
          const { data: insertedProgress, error: insertError } = await supabase
            .from('song_task_progress')
            .insert(progressRecord)
            .select()

          if (insertError) {
            console.error(
              `Error creating progress record for clip ${clip.id}:`,
              insertError
            )
          } else {
            console.log(
              `Created progress record for clip ${clip.id}:`,
              insertedProgress
            )
            progressRecords.push(insertedProgress)
          }
        }

        if (updateError) {
          console.error(`Error updating task ${matchingTask.id}:`, updateError)
        } else {
          console.log(
            `Successfully updated task ${matchingTask.id}:`,
            updatedTask
          )
        }

        return updatedTask
      } else {
        console.log('No matching task found with the given conditions')
        return null
      }
    } catch (error) {
      console.error('Error in handleGenerateStep:', error)
      return null
    }
  }
  // 不是目标端点，直接返回
  return null
}
export async function POST(req: Request) {
  try {
    // 解析请求体
    const requestData = await req.json()

    // 验证请求数据格式
    if (!requestData || !Array.isArray(requestData)) {
      return createResponseWithCors(
        {
          success: false,
          message:
            'Invalid data format. Expected array of request info objects.',
        },
        400
      )
    }

    // 处理请求数据并插入数据库
    const insertPromises = requestData.map(async (requestInfo) => {
      // 基本验证
      if (!requestInfo.url || !requestInfo.method) {
        console.error('Invalid request info:', requestInfo)
        return null
      }

      try {
        // 解析 URL 获取附加信息
        const parsedUrl = new URL(requestInfo.url)
        const endpoint = parsedUrl.pathname

        await handleGenerateStep(endpoint, requestInfo)
        await handleMoProgressMonitoring(endpoint, requestInfo)

        // 提取查询参数
        const queryParams = {}
        parsedUrl.searchParams.forEach((value, key) => {
          queryParams[key] = value
        })

        // 计算响应大小
        const responseSize =
          typeof requestInfo.responseBody === 'string'
            ? Buffer.byteLength(requestInfo.responseBody, 'utf8')
            : 0

        // 响应时间
        const responseTime = requestInfo.response_time || 0

        // 准备插入数据库的记录
        const record = {
          request_timestamp: requestInfo.timestamp || new Date().toISOString(),
          url: requestInfo.url,
          endpoint: endpoint,
          method: requestInfo.method,
          request_type: requestInfo.type || 'Unknown',

          request_headers: requestInfo.requestHeaders || {},
          request_params: queryParams,
          request_body: requestInfo.requestBody || null,
          content_type:
            requestInfo.requestHeaders?.['content-type'] ||
            requestInfo.requestHeaders?.['Content-Type'] ||
            null,

          response_status: requestInfo.responseStatus,
          response_headers: requestInfo.responseHeaders || {},
          response_body: requestInfo.responseBody || null,
          response_size: responseSize,
          response_time: responseTime,

          user_agent: req.headers.get('user-agent') || null,
          // 使用请求中提供的业务类型，或默认值
          business_type: requestInfo.business_type || 'music_api',
        }

        // 插入数据库
        const { data, error } = await supabase
          .from('song_crawler')
          .insert(record)

        console.log('faith=================----1111', data, error)

        if (error) {
          console.error('Error inserting request data:', error)
          return null
        }

        return { success: true, url: requestInfo.url }
      } catch (error) {
        console.error('Error processing request info:', error, requestInfo)
        return null
      }
    })

    // 等待所有插入操作完成
    const results = await Promise.all(insertPromises)
    const successfulInserts = results.filter((r) => r !== null).length

    // 返回响应，使用辅助函数添加CORS头
    return createResponseWithCors({
      success: true,
      message: `Successfully processed ${successfulInserts} of ${requestData.length} requests`,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('API error:', error)
    return createResponseWithCors(
      {
        success: false,
        message: 'Internal server error processing crawler data',
        error: error.message,
      },
      500
    )
  }
}

// 处理 CORS 预检请求
export async function OPTIONS(req: Request) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
