// apps/web/app/api/music/analyze-song/route.ts
import { NextResponse } from 'next/server'

const CLAUDE_API_KEY = 'sk-4cba64337e3d7d7d98dc5253129fae310e5e7c0e730cAxDz'
const BASE_URL = 'https://api.gptsapi.net/v1/messages'

export async function POST(req: Request) {
  try {
    const { songName, songRegion } = await req.json()

    // 验证必要参数
    if (!songName || !songRegion) {
      return NextResponse.json(
        { error: 'Song name and region are required' },
        { status: 400 }
      )
    }

    // 构建提示词
    const prompt = `You are a professional music analyst. Please analyze the following song and provide its musical styles, elements, and characteristics.

Song name: ${songName}
Country/Region: ${songRegion}

Rules:
- Only include styles, sub-genres, and musical influences that are truly relevant to this specific song, based on its actual language, artist, and musical characteristics.
- Do NOT include unrelated genres.
- Do NOT use the label 'ballad' unless the song is truly a folk ballad or narrative ballad in the traditional sense.
- If you are not sure about a style or influence, do not include it.
- If possible, first identify the main language of the song and use it to help determine the correct genre.

Return ONLY a valid JSON object, do not include any explanation, markdown, or extra text. The JSON format must be:
{
  "styles": [ ... ],
  "elements": [ ... ],
  "description": "..."
}`

    // 发送请求到Claude API
    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
        'x-api-key': CLAUDE_API_KEY,
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307',
        max_tokens: 3000,
        messages: [{ role: 'user', content: prompt }],
        temperature: 1.0,
      }),
    })

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`)
    }

    // 解析响应
    const data = await response.json()
    let contentText = ''

    // 提取文本内容
    if (Array.isArray(data?.content) && data.content[0]?.type === 'text') {
      contentText = data.content[0].text
    } else if (typeof data?.content === 'string') {
      contentText = data.content
    } else if (data?.content?.text) {
      contentText = data.content.text
    } else {
      contentText = data?.text || ''
    }

    try {
      // 清理和解析JSON
      let cleanText = contentText.trim()
      if (cleanText.startsWith('```json')) {
        cleanText = cleanText.replace(/^```json\s*/, '').replace(/\s*```$/, '')
      } else if (cleanText.startsWith('```')) {
        cleanText = cleanText.replace(/^```\s*/, '').replace(/\s*```$/, '')
      }

      const result = JSON.parse(cleanText)

      // 验证结果格式
      if (!result || typeof result !== 'object') {
        throw new Error('Invalid result format')
      }

      // 确保属性存在
      if (!Array.isArray(result.styles)) result.styles = []
      if (!Array.isArray(result.elements)) result.elements = []
      if (typeof result.description !== 'string') result.description = ''

      return NextResponse.json({
        analysis: result,
        success: true,
      })
    } catch (parseError: any) {
      console.error('JSON parsing error:', parseError)
      return NextResponse.json(
        {
          error: `Failed to parse analysis result: ${parseError.message}`,
          success: false,
        },
        { status: 500 }
      )
    }
  } catch (error: any) {
    console.error('Analysis error:', error)
    return NextResponse.json(
      {
        error: `Analysis failed: ${error.message}`,
        success: false,
      },
      { status: 500 }
    )
  }
}
