// apps/web/app/api/music/status/route.ts
import { NextResponse } from 'next/server'
import { getUserId } from '../../../utils/lib'
import { transformKieToZhugeFormat } from './service'

const API_TOKEN = 'sk-f52e6bfc417c41338e7c0cc21dbeefc9'
const USER_ID = '413564'
const BASE_URL = 'https://dzwlai.com/apiuser/_open/suno'

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const taskBatchId = searchParams.get('taskBatchId')
    const user_id = searchParams.get('user_id') || USER_ID
    const explicitProviderId = searchParams.get('providerId')

    if (!taskBatchId) {
      return NextResponse.json(
        { error: 'TaskBatchId is required' },
        { status: 400 }
      )
    }

    // 根据 taskBatchId 的格式判断提供商
    let providerId = explicitProviderId
    if (!providerId) {
      if (taskBatchId.length === 19 && /^\d+$/.test(taskBatchId)) {
        providerId = 'zhuge'
      } else if (taskBatchId.length === 32 && /[a-zA-Z]/.test(taskBatchId)) {
        providerId = 'kie'
      } else {
        providerId = 'zhuge' // 默认使用 zhuge
      }
    }

    let data
    if (providerId === 'zhuge') {
      // 使用原有的 zhuge 查询逻辑
      const response = await fetch(
        `${BASE_URL}/music/getState?taskBatchId=${taskBatchId}`,
        {
          headers: {
            'x-token': API_TOKEN!,
            'x-userId': user_id,
          },
        }
      )
      data = await response.json()
    } else if (providerId === 'kie') {
      // 使用 kie 的查询逻辑
      const response = await fetch(
        `https://kieai.erweima.ai/api/v1/generate/record-info?taskId=${taskBatchId}`,
        {
          headers: {
            Accept: 'application/json',
            Authorization: `Bearer b8e434f8bb38f9f2fb2b3803bab5ab3c`,
          },
        }
      )
      const kieData = await response.json()
      console.log('kieData111', kieData)
      // 转换 kie 的响应格式为 zhuge 的格式
      data = transformKieToZhugeFormat(kieData)
    } else {
      // 默认使用 zhuge 的查询逻辑
      const response = await fetch(
        `${BASE_URL}/music/getState?taskBatchId=${taskBatchId}`,
        {
          headers: {
            'x-token': API_TOKEN!,
            'x-userId': user_id,
          },
        }
      )
      data = await response.json()
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Status query error:', error)
    return NextResponse.json(
      { error: 'Failed to query status' },
      { status: 500 }
    )
  }
}
