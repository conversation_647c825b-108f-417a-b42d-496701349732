import { NextResponse } from 'next/server'

const API_BASE_URL = 'https://dzwlai.com/apiuser/_open/suno/music'

export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File

    const HEADERS = {
      'x-token': 'sk-f52e6bfc417c41338e7c0cc21dbeefc9',
      'x-userId': (formData.get('user_id') as string) || '413564',
    }

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    const apiFormData = new FormData()
    apiFormData.append('file', file)

    const response = await fetch(`${API_BASE_URL}/stemsByAudio`, {
      method: 'POST',
      headers: HEADERS,
      body: apiFormData,
    })

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error processing audio:', error)
    return NextResponse.json(
      { error: 'Failed to process audio' },
      { status: 500 }
    )
  }
}
