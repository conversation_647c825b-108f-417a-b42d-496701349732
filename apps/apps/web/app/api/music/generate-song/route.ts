import { NextResponse } from 'next/server'

const CLAUDE_API_KEY = 'sk-4cba64337e3d7d7d98dc5253129fae310e5e7c0e730cAxDz'
const BASE_URL = 'https://api.gptsapi.net/v1/messages'

export const maxDuration = 30

const SONG_PROMPT = `Create complete song lyrics based on this description:
"{description}"

Core Requirements:
1. Language must match the description language
2. Create professional song structure:
   - Intro (2-4 lines) - Set the mood and theme
   - First Verse (8-12 lines) - Establish the story/emotion
   - Chorus (4-8 lines) - Memorable hook with emotional impact
   - Second Verse (8-12 lines) - Develop the narrative further
   - Chorus (repeated) - Reinforce the main message
   - Bridge (4-8 lines) - Provide contrast or resolution
   - Final Chorus - Emotional climax
   - Outro (2-4 lines) - Gentle conclusion

Creative Guidelines:
1. Maintain consistent rhyme scheme (AABB, ABAB, or ABCB patterns)
2. Match specified mood, style, and emotion throughout
3. Build complete emotional/story arc with clear progression
4. Use fresh metaphors, vivid imagery, and sensory details
5. Create memorable, singable chorus with strong hook
6. Ensure syllable count consistency within sections
7. Make chorus suitable for repetition without losing impact
8. Use appropriate vocabulary level for the target audience
9. Include emotional peaks and valleys for dynamic flow
10. Consider the musical rhythm and phrasing

Style Elements:
- Match genre conventions (Pop/Rock/Folk/etc.) and era characteristics
- Use appropriate language level and cultural references
- Include vivid imagery, emotions, and relatable themes
- Consider tempo and rhythm in word choice and line length
- Maximum length: 2000 characters

Technical Requirements:
- Maintain consistent meter and rhythm patterns
- Use alliteration, assonance, and consonance for musicality
- Create natural speech patterns that flow when sung
- Balance repetition with variety for listener engagement
- Ensure each section serves a distinct purpose in the song

Output Format: 
- Pure lyrics with line breaks only
- No section labels or explanatory text
- No formatting or metadata
- Clear visual separation between sections

Example Output:
[Intro]
(intro lyrics)

[Verse 1]
(verse lyrics)

[Chorus]
(chorus lyrics)
...etc.

Function: Return clean lyrics text with proper line breaks only`

export async function POST(req: Request) {
  try {
    const { description, lang = 'en' } = await req.json()

    // 验证必要参数
    if (!description) {
      return NextResponse.json(
        { error: 'Description is required' },
        { status: 400 }
      )
    }

    // 使用统一的提示词
    const prompt = SONG_PROMPT.replace('{description}', description)

    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
        'x-api-key': CLAUDE_API_KEY,
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307',
        max_tokens: 1200,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 1.0, // 最大创造性
      }),
    })

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`)
    }

    const data = await response.json()
    const lyrics = data.content[0].text.trim()

    // 添加一些元数据
    const songData = {
      lyrics,
      metadata: {
        language: lang,
        generatedAt: new Date().toISOString(),
        wordCount: lyrics.split(/\s+/).length,
        verseCount: (lyrics.match(/verse/gi) || []).length,
      },
    }

    return NextResponse.json({
      song: songData,
      language: lang,
      success: true,
    })
  } catch (error) {
    console.error('Error generating song:', error)
    return NextResponse.json(
      { error: 'Failed to generate song' },
      { status: 500 }
    )
  }
}
