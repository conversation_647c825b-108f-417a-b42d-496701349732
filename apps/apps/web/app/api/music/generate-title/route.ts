import { NextResponse } from 'next/server'

const CLAUDE_API_KEY = 'sk-4cba64337e3d7d7d98dc5253129fae310e5e7c0e730cAxDz'
const BASE_URL = 'https://api.gptsapi.net/v1/messages'

const TITLE_PROMPT = `Create 5 creative song titles based on this content and its language (max 20 chars):
"{content}"
Expected output format: JSON array only
Invalid: Any explanatory text or non-JSON content
Example: ["Title 1","Title 2","Title 3","Title 4","Title 5"]
Function: Return titles array`

export async function POST(req: Request) {
  try {
    const { content, lang = 'en' } = await req.json()

    // 验证必要参数
    if (!content) {
      return NextResponse.json(
        { error: 'Content is required' },
        { status: 400 }
      )
    }

    // 使用统一的提示词
    const prompt = TITLE_PROMPT.replace('{content}', content)

    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
        'x-api-key': CLAUDE_API_KEY,
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307',
        max_tokens: 100,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.8,
      }),
    })

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`)
    }

    const data = await response.json()
    const songTitle = data.content[0].text.trim()

    return NextResponse.json({
      title: songTitle,
      language: lang,
      success: true,
    })
  } catch (error) {
    console.error('Error generating song title:', error)
    return NextResponse.json(
      { error: 'Failed to generate song title' },
      { status: 500 }
    )
  }
}
