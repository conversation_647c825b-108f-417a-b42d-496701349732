// apps/web/app/api/music/myMusics/route.ts
import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'

const API_TOKEN = 'sk-f52e6bfc417c41338e7c0cc21dbeefc9'
const USER_ID = '413564'
const BASE_URL = 'https://dzwlai.com/apiuser/_open/suno'

interface TransformedItem {
  id: string
  inputType: string
  makeInstrumental: boolean
  prompt: string
  title: string
  tags: string
  clipId: string
  duration: number
  progress: number
  waitNum: number
  continueClipId: string
  status: number
  cld2AudioUrl: string
  cld2ImageUrl: string
  progressMsg: string
  createTime?: string
  api_provider_id: string
}

export async function GET(req: Request) {
  try {
    // 从 URL 获取参数
    const { searchParams } = new URL(req.url)
    const pageNum = searchParams.get('pageNum') || '1'
    const pageSize = searchParams.get('pageSize') || '10'
    const userId = searchParams.get('user_id') || USER_ID

    // 获取外部 API 数据
    const apiResponse = await fetch(
      `${BASE_URL}/music/myMusics?pageNum=${pageNum}&pageSize=${pageSize}`,
      {
        method: 'GET',
        headers: {
          'x-token': API_TOKEN,
          'x-userId': userId,
          'Content-Type': 'application/json',
        },
      }
    )

    const apiData = await apiResponse.json()

    // 查询数据库中的已完成任务
    const { data: tasks, error } = await supabase
      .from('song_task_list')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 3)
      .order('id', { ascending: false })

    if (error) {
      console.error('Error fetching tasks:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch tasks' },
        { status: 500 }
      )
    }

    // 转换数据库数据格式
    const transformedData = tasks.flatMap((task) => {
      const items: TransformedItem[] = []

      // 第一个音频
      if (task.audioUrl_1 && task.imageUrl_1) {
        items.push({
          id: task.audio1_id,
          inputType: '20',
          makeInstrumental: false,
          prompt: task.prompt || '',
          title: task.title || '',
          tags: task.tags || '',
          clipId: task.audio1_id,
          duration: 0,
          api_provider_id: task.api_provider_id,
          progress: 100,
          waitNum: 0,
          continueClipId: '',
          status: 30,
          cld2AudioUrl: task.audioUrl_1,
          cld2ImageUrl: task.imageUrl_1,
          progressMsg: 'Production completed',
          createTime: task?.created_at,
        })
      }

      // 第二个音频
      if (task.audioUrl_2 && task.imageUrl_2) {
        items.push({
          id: task.audio2_id,
          inputType: '20',
          makeInstrumental: false,
          prompt: task.prompt || '',
          title: task.title || '',
          tags: task.tags || '',
          clipId: task.audio2_id,
          duration: 0,
          api_provider_id: task.api_provider_id,
          progress: 100,
          waitNum: 0,
          continueClipId: '',
          status: 30,
          cld2AudioUrl: task.audioUrl_2,
          cld2ImageUrl: task.imageUrl_2,
          progressMsg: 'Production completed',
          createTime: task?.created_at,
        })
      }

      return items
    })

    console.log(apiData, 'apiData')
    console.log(transformedData, 'transformedData')

    // 合并外部 API 数据和数据库数据
    apiData.data = [...transformedData, ...(apiData.data || [])]
    const combinedData = {
      ...apiData,
      // data: [...(apiData.data || []), ...transformedData],
    }

    return NextResponse.json(combinedData)
  } catch (error) {
    console.error('Error in myMusics route:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
