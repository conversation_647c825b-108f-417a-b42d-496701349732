// app/api/indexnow/route.ts
import { NextRequest, NextResponse } from 'next/server'
import {
  submitToIndexNow,
  submitBatchToIndexNow,
} from '../../lib/indexnow/lib/indexnow'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { url, urls } = body

    if (url) {
      // 单个 URL 提交
      const result = await submitToIndexNow(url)
      return NextResponse.json({ success: true, result })
    } else if (urls && Array.isArray(urls)) {
      console.log('faith=============urls', urls)
      // 批量 URL 提交
      const result = await submitBatchToIndexNow(urls)
      return NextResponse.json({ success: true, result })
    } else {
      return NextResponse.json(
        { error: 'Missing url or urls parameter' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('IndexNow error:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}

// 可选：添加其他 HTTP 方法的处理
export async function GET() {
  return NextResponse.json(
    { error: 'This endpoint only accepts POST requests' },
    { status: 405 }
  )
}
