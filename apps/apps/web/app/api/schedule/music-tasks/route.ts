import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'

// 获取音乐任务列表 - 只获取 status = 0 的最新5条记录
export async function GET() {
  try {
    // 从Supabase获取任务列表，限制5条，按创建时间降序排序
    const { data: tasks, error } = await supabase
      .from('song_task_list')
      .select('*')
      .eq('status', 0)
      .order('created_at', { ascending: false })
      .limit(5)

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      tasks: tasks,
      count: tasks?.length || 0,
    })
  } catch (error) {
    console.error('Error fetching music tasks:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve music tasks' },
      { status: 500 }
    )
  }
}
