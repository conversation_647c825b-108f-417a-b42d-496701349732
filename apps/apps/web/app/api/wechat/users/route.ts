import { NextResponse } from 'next/server'

const WECHAT_API_BASE = 'https://api.weixin.qq.com'

// 获取access_token
async function getAccessToken() {
  try {
    const response = await fetch(`${WECHAT_API_BASE}/cgi-bin/stable_token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        grant_type: 'client_credential',
        appid: 'wx4bc26a99d6409922',
        secret: 'e2904109806e618d0e4fa0a963da010c',
        force_refresh: true, // 设置为true强制刷新token
      }),
    })

    const data = await response.json()

    // 检查微信返回的错误码
    if (data.errcode) {
      throw new Error(
        `WeChat API error: ${data.errmsg} (code: ${data.errcode})`
      )
    }

    return data.access_token
  } catch (error) {
    console.error('Error getting access token:', error)
    throw error
  }
}

export async function GET() {
  try {
    // 获取access_token
    const accessToken = await getAccessToken()

    // 获取用户列表
    const response = await fetch(
      `${WECHAT_API_BASE}/cgi-bin/user/get?access_token=${accessToken}`
    )

    const data = await response.json()

    // 检查微信返回的错误码
    if (data.errcode) {
      throw new Error(
        `WeChat API error: ${data.errmsg} (code: ${data.errcode})`
      )
    }

    // 打印返回数据以便调试
    console.log('Users data:', data)

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in GET handler:', error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Failed to get users',
        details: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    )
  }
}
