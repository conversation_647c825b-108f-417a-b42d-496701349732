// apps/web/app/api/wechat/auth/route.ts
import { NextResponse } from 'next/server'

const WECHAT_API_BASE = 'https://api.weixin.qq.com'

// 获取access_token
async function getAccessToken() {
  const appId = process.env.WECHAT_APP_ID
  const appSecret = process.env.WECHAT_APP_SECRET

  const response = await fetch(
    `${WECHAT_API_BASE}/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`
  )

  const data = await response.json()
  if (!response.ok) {
    throw new Error(`Failed to get access token: ${data.errmsg}`)
  }

  return data.access_token
}

// 获取用户OpenID列表
export async function GET() {
  try {
    const accessToken = await getAccessToken()

    const response = await fetch(
      `${WECHAT_API_BASE}/cgi-bin/user/get?access_token=${accessToken}`
    )

    const data = await response.json()
    if (!response.ok) {
      throw new Error(`Failed to get users: ${data.errmsg}`)
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error getting users:', error)
    return NextResponse.json({ error: 'Failed to get users' }, { status: 500 })
  }
}
