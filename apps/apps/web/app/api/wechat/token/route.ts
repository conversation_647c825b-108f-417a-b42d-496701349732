// apps/web/app/api/wechat/token/route.ts
import { NextResponse } from 'next/server'

const WECHAT_API_BASE = 'https://api.weixin.qq.com'

// 获取稳定版access_token
async function getStableAccessToken() {
  const appId = process.env.WECHAT_APP_ID
  const appSecret = process.env.WECHAT_APP_SECRET

  try {
    const response = await fetch(`${WECHAT_API_BASE}/cgi-bin/stable_token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        grant_type: 'client_credential',
        appid: appId,
        secret: appSecret,
        force_refresh: false, // 普通模式
      }),
    })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(`Failed to get access token: ${JSON.stringify(data)}`)
    }

    return data.access_token
  } catch (error) {
    console.error('Error getting access token:', error)
    throw error
  }
}

// 消息发送API
export async function POST(request: Request) {
  try {
    const { openid, content } = await request.json()

    if (!openid || !content) {
      return NextResponse.json(
        { error: 'OpenID and content are required' },
        { status: 400 }
      )
    }

    // 获取新的access_token
    const accessToken = await getStableAccessToken()

    const messageData = {
      touser: openid,
      msgtype: 'text',
      text: {
        content: content,
      },
    }

    const response = await fetch(
      `${WECHAT_API_BASE}/cgi-bin/message/custom/send?access_token=${accessToken}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(messageData),
      }
    )

    const data = await response.json()

    if (!response.ok) {
      throw new Error(`Failed to send message: ${JSON.stringify(data)}`)
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error sending message:', error)
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : 'Failed to send message',
      },
      { status: 500 }
    )
  }
}
