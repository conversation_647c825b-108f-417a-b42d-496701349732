import { NextResponse } from 'next/server'

const WECHAT_API_BASE = 'https://api.weixin.qq.com'

// 全局变量存储 token 信息
let tokenCache = {
  accessToken: null,
  expiresAt: 0,
}

async function getAccessToken() {
  const now = Date.now()

  // 如果 token 存在且未过期，直接返回缓存的 token
  if (tokenCache.accessToken && now < tokenCache.expiresAt) {
    return tokenCache.accessToken
  }

  // 正确的 GET 请求，参数放在 URL 中
  const url = `${WECHAT_API_BASE}/cgi-bin/token?grant_type=client_credential&appid=wx4bc26a99d6409922&secret=e2904109806e618d0e4fa0a963da010c`

  const response = await fetch(url, {
    method: 'GET',
    // GET 请求不需要 body 和 Content-Type
  })

  const data = await response.json()

  if (data.errcode) {
    throw new Error(`WeChat API error: ${data.errmsg} (code: ${data.errcode})`)
  }

  // 缓存 token，设置过期时间（提前5分钟过期，确保安全）
  tokenCache.accessToken = data.access_token
  tokenCache.expiresAt = now + (data.expires_in - 300) * 1000

  return tokenCache.accessToken
}

function getBeijingTime() {
  const options = {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false, // 使用24小时制
  }

  return new Date().toLocaleString('zh-CN', options)
}

export async function POST(request: Request) {
  try {
    // 解析请求数据，增加了完成时间和产品名称参数
    const {
      openid = 'ooAOG6WFy1y3J-3WsjWMs1ZtlxYU',
      completionTime,
      productName,
      content = '', // 保留原有的content参数用于兼容性
    } = await request.json()

    if (!openid) {
      return NextResponse.json(
        { errcode: 40001, errmsg: 'OpenID is required' },
        { status: 400 }
      )
    }

    // 检查是否提供了模板消息所需参数
    if (!productName) {
      return NextResponse.json(
        {
          errcode: 40002,
          errmsg:
            'Completion time and product name are required for template message',
        },
        { status: 400 }
      )
    }

    // 获取access_token
    const accessToken = await getAccessToken()

    // 构造模板消息数据
    // 注意：模板ID需要替换为您在微信公众平台上配置的实际模板ID
    const templateId = 'tkw1yc3cQ5iv4geHCeWW-pFXlqqlqwR5HS05ucW7_U0' // 从截图中获取的模板ID

    const templateData = {
      touser: openid,
      template_id: templateId,
      // 可选：点击模板消息后跳转的链接
      url: 'https://www.aimakesong.com/',
      // 模板数据
      data: {
        time5: {
          // 正确格式，添加了.DATA后缀
          value: getBeijingTime(),
          color: '#173177',
        },
        thing2: {
          // 正确格式，添加了.DATA后缀
          value: productName,
          color: '#173177',
        },
      },
    }

    // 发送模板消息
    const response = await fetch(
      `${WECHAT_API_BASE}/cgi-bin/message/template/send?access_token=${accessToken}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateData),
      }
    )

    const data = await response.json()

    // 检查微信返回的错误码
    if (data.errcode && data.errcode !== 0) {
      throw new Error(
        `WeChat API error: ${data.errmsg} (code: ${data.errcode})`
      )
    }

    // 打印成功响应以便调试
    console.log('Template message sent successfully:', data)

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in POST handler:', error)
    return NextResponse.json(
      {
        errcode: 500,
        errmsg:
          error instanceof Error
            ? error.message
            : 'Failed to send template message',
        details: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    )
  }
}
