// apps/web/app/api/user/info/route.ts
import { CookieOptions, setCookie } from '@/utils/lib/cookie'
import { supabase } from '../../../lib/supabaseClient'

// 使用示例
// 方式一：基础使用
// async function fetchUserInfo(email: string) {
//   try {
//     const response = await fetch(`/api/user/info?email=${encodeURIComponent(email)}`)
//     const data = await response.json()

//     if (!response.ok) {
//       throw new Error(data.message || 'Failed to fetch user info')
//     }

//     return data.data
//   } catch (error) {
//     console.error('Error fetching user info:', error)
//     throw error
//   }
// }

// async function postUserInfo(email: string) {
//   const response = await fetch('/api/user/info', {
//     method: 'POST',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify({ email })
//   })

//   const data = await response.json()

//   if (!response.ok) {
//     throw new Error(data.message || 'Failed to fetch user info')
//   }

//   return data.data
// }

interface UserData {
  created_at: string
  username: string
  avatar_url: string | null
  email: string
  last_login_time: string
  points: number
  membership_status: string
  membership_level: string
  membership_start_date: string | null
  membership_end_date: string | null
  updated_at: string
}

async function getUserByEmail(email: string) {
  const { data: users, error } = await supabase
    .from('song_user')
    .select(
      `
      id,
      created_at,
      username,
      avatar_url,
      email,
      last_login_time,
      points,
      membership_status,
      membership_level,
      membership_start_date,
      membership_end_date,
      updated_at,
      country
    `
    )
    .eq('email', email)
    .limit(1)

  console.log('faith=============useruser-user-8888-users', users)

  const user = users && users.length > 0 ? users[0] : null

  if (error) throw error
  if (!user) throw new Error('User not found')

  const now = new Date()
  const endDate = user.membership_end_date
    ? new Date(user.membership_end_date)
    : null
  const isMembershipExpired = endDate ? now > endDate : true

  return {
    ...user,
    is_membership_active:
      !isMembershipExpired && user.membership_status === 'active',
    membership_days_left: endDate
      ? Math.max(
          0,
          Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
        )
      : 0,
  }
}

async function updateUserCountry(userId: string, country: string) {
  const { error } = await supabase
    .from('song_user')
    .update({ country: country, updated_at: new Date().toISOString() })
    .eq('id', userId)

  if (error) {
    console.error('Error updating user country:', error)
    throw error
  }

  console.log(`Updated country to ${country} for user ID: ${userId}`)
}

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const email = searchParams.get('email')

    if (!email) {
      return Response.json(
        { error: 'Missing email parameter' },
        { status: 400 }
      )
    }

    const country = req.headers.get('x-vercel-ip-country') || 'unknown'

    const userData = await getUserByEmail(email)

    // 如果有用户数据且用户没有country字段或country为空，则更新country
    if (userData && (!userData.country || userData.country === '')) {
      await updateUserCountry(userData.id, country)
      userData.country = country
    }

    const headers = new Headers()

    const cookieOptions = {
      // 只在 HTTPS 连接中传输 cookie
      secure: process.env.NODE_ENV === 'production', // 生产环境为 true，开发环境为 false

      // Cookie 的 SameSite 属性，用于防止 CSRF 攻击
      sameSite: 'lax' as const, // 允许在用户点击链接时发送 cookie

      // Cookie 的作用路径
      path: '/', // 在整个网站都可以访问这个 cookie

      // Cookie 的过期时间（以秒为单位）
      // maxAge: 60 * 60 * 24 * 7 // 7天
      maxAge: 60 * 60 * 24 * 1, // 1天
      // 60 秒 * 60 分钟 * 24 小时 * 7 天 = 604800 秒
    }

    // 使用封装的方法设置 cookie
    setCookie(headers, 'userId', userData.id, cookieOptions)
    setCookie(headers, 'userCreatedAt', userData.created_at, cookieOptions)
    setCookie(headers, 'username', userData.username, cookieOptions)
    setCookie(headers, 'avatarUrl', userData.avatar_url, cookieOptions)
    setCookie(headers, 'userEmail', userData.email, cookieOptions)
    setCookie(headers, 'lastLoginTime', userData.last_login_time, cookieOptions)
    setCookie(headers, 'points', userData.points, cookieOptions)
    setCookie(
      headers,
      'membershipStatus',
      userData.membership_status,
      cookieOptions
    )
    setCookie(
      headers,
      'membershipLevel',
      userData.membership_level,
      cookieOptions
    )
    setCookie(
      headers,
      'membershipStartDate',
      userData.membership_start_date,
      cookieOptions
    )
    setCookie(
      headers,
      'membershipEndDate',
      userData.membership_end_date,
      cookieOptions
    )
    setCookie(headers, 'updatedAt', userData.updated_at, cookieOptions)

    return Response.json({ success: true, data: userData }, { headers })
  } catch (error) {
    console.error('Error:', error)
    const isUserNotFound =
      error instanceof Error && error.message === 'User not found'
    const status = isUserNotFound ? 404 : 500
    return Response.json(
      {
        error: status === 404 ? 'User not found' : 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status }
    )
  }
}

export async function POST(req: Request) {
  try {
    const { email } = await req.json()

    if (!email) {
      return Response.json({ error: 'Email is required' }, { status: 400 })
    }

    const userData = await getUserByEmail(email)
    return Response.json({ success: true, data: userData })
  } catch (error) {
    console.error('Error:', error)
    const isUserNotFound =
      error instanceof Error && error.message === 'User not found'
    const status = isUserNotFound ? 404 : 500
    return Response.json(
      {
        error: status === 404 ? 'User not found' : 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status }
    )
  }
}
