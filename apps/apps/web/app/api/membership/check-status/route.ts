// apps/web/app/api/membership/check-status/route.ts
import { NextRequest, NextResponse } from "next/server";
import { supabase } from "../../../lib/supabaseClient";

// 定义类型接口
interface SongOrder {
  id: number;
  user_id: number;
  order_number: string;
  paypro_order_id: string | null;
  product_id: string | null;
  amount: number;
  currency: string;
  status: string;
  payment_method: string | null;
  created_at: string;
  updated_at: string;
  membership_level_id: number | null;
}

interface SongUser {
  id: number;
  created_at: string;
  username: string | null;
  avatar_url: string | null;
  email: string | null;
  points: number | null;
  membership_status: string;
  membership_level: string;
  membership_start_date: string | null;
  membership_end_date: string | null;
  updated_at: string;
}

export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const orderId = searchParams.get("orderId");

    if (!orderId) {
      return NextResponse.json(
        { error: "Order ID is required" },
        { status: 400 }
      );
    }

    // 联表查询订单和用户信息
    const { data: order, error: orderError } = await supabase
      .from("song_order")
      .select(
        `
        *,
        song_user:user_id (
          membership_status,
          membership_level,
          points,
          membership_end_date
        )
      `
      )
      .eq("id", orderId)
      .single();

    if (orderError || !order) {
      console.error("Error fetching order:", orderError);
      return NextResponse.json({ error: "Order not found" }, { status: 404 });
    }

    // 确定订单状态
    let status: "pending" | "succeeded" | "failed" = "pending";
    let message = "";

    // 根据订单状态确定结果
    if (
      order.status === "paid" &&
      order.song_user?.membership_status === "active"
    ) {
      status = "succeeded";
    } else if (
      ["failed", "declined", "cancelled", "refunded"].includes(order.status)
    ) {
      status = "failed";
      message = "Payment failed or declined";
    }

    return NextResponse.json({
      status,
      message,
      data: {
        orderStatus: order.status,
        orderAmount: order.amount,
        orderCurrency: order.currency,
        paymentMethod: order.payment_method,
        membershipStatus: order.song_user?.membership_status || "free",
        membershipLevel: order.song_user?.membership_level || "free",
        points: order.song_user?.points || 0,
        membershipEndDate: order.song_user?.membership_end_date,
        orderNumber: order.order_number,
        payproOrderId: order.paypro_order_id,
      },
    });
  } catch (error) {
    console.error("Error checking order status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// 响应数据类型
export interface CheckStatusResponse {
  status: "pending" | "succeeded" | "failed";
  message?: string;
  data?: {
    orderStatus: string;
    orderAmount: number;
    orderCurrency: string;
    paymentMethod: string | null;
    membershipStatus: string;
    membershipLevel: string;
    points: number;
    membershipEndDate?: string | null;
    orderNumber: string;
    payproOrderId: string | null;
  };
  error?: string;
}
