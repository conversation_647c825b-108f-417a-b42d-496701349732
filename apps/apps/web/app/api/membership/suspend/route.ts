// apps/web/app/api/membership/suspend/route.ts
import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { supabase } from '../../../lib/supabaseClient'

export async function POST(req: Request) {
  try {
    const { reasonText } = await req.json()

    // 获取当前用户session
    const cookieStore = await cookies()
    const oauthEmail = cookieStore.get('oauth_email')

    if (!oauthEmail) {
      return new Response('Unauthorized', { status: 401 })
    }

    // 1. 获取订阅信息 - 修改为关联查询
    const { data: subscriptionData, error: subError } = await supabase
      .from('song_user')
      .select(
        `
          *,
          song_order(
            subscription_id,
            created_at
          )
        `
      ) // 移除 !inner
      .eq('email', oauthEmail.value)
      .not('song_order.subscription_id', 'is', null)
      .limit(1)
      .single()

    if (subError || !subscriptionData) {
      return NextResponse.json(
        { success: false, message: 'Subscription not found' },
        { status: 404 }
      )
    }

    // 从关联数据中获取 subscription_id
    const subscriptionId = subscriptionData.song_order[0]?.subscription_id

    if (!subscriptionId) {
      return NextResponse.json(
        { success: false, message: 'No valid subscription ID found' },
        { status: 404 }
      )
    }

    // 2. 验证订阅状态是否为active
    if (subscriptionData.membership_status !== 'active') {
      return NextResponse.json(
        {
          success: false,
          message: 'Only active subscriptions can be suspended',
        },
        { status: 400 }
      )
    }

    // 3. 调用 PayPro Global API 暂停订阅
    const payproResponse = await fetch(
      'https://store.payproglobal.com/api/Subscriptions/Suspend',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: subscriptionId,
          reasonText: reasonText || 'Customer requested suspension',
          vendorAccountId: 169067,
          apiSecretKey: '779196d6-3d8d-44cf-b128-227b6a4eb793',
          suspendImmediately: true,
          suspendNextRenewal: true,
        }),
      }
    )

    const payproData = await payproResponse.json()

    if (!payproData.isSuccess) {
      return NextResponse.json(
        {
          success: false,
          message: 'Failed to suspend subscription',
          errors: payproData.errors,
        },
        { status: 400 }
      )
    }

    // 4. 更新本地数据库中的订阅状态
    const { error: updateError } = await supabase
      .from('song_user')
      .update({
        membership_status: 'suspended',
        updated_at: new Date().toISOString(),
        points: 0,
      })
      .eq('id', subscriptionData.id)

    if (updateError) {
      console.error('Error updating subscription status:', updateError)
      return NextResponse.json({
        success: true,
        message: 'Subscription suspended but local update failed',
      })
    }

    // 5. 记录会员变更日志
    await supabase.from('song_membership_log').insert({
      user_id: subscriptionData.id,
      old_status: 'active',
      new_status: 'suspended',
      old_level: subscriptionData.membership_level,
      new_level: subscriptionData.membership_level,
      change_reason: 'subscription_suspended',
      note: reasonText,
    })

    return NextResponse.json({
      success: true,
      message: 'Subscription suspended successfully',
    })
  } catch (error) {
    console.error('Error suspending subscription:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    )
  }
}
