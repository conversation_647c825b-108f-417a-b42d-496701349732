import { supabase } from '@/lib/supabaseClient'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { email, code } = await request.json()

    if (!email || !code) {
      return NextResponse.json(
        { code: 1, error: 'Email and verification code are required' },
        { status: 400 }
      )
    }

    // 获取对应的验证码记录
    const { data, error } = await supabase
      .from('auth_code')
      .select('*')
      .eq('email', email)
      .eq('code', code)
      .eq('used', 0)
      .single()

    if (error || !data) {
      // 查询无结果或发生错误
      return NextResponse.json(
        { code: 1, error: 'Invalid verification code' },
        { status: 400 }
      )
    }

    // 检查是否过期
    if (new Date(data.expires_at) < new Date()) {
      // 过期的验证码标记为已使用
      await supabase
        .from('auth_code')
        .update({ used: 2 }) // 2表示过期
        .eq('id', data.id)

      return NextResponse.json(
        { code: 2, error: 'Verification code has expired' },
        { status: 400 }
      )
    }

    // 验证成功，将验证码标记为已使用
    await supabase
      .from('auth_code')
      .update({ used: 1, used_at: new Date().toISOString() })
      .eq('id', data.id)

    return NextResponse.json({
      code: 0,
      message: 'Verification successful',
    })
  } catch (error) {
    console.error('Error verifying code:', error)
    return NextResponse.json(
      { code: 1, error: 'Failed to verify code' },
      { status: 500 }
    )
  }
}
