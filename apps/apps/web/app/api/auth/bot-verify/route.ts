interface TurnstileVerificationResponse {
  success: boolean
  error_codes?: string[]
  challenge_ts?: string
  hostname?: string
}

interface RequestBody extends FormData {
  turnstileToken: string
}

export async function POST(request: Request) {
  try {
    const body: RequestBody = await request.json()
    const { turnstileToken } = body

    const verificationResponse = await fetch(
      'https://challenges.cloudflare.com/turnstile/v0/siteverify',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secret: '0x4AAAAAABTwSRWpbx1ujxx1vPHsRkC6qy8',
          response: turnstileToken,
        }),
      }
    )

    const verificationData: TurnstileVerificationResponse =
      await verificationResponse.json()

    if (!verificationData.success) {
      console.error(
        'Turnstile verification failed:',
        verificationData['error_codes']
      )
      return Response.json({ error: '验证码验证失败' }, { status: 400 })
    }

    return Response.json({ message: 'success', code: 0 })
  } catch (error) {
    console.error('API route error:', error)
    return Response.json({ error: '服务器错误' }, { status: 500 })
  }
}
