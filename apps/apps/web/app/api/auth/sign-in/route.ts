import { supabase } from '@/lib/supabaseClient'
import { setCookie } from '@/utils/lib/cookie'
import { NextRequest, NextResponse } from 'next/server'
//
async function updateUserCountry(userId: string, country: string) {
  const { error } = await supabase
    .from('song_user')
    .update({ country: country, updated_at: new Date().toISOString() })
    .eq('id', userId)

  if (error) {
    console.error('Error updating user country:', error)
    throw error
  }

  console.log(`Updated country to ${country} for user ID: ${userId}`)
}

async function getUserByEmail(email: string) {
  console.log('email777777777', email)

  const { data: user, error } = await supabase
    .from('song_user')
    .select(
      `
      id,
      created_at,
      username,
      avatar_url,
      email,
      last_login_time,
      points,
      membership_status,
      membership_level,
      membership_start_date,
      membership_end_date,
      updated_at,
      country
    `
    )
    .eq('email', email)
    .limit(1)
    .maybeSingle()

  console.log(66666666666, user, error)

  if (error) throw error
  if (!user) {
    return null
  }

  console.log(88888888888)

  const now = new Date()
  const endDate = user.membership_end_date
    ? new Date(user.membership_end_date)
    : null
  const isMembershipExpired = endDate ? now > endDate : true
  console.log('user-----', user)

  return {
    ...user,
    is_membership_active:
      !isMembershipExpired && user.membership_status === 'active',
    membership_days_left: endDate
      ? Math.max(
          0,
          Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
        )
      : 0,
  }
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    const ip = request.headers.get('x-real-ip')

    if (!email) {
      return NextResponse.json(
        { code: 1, error: 'Email and verification code are required' },
        { status: 400 }
      )
    }

    // 检查数据库中是否已经存在该电子邮件地址
    let userData = await getUserByEmail(email)
    console.log('🚀 ~ userData:', userData)
    if (!userData) {
      // 插入新用户
      const { error: insertError, data: insertData } = await supabase
        .from('song_user')
        .insert({
          email,
          created_at: new Date().toISOString(),
          username: email,
          avatar_url: '',
          last_login_time: new Date().toISOString(),
          points: 5,
          membership_status: 'free',
          membership_level: 'free',
          membership_start_date: null,
          membership_end_date: null,
          updated_at: new Date().toISOString(),
          register_ip: null,
          country: null,
          register_source: 'email',
        })
        .select()
        .limit(1)
        .maybeSingle()

      console.log(333332222, insertData)

      console.log('insertError', insertError)

      if (insertError) {
        return NextResponse.json(
          { code: 1, error: 'Failed to create user' },
          { status: 500 }
        )
      }

      userData = insertData
    }

    const { data: updatedUser, error: updateError } = await supabase
      .from('song_user')
      .update({
        last_login_time: new Date().toISOString(),
      })
      .eq('email', email)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating user:', updateError)
      throw updateError
    }
    const country = request.headers.get('x-vercel-ip-country') || 'unknown'

    console.log(55555555, userData)

    // 如果有用户数据且用户没有country字段或country为空，则更新country
    if (userData && (!userData.country || userData.country === '')) {
      console.log(333333333333)

      await updateUserCountry(userData.id, country)
      userData.country = country
    }
    const headers = new Headers()
    const cookieOptions = {
      // 只在 HTTPS 连接中传输 cookie
      secure: process.env.NODE_ENV === 'production', // 生产环境为 true，开发环境为 false

      // Cookie 的 SameSite 属性，用于防止 CSRF 攻击
      sameSite: 'lax' as const, // 允许在用户点击链接时发送 cookie

      // Cookie 的作用路径
      path: '/', // 在整个网站都可以访问这个 cookie

      // Cookie 的过期时间（以秒为单位）
      // maxAge: 60 * 60 * 24 * 7 // 7天
      maxAge: 60 * 60 * 24 * 1, // 1天
      // 60 秒 * 60 分钟 * 24 小时 * 7 天 = 604800 秒
    }

    // 使用封装的方法设置 cookie
    setCookie(headers, 'oauth_id', userData.id, cookieOptions)
    setCookie(headers, 'userCreatedAt', userData.created_at, cookieOptions)
    setCookie(headers, 'username', userData.username, cookieOptions)
    setCookie(headers, 'oauth_avatar', userData.avatar_url, cookieOptions)
    setCookie(headers, 'oauth_email', userData.email, cookieOptions)
    setCookie(headers, 'lastLoginTime', userData.last_login_time, cookieOptions)
    setCookie(headers, 'points', userData.points, cookieOptions)
    setCookie(
      headers,
      'membershipStatus',
      userData.membership_status,
      cookieOptions
    )
    setCookie(
      headers,
      'membershipLevel',
      userData.membership_level,
      cookieOptions
    )
    setCookie(
      headers,
      'membershipStartDate',
      userData.membership_start_date,
      cookieOptions
    )
    setCookie(
      headers,
      'membershipEndDate',
      userData.membership_end_date,
      cookieOptions
    )
    setCookie(headers, 'updatedAt', userData.updated_at, cookieOptions)
    return NextResponse.json(
      { code: 0, message: 'User created successfully' },
      { status: 200, headers }
    )
  } catch (error) {
    return NextResponse.json(
      { code: 1, error: 'Failed login' },
      { status: 500 }
    )
  }
}
