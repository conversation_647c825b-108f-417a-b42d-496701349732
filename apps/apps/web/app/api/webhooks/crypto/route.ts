// apps/web/app/api/payment/crypto/route.ts

import { supabase } from '@/lib/supabaseClient'
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  try {
    const { internalOrderId, txHash, email } = await req.json()

    // 验证必要参数
    if (!internalOrderId || !txHash || !email) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    // 1. 通过 email 查询用户信息
    const { data: user, error: userError } = await supabase
      .from('song_user')
      .select('id, membership_status, membership_level')
      .eq('email', email)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: `User not found with email ${email}` },
        { status: 400 }
      )
    }

    // 2. 创建订单记录
    const { data: order, error: orderError } = await supabase
      .from('song_order')
      .insert({
        user_id: user.id,
        status: 'paid',
        order_number: txHash,
        payment_method: 'wallet',
        subscription_id: '',
        amount: -1,
        currency: 'usdt',
        paypro_order_id: txHash,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (orderError || !order) {
      return NextResponse.json(
        { error: `Order creation failed: ${orderError?.message}` },
        { status: 500 }
      )
    }

    // 3. 获取会员等级信息
    const { data: membershipLevel, error: membershipError } = await supabase
      .from('song_membership_level')
      .select('*')
      .eq('id', internalOrderId)
      .single()

    if (membershipError || !membershipLevel) {
      return NextResponse.json(
        { error: `Membership level not found: ${membershipError?.message}` },
        { status: 400 }
      )
    }

    // 5. 更新用户会员状态
    const membershipEndDate = new Date()
    membershipEndDate.setDate(
      membershipEndDate.getDate() + membershipLevel.duration_days
    )

    const { error: userUpdateError } = await supabase
      .from('song_user')
      .update({
        membership_status: 'active',
        membership_level: membershipLevel.name,
        membership_start_date: new Date().toISOString(),
        membership_end_date: membershipEndDate.toISOString(),
        updated_at: new Date().toISOString(),
        points: membershipLevel.points,
      })
      .eq('id', user.id)

    if (userUpdateError) {
      return NextResponse.json(
        { error: `User update failed: ${userUpdateError.message}` },
        { status: 500 }
      )
    }

    // 6. 记录会员变更
    const { error: membershipLogError } = await supabase
      .from('song_membership_log')
      .insert({
        user_id: user.id,
        order_id: order.id,
        old_status: user.membership_status || 'free',
        new_status: 'active',
        old_level: user.membership_level || 'free',
        new_level: membershipLevel.name,
        start_date: new Date().toISOString(),
        end_date: membershipEndDate.toISOString(),
        change_reason: 'crypto_purchase',
      })

    if (membershipLogError) {
      return NextResponse.json(
        {
          error: `Membership log creation failed: ${membershipLogError.message}`,
        },
        { status: 500 }
      )
    }

    // 返回成功响应
    return NextResponse.json({
      orderId: order.id,
      txHash: txHash,
      success: true,
    })
  } catch (error: any) {
    console.error('Error processing payment:', error)
    return NextResponse.json(
      { error: 'Failed to process payment' },
      { status: 500 }
    )
  }
}
