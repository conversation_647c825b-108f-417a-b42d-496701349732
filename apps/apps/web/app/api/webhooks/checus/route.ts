import { supabase } from '@/lib/supabaseClient'
import { NextResponse } from 'next/server'

// 
export async function POST(req: Request) {
  try {
    // 1. 解析请求数据
    const { data } = await req.json()
    console.log('Checus webhook POST request data:', data)

    // 2. 基本验证
    if (!data.outTradeNo || !data.status) {
      console.error('Missing required parameters')
    }

    // 3. 检查状态 - 只处理成功的支付
    if (data.status !== 'SUCCESS') {
      // 如果状态是失败或取消，可以更新订单状态
      if (data.status === 'FAILED' || data.status === 'CLOSED') {
        try {
          const { data: existingOrder } = await supabase
            .from('song_order')
            .select('id')
            .eq('order_number', data.outTradeNo)
            .single()

          if (existingOrder) {
            await supabase
              .from('song_order')
              .update({
                status: data.status.toLowerCase(),
                updated_at: new Date().toISOString(),
              })
              .eq('id', existingOrder.id)
          }
        } catch (error) {
          console.error('Error updating failed order:', error)
        }
      }
      console.error(`Payment status: ${data.status}`)
      return NextResponse.json({
        msg: 'Success',
        code: 'SUCCESS',
      })
    }

    // 4. 查找订单记录
    const { data: existingOrder, error: orderQueryError } = await supabase
      .from('song_order')
      .select('id, user_id, product_id, status')
      .eq('order_number', data.outTradeNo)
      .single()

    if (orderQueryError || !existingOrder) {
      console.error('Order not found:', data.outTradeNo)
      return NextResponse.json({
        msg: 'Success',
        code: 'SUCCESS',
      })
    }

    // 如果订单已经处理，避免重复处理
    if (existingOrder?.status === 'paid') {
      console.log('Order already processed:', data.outTradeNo)
      return NextResponse.json({
        msg: 'Success',
        code: 'SUCCESS',
      })
    }

    // 5. 获取用户信息
    const { data: user, error: userError } = await supabase
      .from('song_user')
      .select('id, membership_status, membership_level')
      .eq('id', existingOrder?.user_id)
      .single()

    if (userError || !user) {
      console.error('User not found:', existingOrder?.user_id)
      return NextResponse.json({
        msg: 'Success',
        code: 'SUCCESS',
      })
    }

    // 6. 更新订单状态
    try {
      await supabase
        .from('song_order')
        .update({
          status: 'paid',
          payment_method:
            data.paymentDetails?.[0]?.paymentMethodType || 'checus',
          subscription_id: data.tradeToken || '',
          amount: data.totalAmount || 0,
          currency: data.currency || 'USD',
          paypro_order_id: data.thirdChannelNo || '',
          updated_at: new Date().toISOString(),
        })
        .eq('id', existingOrder?.id)
    } catch (error) {
      console.error('Error updating order:', error)
      return NextResponse.json({
        msg: 'Success',
        code: 'SUCCESS',
      })
    }

    // 7. 获取会员等级信息
    let membershipLevel = null
    try {
      // 首先尝试从product_id获取会员等级
      let { data: level, error: membershipError } = await supabase
        .from('song_membership_level')
        .select('*')
        .eq('id', existingOrder?.product_id)
        .single()

      // 如果找不到，尝试通过金额匹配
      if (membershipError || !level) {
        const { data: membershipByPrice, error: membershipByPriceError } =
          await supabase
            .from('song_membership_level')
            .select('*')
            .eq('price', data.totalAmount)
            .eq('currency', data.currency)
            .single()

        if (membershipByPriceError || !membershipByPrice) {
          // 如果还是找不到，使用默认的基础会员（ID为1）
          const { data: basicMembership } = await supabase
            .from('song_membership_level')
            .select('*')
            .eq('id', 1)
            .single()

          if (!basicMembership) {
            console.error('Could not determine membership level')
          }

          membershipLevel = basicMembership
        } else {
          membershipLevel = membershipByPrice
        }
      } else {
        membershipLevel = level
      }
    } catch (error) {
      console.error('Error getting membership level:', error)
      return NextResponse.json({
        msg: 'Success',
        code: 'SUCCESS',
      })
    }

    // 8. 更新用户会员状态
    try {
      const membershipEndDate = new Date()
      membershipEndDate.setDate(
        membershipEndDate.getDate() + membershipLevel.duration_days
      )

      // 先查询用户现有的积分，累加
      const { data: userPoints } = await supabase
        .from('song_user')
        .select('points')
        .eq('id', user?.id)
        .single()

      console.log('userPoints -->', userPoints)
      const newPoints =
        parseInt(userPoints?.points || 0) +
        parseInt(membershipLevel.points || 0)

      // 更新用户积分
      await supabase
        .from('song_user')
        .update({
          membership_status: 'active',
          membership_level: membershipLevel.name,
          membership_start_date: new Date().toISOString(),
          membership_end_date: membershipEndDate.toISOString(),
          updated_at: new Date().toISOString(),
          points: newPoints,
        })
        .eq('id', user?.id)
    } catch (error) {
      console.error('Error updating user membership:', error)
      return NextResponse.json({
        msg: 'Success',
        code: 'SUCCESS',
      })
    }

    // 9. 记录会员变更
    try {
      await supabase.from('song_membership_log').insert({
        user_id: user?.id,
        order_id: existingOrder?.id,
        old_status: user?.membership_status || 'free',
        new_status: 'active',
        old_level: user?.membership_level || 'free',
        new_level: membershipLevel.name,
        start_date: new Date().toISOString(),
        end_date: new Date(
          Date.now() + membershipLevel.duration_days * 24 * 60 * 60 * 1000
        ).toISOString(),
        change_reason: 'checus_purchase',
        transaction_id: data.tradeToken || '',
      })
    } catch (error) {
      console.error('Error logging membership change:', error)
      return NextResponse.json({
        msg: 'Success',
        code: 'SUCCESS',
      })
    }
  } catch (error: any) {
    return NextResponse.json({
      msg: 'Success',
      code: 'SUCCESS',
    })
  } finally {
    return NextResponse.json({
      msg: 'Success',
      code: 'SUCCESS',
    })
  }
}
