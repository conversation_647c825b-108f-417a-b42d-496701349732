import { useWeb3React } from '@web3-react/core'
import { InjectedConnector } from '@web3-react/injected-connector'
import { useEffect, useState, useCallback } from 'react'

// 支持的链配置
export const NETWORK_CONFIGS = {
  1: {
    name: 'Ethereum Mainnet',
    usdt: '******************************************',
    explorer: 'https://etherscan.io',
    decimals: 6,
    symbol: 'ETH',
    rpcUrls: ['https://ethereum.publicnode.com'],
    blockExplorerUrls: ['https://etherscan.io'],
  },
  56: {
    name: 'BSC',
    usdt: '******************************************',
    explorer: 'https://bscscan.com',
    decimals: 18,
    symbol: 'BNB',
    rpcUrls: ['https://bsc-dataseed1.binance.org'],
    blockExplorerUrls: ['https://bscscan.com'],
  },
  137: {
    name: 'Polygon',
    usdt: '******************************************',
    explorer: 'https://polygonscan.com',
    decimals: 6,
    symbol: 'MATIC',
    rpcUrls: ['https://polygon-rpc.com'],
    blockExplorerUrls: ['https://polygonscan.com'],
  },
} as const

export const injected = new InjectedConnector({
  supportedChainIds: Object.keys(NETWORK_CONFIGS).map(Number),
})

export function useWeb3() {
  const { active, account, library, connector, activate, deactivate, chainId } =
    useWeb3React()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const switchNetwork = useCallback(async (targetChainId: number) => {
    if (!window.ethereum) {
      throw new Error('MetaMask is not installed')
    }

    const currentChainId = await window.ethereum.request({
      method: 'eth_chainId',
    })
    if (parseInt(currentChainId, 16) === targetChainId) {
      return // 已经在目标网络上
    }

    try {
      setLoading(true)
      setError('')

      // 先尝试切换网络
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${targetChainId.toString(16)}` }],
      })

      // 等待网络切换完成
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Network switch timeout'))
        }, 10000) // 10秒超时

        const handleNetworkChange = () => {
          clearTimeout(timeout)
          resolve(true)
        }

        window.ethereum.once('chainChanged', handleNetworkChange)
      })
    } catch (switchError: any) {
      if (switchError.code === 4902) {
        try {
          const config =
            NETWORK_CONFIGS[targetChainId as keyof typeof NETWORK_CONFIGS]
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [
              {
                chainId: `0x${targetChainId.toString(16)}`,
                chainName: config.name,
                nativeCurrency: {
                  name: config.symbol,
                  symbol: config.symbol,
                  decimals: 18,
                },
                rpcUrls: config.rpcUrls,
                blockExplorerUrls: config.blockExplorerUrls,
              },
            ],
          })
        } catch (addError) {
          setError('Failed to add network')
          throw addError
        }
      } else {
        setError('Failed to switch network')
        throw switchError
      }
    } finally {
      setLoading(false)
    }
  }, [])

  const connect = useCallback(async () => {
    try {
      setLoading(true)
      setError('')
      await activate(injected, undefined, true)
    } catch (error) {
      setError('Failed to connect')
      console.error('Failed to connect to wallet:', error)
    } finally {
      setLoading(false)
    }
  }, [activate])

  useEffect(() => {
    if (window.ethereum) {
      const handleChainChanged = (chainId: string) => {
        console.log('Network changed to:', parseInt(chainId, 16))
        activate(injected)
      }

      const handleAccountsChanged = (accounts: string[]) => {
        if (accounts.length === 0) {
          deactivate()
        } else {
          activate(injected)
        }
      }

      window.ethereum.on('chainChanged', handleChainChanged)
      window.ethereum.on('accountsChanged', handleAccountsChanged)

      return () => {
        window.ethereum.removeListener('chainChanged', handleChainChanged)
        window.ethereum.removeListener('accountsChanged', handleAccountsChanged)
      }
    }
  }, [activate, deactivate])

  useEffect(() => {
    injected.isAuthorized().then((isAuthorized) => {
      if (isAuthorized) {
        activate(injected)
      }
    })
  }, [activate])

  return {
    active,
    account,
    library,
    connector,
    connect,
    disconnect: deactivate,
    switchNetwork,
    loading,
    error,
    chainId,
  }
}
