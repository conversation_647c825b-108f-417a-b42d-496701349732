// lib/indexnow.js
/**
 * 提交单个 URL 到 IndexNow
 * @param {string} url - 要提交的网页 URL
 * @returns {Promise<any>}
 */
export async function submitToIndexNow(url) {
  const API_KEY = 'bca9c964c32a4e83423bd77f94da8922' // 您的 API 密钥
  const DOMAIN = new URL(url).hostname

  const endpoint = `https://www.bing.com/indexnow?url=${encodeURIComponent(
    url
  )}&key=${API_KEY}&keyLocation=${encodeURIComponent(
    `https://${DOMAIN}/${API_KEY}.txt`
  )}`

  const response = await fetch(endpoint)
  return response.text()
}

/**
 * 批量提交多个 URL 到 IndexNow
 * @param {string[]} urls - 要提交的网页 URL 数组
 * @returns {Promise<any>}
 */
export async function submitBatchToIndexNow(urls) {
  if (!urls || urls.length === 0) return null

  const API_KEY = 'bca9c964c32a4e83423bd77f94da8922' // 您的 API 密钥
  const DOMAIN = new URL(urls[0]).hostname

  const response = await fetch('https://www.bing.com/indexnow', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      host: DOMAIN,
      key: API_KEY,
      keyLocation: `https://${DOMAIN}/${API_KEY}.txt`,
      urlList: urls,
    }),
  })

  // 检查 content-length 是否为0
  const contentLength = response.headers.get('content-length')
  if (contentLength === '0') {
    // 返回成功但响应体为空的情况
    return {
      success: true,
      status: response.status,
      message:
        'URL submission successful. Empty response received from IndexNow API.',
    }
  }

  // 如果响应体不为空，尝试解析JSON
  try {
    const data = await response.json()
    return {
      success: true,
      status: response.status,
      data,
    }
  } catch (error) {
    // 如果解析JSON失败，则获取文本响应
    const text = await response.text()
    return {
      success: response.ok,
      status: response.status,
      response: text || '(empty response)',
    }
  }

  return response.json()
}
