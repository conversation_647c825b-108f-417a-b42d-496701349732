// app/lib/user.ts 或 app/utils/user.ts
import { cookies } from 'next/headers'

export async function fetchUserInfo() {
  try {
    const cookieStore = cookies()
    // @ts-ignore
    const email = cookieStore.get('oauth_email')?.value

    if (!email) {
      console.log('No oauth_email cookie found')
      return null
    }

    try {
      const response = await fetch(`https://www.aimakesong.com/api/user/info?email=${encodeURIComponent(email)}`, {
        cache: 'no-store',
        headers: {
          Accept: 'application/json'
        },
        signal: AbortSignal.timeout(5000)
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || `HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.data
    } catch (fetchError) {
      // @ts-ignore
      if (fetchError.name === 'AbortError') {
        console.error('Request timeout while fetching user info')
        return null
      }
      throw fetchError
    }
  } catch (error) {
    console.error('Error in fetchUserInfo:', error)
    return null
  }
}

// 可以添加类型定义
export interface UserInfo {
  id: string
  email: string
  name: string
  // ... 其他用户信息字段
}

// 也可以添加其他相关的用户函数
export async function isUserLoggedIn() {
  const cookieStore = cookies()
  // @ts-ignore
  return !!cookieStore.get('oauth_email')?.value
}
