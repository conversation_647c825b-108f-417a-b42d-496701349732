// apps/web/lib/wechat.ts
const WECHAT_API_BASE = 'https://api.weixin.qq.com'

export async function getAccessToken(forceRefresh = true) {
  // 默认使用强制刷新
  try {
    const response = await fetch(`${WECHAT_API_BASE}/cgi-bin/stable_token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        grant_type: 'client_credential',
        appid: process.env.WECHAT_APP_ID,
        secret: process.env.WECHAT_APP_SECRET,
        force_refresh: forceRefresh, // 设置为true强制刷新token
      }),
    })

    const data = await response.json()

    if (!response.ok || data.errcode) {
      console.error('Token response:', data)
      throw new Error(`Failed to get access token: ${JSON.stringify(data)}`)
    }

    return data.access_token
  } catch (error) {
    console.error('Error getting access token:', error)
    throw error
  }
}
