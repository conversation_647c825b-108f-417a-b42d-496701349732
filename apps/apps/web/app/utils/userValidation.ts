// utils/userValidation.ts
interface ValidationResult {
  success: boolean
  message?: string
  userInfo?: any
}

export const validateUserForGeneration = async (user: any, pointsRequired: number = 100): Promise<ValidationResult> => {
  if (!user) {
    return { success: false, message: 'Please login first' }
  }

  // {
  //   "success": true,
  //   "data": {
  //       "created_at": "2025-01-12T15:00:06.361347+00:00",
  //       "username": "super man",
  //       "avatar_url": "https://lh3.googleusercontent.com/a/ACg8ocJfGSHFxNnkTmltcwD0cOQxk_uqh8sSQQHuEU2_GwJFFs4rlhM=s96-c",
  //       "email": "<EMAIL>",
  //       "last_login_time": "2025-01-13T07:47:10.935Z",
  //       "points": 4,
  //       "membership_status": "free",
  //       "membership_level": "free",
  //       "membership_start_date": null,
  //       "membership_end_date": null,
  //       "updated_at": "2025-01-12T15:00:06.361347+00:00",
  //       "is_membership_active": false,
  //       "membership_days_left": 0
  //   }
  // }

  try {
    // Get latest user info
    const userInfoResponse = await fetch(`/api/user/info?email=${encodeURIComponent(user.email)}`)
    const userInfoData = await userInfoResponse.json()

    if (!userInfoData.success) {
      return {
        success: false,
        message: 'Failed to get user information'
      }
    }

    const userData = userInfoData.data

    // Check if points are sufficient
    if (userData.points < pointsRequired) {
      return {
        success: false,
        message: `Insufficient points. Required: ${pointsRequired}, Current points: ${userData.points}`,
        userInfo: userData
      }
    }

    // All validations passed
    return {
      success: true,
      userInfo: userData
    }
  } catch (error) {
    console.error('User validation error:', error)
    return {
      success: false,
      message: 'Verification failed, please try again later'
    }
  }
}
