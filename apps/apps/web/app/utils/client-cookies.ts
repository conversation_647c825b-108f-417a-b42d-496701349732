// utils/client-cookies.ts
import Cookies from 'js-cookie'

export function getUserFromClientCookies() {
  const avatar = Cookies.get('oauth_avatar')
  const email = Cookies.get('oauth_email')
  const id = Cookies.get('oauth_id')
  const membershipStatus = Cookies.get('membershipStatus')

  //
  if (!email || !id) {
    return null
  }

  return {
    avatar,
    email,
    id,
    membershipStatus,
    points: 0,
    balance: 0,
  }
}
