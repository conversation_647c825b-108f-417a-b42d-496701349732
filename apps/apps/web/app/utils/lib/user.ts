// apps/web/app/utils/lib/user.ts
import Cookies from 'js-cookie'
import { useRouter } from 'next/navigation'

export const clearUserData = () => {
  // Clear all cookies
  const cookies = document.cookie.split(';')
  cookies.forEach((cookie) => {
    const cookieName = cookie.split('=')[0].trim()
    Cookies.remove(cookieName)
  })

  // Clear localStorage
  localStorage.clear()
}

export const handleSignOut = () => {
  try {
    // Clear all user data
    clearUserData()

    // Redirect to home page
    window.location.href = '/'
  } catch (error) {
    console.error('Error during sign out:', error)
  }
}
