// types/cookie.ts
export interface CookieOptions {
  path?: string
  httpOnly?: boolean
  sameSite?: 'strict' | 'lax' | 'none'
  maxAge?: number
  secure?: boolean
}

function serializeCookieOptions(options: {
  httpOnly?: boolean
  secure?: boolean
  sameSite?: 'strict' | 'lax' | 'none'
  path?: string
  maxAge?: number
}) {
  const parts = []

  if (options.httpOnly) parts.push('HttpOnly')
  if (options.secure) parts.push('Secure')
  if (options.sameSite) parts.push(`SameSite=${options.sameSite}`)
  if (options.path) parts.push(`Path=${options.path}`)
  if (options.maxAge) parts.push(`Max-Age=${options.maxAge}`)

  return parts.join('; ')
}

export function setCookie(headers: Headers, name: string, value: string | number | undefined, options: CookieOptions) {
  if (value !== undefined) {
    const encodedValue = encodeURIComponent(value)
    headers.append('Set-<PERSON>ie', `${name}=${encodedValue}; ${serializeCookieOptions(options)}`)
  }
}
