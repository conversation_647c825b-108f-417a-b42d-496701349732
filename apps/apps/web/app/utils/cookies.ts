// utils/cookies.ts
import { cookies } from 'next/headers'

export async function getUserFromCookies() {
  const cookieStore = await cookies()
  const avatar = cookieStore.get('oauth_avatar')?.value
  const email = cookieStore.get('oauth_email')?.value
  const id = cookieStore.get('oauth_id')?.value

  if (!email || !id) {
    return null
  }

  return {
    avatar,
    email,
    id,
    points: 0, // 从API获取
    balance: 0, // 从API获取
  }
}
