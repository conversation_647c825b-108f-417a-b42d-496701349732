// utils/pointsService.ts
interface ConsumePointsResult {
  success: boolean
  message?: string
  points?: number
}

export const consumeUserPoints = async (user: any, consumePoints: number): Promise<ConsumePointsResult> => {
  if (!user?.email || !user?.id || !consumePoints) {
    return {
      success: false,
      message: 'Invalid user information or points amount'
    }
  }

  try {
    const response = await fetch('/api/membership/points/consume', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: user.email,
        userId: user.id,
        consumePoints
      })
    })

    const data = await response.json()

    if (!response.ok) {
      return {
        success: false,
        message: data.message || 'Failed to consume points'
      }
    }

    return {
      success: true,
      points: data.points,
      message: data.message
    }
  } catch (error) {
    console.error('Consume points error:', error)
    return {
      success: false,
      message: 'Failed to consume points, please try again later'
    }
  }
}
