import type { NextRequest, NextResponse } from 'next/server'

interface GeoLocationOptions {
  cookieMaxAge?: number // 以秒为单位
  cookiePath?: string
  cookieSameSite?: 'strict' | 'lax' | 'none'
}

const EU_COUNTRIES = [
  'AT',
  'BE',
  'BG',
  'HR',
  'CY',
  'CZ',
  'DK',
  'EE',
  'FI',
  'FR',
  'DE',
  'GR',
  'HU',
  'IE',
  'IT',
  'LV',
  'LT',
  'LU',
  'MT',
  'NL',
  'PL',
  'PT',
  'RO',
  'SK',
  'SI',
  'ES',
  'SE',
] as const

export function handleGeoLocation(
  req: NextRequest,
  response: NextResponse,
  options: GeoLocationOptions = {}
) {
  const {
    cookieMaxAge = 60 * 60 * 24 * 7, // 默认7天
    cookiePath = '/',
    cookieSameSite = 'strict',
  } = options

  console.log('进来判断欧盟了没？？')

  const country = req.headers.get('x-vercel-ip-country') || ''
  const isEU = EU_COUNTRIES.includes(country as (typeof EU_COUNTRIES)[number])

  const cookieOptions = {
    path: cookiePath,
    httpOnly: false,
    sameSite: cookieSameSite,
    maxAge: cookieMaxAge,
  }

  console.log('准备设置 cookie，options:', cookieOptions)

  // 设置 cookie 并检查结果
  response.cookies.set('userCountry', country, cookieOptions)

  // 检查 cookie 是否设置成功
  const cookies = response.headers.get('Set-Cookie')
  console.log('设置后的 cookies:', cookies)

  response.cookies.set('isEU', String(isEU), cookieOptions)

  return {
    country,
    isEU,
    response,
  }
}
