// Type declarations for next-intl
declare module 'next-intl' {
  import { ReactNode } from 'react'

  export function useTranslations(
    namespace?: string
  ): (key: string, values?: Record<string, any>) => string
  export function useLocale(): string
  export function getTranslations(): Promise<
    (key: string, values?: Record<string, any>) => string
  >

  export interface MessageDescriptor {
    id: string
    defaultMessage?: string
    description?: string
  }

  export interface IntlConfig {
    locale: string
    messages: Record<string, any>
    timeZone?: string
    formats?: Record<string, any>
    defaultTranslationValues?: Record<string, any>
    now?: Date
    onError?: (error: Error) => void
  }

  export function NextIntlClientProvider(props: {
    locale: string
    messages: Record<string, any>
    children: ReactNode
  }): JSX.Element
}
