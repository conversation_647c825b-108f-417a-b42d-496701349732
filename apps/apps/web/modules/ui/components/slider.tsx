import * as React from 'react'
import { cn } from '@ui/lib'

export interface SliderProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  min?: number
  max?: number
  step?: number
  value: number
  onValueChange: (value: number) => void
}

export const Slider: React.FC<SliderProps> = ({
  min = 0,
  max = 100,
  step = 1,
  value,
  onValueChange,
  className,
  ...props
}) => {
  return (
    <input
      type="range"
      min={min}
      max={max}
      step={step}
      value={value}
      onChange={(e) => onValueChange(Number(e.target.value))}
      className={cn(
        'w-full h-2 rounded-lg bg-muted/40 accent-primary focus:outline-none focus:ring-2 focus:ring-primary transition-all',
        className
      )}
      {...props}
    />
  )
}
Slider.displayName = 'Slider'
