import * as React from 'react'
import { cn } from '@ui/lib'

export interface CheckboxProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: React.ReactNode
}

export const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, label, ...props }, ref) => {
    return (
      <label className="inline-flex items-center gap-2 cursor-pointer select-none">
        <input
          type="checkbox"
          ref={ref}
          className={cn(
            'h-4 w-4 rounded border border-input bg-background checked:bg-primary checked:border-primary focus:ring-2 focus:ring-primary transition-all',
            className
          )}
          {...props}
        />
        {label && <span className="text-sm text-foreground">{label}</span>}
      </label>
    )
  }
)
Checkbox.displayName = 'Checkbox'
