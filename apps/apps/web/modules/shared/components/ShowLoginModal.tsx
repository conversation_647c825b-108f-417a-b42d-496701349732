import { useTranslations } from 'next-intl'
import { Wand2, X } from 'lucide-react'
import { Link, usePathname } from '@i18n/routing'

interface Props {
  title: string
  desc: string
  needBottomArea?: boolean
  needBottomPay?: boolean
  onClose: () => void
}
export default function ShowLoginModal({
  title,
  desc,
  needBottomArea = false,
  needBottomPay = true,
  onClose,
}: Props) {
  const t = useTranslations()
  const pathname = usePathname()

  const handleJumpLogin = () => {
    localStorage.setItem('REDIRECT_PATH', pathname)
  }

  return (
    <div
      className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center"
      onClick={onClose}
    >
      <div
        className="bg-gray-800/90 border border-gray-700/50 rounded-2xl p-8 max-w-md w-11/12 mx-4 relative transform transition-all overflow-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        >
          <X className="h-5 w-5" />
        </button>

        {/* 图标 */}
        <div className="h-20 w-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mx-auto flex items-center justify-center mb-6">
          <Wand2 className="h-10 w-10 text-white" />
        </div>

        {/* 内容 */}
        <div className="text-center space-y-6">
          <h3 className="text-2xl font-bold">
            <span className="text-purple-500">{title}</span>
          </h3>

          <p className="text-lg font-medium text-purple-300 animate-pulse">
            {desc}
          </p>

          <div className="space-y-2">
            <a
              href="/api/oauth/google"
              onClick={handleJumpLogin}
              className="block"
            >
              <button className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl font-medium hover:opacity-90 transition-all">
                {t('freeTrail.loginNow')}
              </button>
            </a>
          </div>

          {needBottomArea && (
            <div className="space-y-3 pt-4">
              <h4 className="text-sm font-medium text-purple-400">
                {t('freeTrail.memberBenefits')}
              </h4>
              <ul className="text-sm text-gray-400 space-y-2">
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-purple-500"></div>
                  {t('freeTrail.unlimitedGeneration')}
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-purple-500"></div>
                  {t('freeTrail.highQualityVoice')}
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-purple-500"></div>
                  {t('freeTrail.batchExport')}
                </li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
