'use client'

import {
  CheckCircle,
  XCircle,
  LucideIcon,
  Loader2,
  AlertCircle,
  X,
} from 'lucide-react'
import Cookies from 'js-cookie'
import { useRouter } from '@shared/hooks/router'
import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import CryptoPaymentModal from '@/[locale]/(marketing)/(home)/components/CryptoPaymentModal'
import Image from 'next/image'
import { detectMobileDevice } from '@/utils/lib'

export interface PlanFeatures {
  level: number
  title: string
  IconComponent: LucideIcon
  price: string
  originalPrice?: string
  credits: string
  saveText?: string
  features: string[]
  popularBadge?: string
  disabledFeatures?: string[]
  isPopular?: boolean
  buttonText?: string
  buttonVariant?: 'dark' | 'light'
  disabled?: boolean
  className?: string
  isYearly?: boolean
  discountPercent?: number
}

interface PricingCardProps extends PlanFeatures {
  // Additional props specific to PricingCard can be added here
}

interface PaymentMethodModalProps {
  isOpen: boolean
  onClose: () => void
  onSelectPayment: (
    method: 'paypro' | 'other' | 'checus',
    params: { level: number; isYearly: boolean; paymentChannel: string }
  ) => void
  title: string
  isYearly: boolean
  level: number
}

const PaymentMethodModal: React.FC<PaymentMethodModalProps> = ({
  isOpen,
  onClose,
  onSelectPayment,
  title,
  isYearly,
  level,
}) => {
  if (!isOpen) return null
  const t = useTranslations()

  const handlePaymentClick = (
    method: 'paypro' | 'other' | 'checus',
    channel: string
  ) => {
    onSelectPayment(method, { level, isYearly, paymentChannel: channel })
  }

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-[#18161E] border border-purple-500/30 rounded-xl p-7 max-w-md w-full mx-4 shadow-xl shadow-purple-500/10">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-purple-500">
            {t('selectPaymentMethod')}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-pink-400 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* PayPro Option */}
        <div className="border space-y-4 border-purple-500/30 rounded-xl p-5 mb-5">
          <div className="flex justify-between items-center">
            <div className="text-xl font-medium text-white">PayPro</div>
            <div className="bg-purple-900/30 text-purple-400 px-3 py-1 rounded-full text-sm">
              {t('recommended')}
            </div>
          </div>
          <div className="flex items-center gap-3 flex-wrap">
            <div
              onClick={() => handlePaymentClick('paypro', 'PayPal')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/paypal.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="PayPal"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                PayPal
              </div>
            </div>
            <div
              onClick={() => handlePaymentClick('paypro', 'Visa')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/visa.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="Visa"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                Visa
              </div>
            </div>
            <div
              onClick={() => handlePaymentClick('paypro', 'MasterCard')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/mastercard.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="MasterCard"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                MasterCard
              </div>
            </div>
            <div
              onClick={() => handlePaymentClick('paypro', 'AliPay')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/alipay.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="AliPay"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                AliPay
              </div>
            </div>
            <div
              onClick={() => handlePaymentClick('paypro', 'WebMoney')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/webmoney.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="WebMoney"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                WebMoney
              </div>
            </div>
            <div
              onClick={() => handlePaymentClick('paypro', 'WeChat')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/wechat.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="WeChat"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                WeChat
              </div>
            </div>
          </div>
        </div>

        {/* Other Option */}
        <div className="border border-gray-700 space-y-4 rounded-xl p-5">
          <div className="text-xl font-medium text-white">{t('other')}</div>
          <div className="flex items-center gap-3 flex-wrap">
            <div
              onClick={() => handlePaymentClick('other', 'card_ru')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/mir.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="Мир"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                Мир
              </div>
            </div>
            <div
              onClick={() => handlePaymentClick('other', 'tbank_ru')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/tbank.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="Tbank"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                Tbank
              </div>
            </div>
            <div
              onClick={() => handlePaymentClick('other', 'yoomoney_ru')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/yoomoney.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="Yoomoney"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                Yoomoney
              </div>
            </div>
            <div
              onClick={() => handlePaymentClick('other', 'sberpay_ru')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/sberbank.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="Sberbank"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                Sberbank
              </div>
            </div>
          </div>
        </div>
        {/* Checus Option */}
        <div className="border border-gray-700 space-y-4 rounded-xl p-5 mt-5">
          <div className="text-xl font-medium text-white">Checus</div>
          <div className="flex items-center gap-3 flex-wrap">
            <div
              onClick={() => handlePaymentClick('checus', 'checus')}
              className="relative w-12 h-8 group cursor-pointer transition-transform hover:scale-110"
            >
              <Image
                src="/images/checus.svg"
                fill
                className="object-contain"
                sizes="100%"
                priority
                alt="Checus"
              />
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                Checus
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const PricingCard: React.FC<PricingCardProps> = ({
  isYearly,
  level,
  title,
  IconComponent,
  price,
  originalPrice,
  discountPercent,
  credits,
  saveText,
  features,
  popularBadge,
  disabledFeatures = [],
  isPopular = false,
  buttonText = 'Subscribe Now',
  buttonVariant = 'dark',
  disabled = false,
  className,
}) => {
  const t = useTranslations()
  const [isLoading, setIsLoading] = useState(false)
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [paymentId, setPaymentId] = useState<string | null>(null)
  const [showTimeoutModal, setShowTimeoutModal] = useState(false)
  const [showCryptoModal, setShowCryptoModal] = useState(false)
  const [showPaymentMethodModal, setShowPaymentMethodModal] = useState(false)
  const [paymentData, setPaymentData] = useState(null)

  useEffect(() => {
    let intervalId: NodeJS.Timeout
    let timeoutId: NodeJS.Timeout
    const startTime = Date.now()
    const TIMEOUT_DURATION = 5 * 60 * 1000 // 5 minutes

    const checkSubscriptionStatus = async () => {
      if (!paymentId) return

      try {
        const response = await fetch(
          `/api/membership/check-status?orderId=${paymentId}`
        )
        const data = (await response.json()) as any

        if (data.status === 'succeeded' && data.data) {
          clearInterval(intervalId)
          clearTimeout(timeoutId)
          setIsLoading(false)
          setPaymentId(null)
          setShowSuccessModal(true)
        } else if (data.status === 'failed') {
          clearInterval(intervalId)
          clearTimeout(timeoutId)
          setIsLoading(false)
          setPaymentId(null)
          alert(data.message || 'Payment failed')
        } else if (Date.now() - startTime >= TIMEOUT_DURATION) {
          clearInterval(intervalId)
          clearTimeout(timeoutId)
          setIsLoading(false)
          setShowTimeoutModal(true)
        }
      } catch (error) {
        console.error('Error checking subscription status:', error)
      }
    }

    if (paymentId && isLoading) {
      intervalId = setInterval(checkSubscriptionStatus, 5000)
      checkSubscriptionStatus()

      // Set 5-minute timeout
      timeoutId = setTimeout(() => {
        clearInterval(intervalId)
        setIsLoading(false)
        setShowTimeoutModal(true)
      }, TIMEOUT_DURATION)
    }

    return () => {
      clearInterval(intervalId)
      clearTimeout(timeoutId)
    }
  }, [paymentId, isLoading])

  const openPayment = (url: string) => {
    // Check if iOS Safari
    const isIOSSafari =
      /iP(ad|hone|od)/.test(navigator.userAgent) &&
      /WebKit/.test(navigator.userAgent) &&
      !/(CriOS|FxiOS|OPiOS|mercury)/.test(navigator.userAgent)

    // For iOS Safari, show prompt and redirect
    if (isIOSSafari) {
      const confirmed = window.confirm(t('redirectSubscription'))
      if (confirmed) {
        window.location.href = url
      }
      return
    } else {
      window.open(url, '_blank')
    }
  }

  const router = useRouter()

  const handleClick = async () => {
    console.log('faith=============wiidnow', window.location.href)

    const oauth_email = Cookies.get('oauth_email')
    console.log('oauth_email :>> ', oauth_email)

    if (!oauth_email) {
      router.push('/auth/login')
      return
    }

    console.log('level :>> ', level)

    if (price === 'Free') return

    // Show payment method modal instead of directly processing
    setShowPaymentMethodModal(true)
  }

  const handlePaymentSelection = async (
    method: 'paypro' | 'other' | 'checus',
    {
      level,
      isYearly,
      paymentChannel,
    }: { level: number; isYearly: boolean; paymentChannel: string }
  ) => {
    setShowPaymentMethodModal(false)
    try {
      setIsLoading(true)

      const response = await fetch('/api/membership/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          membershipLevelId: level,
          isYearly,
          paymentMethod: method,
          paymentChannel,
          isMobile: detectMobileDevice().isMobile,
        }),
      })

      if (!response.ok) {
        throw new Error('Subscription failed')
      }

      const data = await response.json()
      setPaymentId(data.orderId)
      console.log(data, 'data')

      if (method === 'paypro') {
        // PayPro支付方式直接打开链接
        openPayment(data.checkoutUrl)
      } else if (method === 'other') {
        // Payssion支付方式需要创建并提交表单
        const form = document.createElement('form')
        form.method = 'POST'
        form.name = 'payssion_payment'
        form.action = 'https://www.payssion.com/payment/create.html'

        Object.entries(data).forEach(([key, value]) => {
          if (key !== 'orderId' && key !== 'checkoutUrl') {
            const input = document.createElement('input')
            input.type = 'hidden'
            input.name = key
            input.value = String(value)
            form.appendChild(input)
          }
        })
        const input = document.createElement('input')
        input.type = 'hidden'
        input.name = 'return_url'
        input.value = `${location.origin}/auth/login`
        form.appendChild(input)
        document.body.appendChild(form)
        form.submit()
        document.body.removeChild(form)
      } else if (method === 'checus') {
        const checusResponse = await fetch(
          'https://p-gate-uat.checus.com/aggregate-pay/api/gateway/orderAndPay',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              sign: data.sign,
            },
            body: JSON.stringify(data.checusData),
          }
        )

        const checusResult = await checusResponse.json()

        if (checusResult.code === 'APPLY_SUCCESS') {
          openPayment(checusResult.data.redirectUrl)
        } else {
          throw new Error(checusResult.msg || 'Checus payment failed')
        }
      }
    } catch (error) {
      console.error('Payment error:', error)
      setIsLoading(false)
    }
  }

  return (
    <>
      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#1a1d24] border border-gray-800 rounded-xl p-6 flex flex-col items-center">
            <Loader2 className="w-8 h-8 text-purple-400 animate-spin" />
            <p className="mt-4 text-gray-300">{t('processingSubscription')}</p>
          </div>
        </div>
      )}

      {/* Subscription Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#1a1d24] border border-gray-800 rounded-xl p-8 max-w-md w-full mx-4">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {t('subscriptionSuccessful')}
              </h3>
              <p className="text-gray-400 text-center mb-6">
                {t('thankYouForSubscribing')} {title}.{' '}
                {t('accessToPremiumFeatures')}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 w-full">
                <button
                  onClick={() => {
                    setShowSuccessModal(false)
                    router.push('/auth/login')
                  }}
                  className="flex-1 py-3 px-4 rounded-lg font-medium bg-gray-800 text-white hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
                >
                  {t('common.menu.profile')}
                </button>
                <button
                  onClick={() => {
                    setShowSuccessModal(false)
                    router.push('/ai-music-generator')
                  }}
                  className="flex-1 py-3 px-4 rounded-lg font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-opacity flex items-center justify-center gap-2"
                >
                  {t('createMusic')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Timeout Modal */}
      {showTimeoutModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#1a1d24] border border-gray-800 rounded-xl p-8 max-w-md w-full mx-4">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mb-4">
                <AlertCircle className="w-8 h-8 text-yellow-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {t('paymentProcessing')}
              </h3>
              <p className="text-gray-400 text-center mb-6">
                {t('paymentProcessingDesc')}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 w-full">
                <button
                  onClick={() => {
                    setShowTimeoutModal(false)
                    setPaymentId(null)
                    router.push('/auth/login')
                  }}
                  className="flex-1 py-3 px-4 rounded-lg font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 transition-opacity flex items-center justify-center gap-2"
                >
                  {t('goToProfile')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Method Selection Modal */}
      <PaymentMethodModal
        isOpen={showPaymentMethodModal}
        onClose={() => setShowPaymentMethodModal(false)}
        onSelectPayment={handlePaymentSelection}
        title={title}
        level={level}
        isYearly={!!isYearly}
      />

      {/* Crypto Payment Modal */}
      <CryptoPaymentModal
        isOpen={showCryptoModal}
        onClose={() => setShowCryptoModal(false)}
        title={title}
        email={Cookies.get('oauth_email')}
        IconComponent={IconComponent}
        price={price}
      />

      <div
        className={`bg-[#121218] ${className} border ${
          isPopular
            ? 'scale-105 shadow-2xl shadow-purple-500/20 bg-gradient-to-b from-purple-900/20 to-pink-500/20 border-pink-500/40'
            : 'border-gray-800'
        } rounded-3xl p-6 flex flex-col h-full relative  hover:bg-[#18181f] transition-all duration-300 ${
          disabled
            ? 'opacity-50 cursor-not-allowed pointer-events-none z-10'
            : ''
        } ${isPopular ? 'z-20' : 'z-10'}`}
      >
        {saveText && (
          <span className="absolute top-4 left-4 text-green-400 text-sm font-medium">
            {saveText}
          </span>
        )}
        {isPopular && (
          <span className="absolute top-3 right-5 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">
            {popularBadge}
          </span>
        )}

        <div className={`text-center mb-6 ${isPopular ? 'relative' : ''}`}>
          {isPopular && (
            <div className="absolute -inset-6 -top-8 bg-gradient-to-b from-purple-500/30 to-pink-500/20 blur-xl opacity-40 -z-10" />
          )}
          <div className="flex items-center justify-center gap-2 mb-2 mt-6">
            <IconComponent
              className={`w-6 h-6 ${
                isPopular ? 'text-pink-400' : 'text-purple-400'
              }`}
            />
            <h3
              className={`text-xl font-semibold ${
                isPopular ? 'text-pink-400' : 'text-purple-400'
              }`}
            >
              {title}
            </h3>
          </div>
          <p className="text-gray-300 mb-4 font-medium">{credits}</p>

          {/* Price display with original price and discount */}
          <div className="flex flex-col items-center justify-center mb-2">
            {originalPrice && (
              <div className="flex items-center justify-center gap-2 mb-1">
                <span className="text-gray-500 line-through text-sm">
                  {originalPrice}
                </span>
                {discountPercent && (
                  <span className="text-xs px-2 py-0.5 bg-green-500/20 text-green-400 rounded-full">
                    -{discountPercent}%
                  </span>
                )}
              </div>
            )}
            <h4
              className={`text-2xl font-bold ${
                isPopular ? 'text-purple-500' : 'text-white'
              }`}
            >
              {price}
            </h4>
          </div>

          {!disabled && (
            <button
              onClick={handleClick}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-300
  ${
    isPopular
      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:opacity-90 shadow-lg shadow-pink-500/30'
      : buttonVariant === 'dark'
      ? 'bg-gray-800 text-white hover:bg-gray-700'
      : 'bg-purple-600 text-white hover:bg-purple-700'
  }`}
            >
              {buttonText}
            </button>
          )}
        </div>

        <div className="space-y-4">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-3">
              <CheckCircle
                className={`w-5 h-5 ${
                  isPopular ? 'text-pink-400' : 'text-purple-400'
                } flex-shrink-0`}
              />
              <span className={`${isPopular ? 'text-white' : 'text-gray-300'}`}>
                {feature}
              </span>
            </div>
          ))}

          {disabledFeatures?.map((feature, index) => (
            <div key={index} className="flex items-center gap-3">
              <XCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
              <span className="text-gray-500">{feature}</span>
            </div>
          ))}
        </div>
      </div>
    </>
  )
}

export default PricingCard
