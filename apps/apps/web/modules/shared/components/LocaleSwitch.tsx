'use client'

import { config } from '@config'
import { usePathname, useRouter } from '@i18n/routing'
import { Button } from '@ui/components/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@ui/components/dropdown-menu'
import { useLocale } from 'next-intl'
import { useSearchParams } from 'next/navigation'

const { locales } = config.i18n

export function LocaleSwitch() {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const currentLocale = useLocale()

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="gap-2 hitespace-nowrap min-w-[40px] md:min-w-36 px-2 md:px-3"
        >
          {locales[currentLocale as keyof typeof locales].flag === '' ? (
            <svg
              className="w-4 h-4"
              viewBox="0 0 513 343"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_301_161)">
                <path d="M0 0.963867H513V342.957H0V0.963867Z" fill="#D80027" />
                <path d="M0 0.963867H256.5V171.96H0V0.963867Z" fill="#0052B4" />
                <path
                  d="M186.799 86.4627L162.876 97.7156L175.615 120.886L149.637 115.915L146.345 142.158L128.25 122.858L110.155 142.158L106.864 115.915L80.886 120.885L93.6248 97.7146L69.7012 86.4627L93.6248 75.2097L80.886 52.0396L106.864 57.0093L110.156 30.7661L128.25 50.0667L146.346 30.7661L149.637 57.0093L175.616 52.0396L162.876 75.2097L186.799 86.4627Z"
                  fill="white"
                />
                <path
                  d="M128.25 115.53C144.301 115.53 157.313 102.518 157.313 86.4674C157.313 70.4166 144.301 57.4048 128.25 57.4048C112.199 57.4048 99.1875 70.4166 99.1875 86.4674C99.1875 102.518 112.199 115.53 128.25 115.53Z"
                  fill="#0052B4"
                />
                <path
                  d="M128.25 105.891C117.537 105.891 108.821 97.1744 108.821 86.4626C108.821 75.7497 117.538 67.0337 128.25 67.0337C138.963 67.0337 147.679 75.7507 147.679 86.4626C147.679 97.1744 138.963 105.891 128.25 105.891Z"
                  fill="white"
                />
              </g>
              <defs>
                <clipPath id="clip0_301_161">
                  <rect
                    width="513"
                    height="342"
                    fill="white"
                    transform="translate(0 0.959961)"
                  />
                </clipPath>
              </defs>
            </svg>
          ) : (
            <span>{locales[currentLocale as keyof typeof locales].flag}</span>
          )}
          <span className="hidden md:inline">
            {locales[currentLocale as keyof typeof locales].label}
          </span>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        <DropdownMenuRadioGroup
          value={currentLocale}
          onValueChange={(value) => {
            router.push(
              {
                pathname,
                query: Object.fromEntries(searchParams.entries()),
              },
              { locale: value }
            )
            router.refresh()
          }}
        >
          {Object.entries(locales).map(([locale, { flag, label }]) => {
            let f = flag
            if (label === '繁體中文-台灣') {
              f = (
                <svg
                  className="w-4 h-4 mr-2"
                  viewBox="0 0 513 343"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clipPath="url(#clip0_301_161)">
                    <path
                      d="M0 0.963867H513V342.957H0V0.963867Z"
                      fill="#D80027"
                    />
                    <path
                      d="M0 0.963867H256.5V171.96H0V0.963867Z"
                      fill="#0052B4"
                    />
                    <path
                      d="M186.799 86.4627L162.876 97.7156L175.615 120.886L149.637 115.915L146.345 142.158L128.25 122.858L110.155 142.158L106.864 115.915L80.886 120.885L93.6248 97.7146L69.7012 86.4627L93.6248 75.2097L80.886 52.0396L106.864 57.0093L110.156 30.7661L128.25 50.0667L146.346 30.7661L149.637 57.0093L175.616 52.0396L162.876 75.2097L186.799 86.4627Z"
                      fill="white"
                    />
                    <path
                      d="M128.25 115.53C144.301 115.53 157.313 102.518 157.313 86.4674C157.313 70.4166 144.301 57.4048 128.25 57.4048C112.199 57.4048 99.1875 70.4166 99.1875 86.4674C99.1875 102.518 112.199 115.53 128.25 115.53Z"
                      fill="#0052B4"
                    />
                    <path
                      d="M128.25 105.891C117.537 105.891 108.821 97.1744 108.821 86.4626C108.821 75.7497 117.538 67.0337 128.25 67.0337C138.963 67.0337 147.679 75.7507 147.679 86.4626C147.679 97.1744 138.963 105.891 128.25 105.891Z"
                      fill="white"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_301_161">
                      <rect
                        width="513"
                        height="342"
                        fill="white"
                        transform="translate(0 0.959961)"
                      />
                    </clipPath>
                  </defs>
                </svg>
              ) as any
            }
            return (
              <DropdownMenuRadioItem
                key={locale}
                value={locale}
                className="cursor-pointer"
              >
                {f} {label}
              </DropdownMenuRadioItem>
            )
          })}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
