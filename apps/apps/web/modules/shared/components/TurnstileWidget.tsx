'use client'
import { useLocale } from 'next-intl'
import Script from 'next/script'
import { useCallback, useEffect, useRef } from 'react'

declare global {
  interface Window {
    turnstile?: {
      render: (
        element: HTMLElement,
        options: {
          sitekey: string
          callback: (token: string) => void
          theme: string
          language: string
        }
      ) => void
      remove: (element: HTMLElement) => void
    }
  }
}

interface TurnstileWidgetProps {
  onVerify: (token: string) => void
  //颜色
  theme?: 'light' | 'dark'
  //语言
  language?: string
}

const TurnstileWidget: React.FC<TurnstileWidgetProps> = ({
  onVerify,
  theme = 'light',
}) => {
  const divRef = useRef<HTMLDivElement>(null)
  const language = useLocale()

  const renderWidget = useCallback(() => {
    if (divRef.current && window.turnstile) {
      window.turnstile.render(divRef.current, {
        sitekey: process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY as string,
        callback: onVerify,
        theme,
        language,
      })
    }
  }, [theme, language, onVerify])

  useEffect(() => {
    if (window.turnstile) {
      renderWidget()
    }
    const currentRef = divRef.current
    return () => {
      if (currentRef) {
        window.turnstile?.remove(currentRef)
      }
    }
  }, [renderWidget, theme, language, onVerify])

  return (
    <>
      <Script
        src="https://challenges.cloudflare.com/turnstile/v0/api.js"
        onLoad={renderWidget}
      />
      <div ref={divRef} />
    </>
  )
}

export default TurnstileWidget
