import { config } from '@config'
import { createNavigation } from 'next-intl/navigation'
import { defineRouting } from 'next-intl/routing'

export const routing = defineRouting({
  locales: Object.keys(config.i18n.locales),
  defaultLocale: config.i18n.defaultLocale,
  localePrefix: 'as-needed',
  localeCookie: {
    name: config.i18n.localeCookieName,
  },
  localeDetection: false,
})

// 1

export const { Link, redirect, usePathname, useRouter } =
  createNavigation(routing)
