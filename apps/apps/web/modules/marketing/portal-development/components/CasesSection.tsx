export function CasesSection() {
  const cases = [
    {
      title: "ABC Global Portal",
      description: "12 天完成英西双语站，3 个月访客增长 178%。",
      metrics: {
        duration: "12 天",
        growth: "178%",
        languages: "英西双语"
      },
      tags: ["多语言", "全球化", "快速增长"]
    },
    {
      title: "DEF Industry Hub",
      description: "上线首月收录 1,200+ 关键词，获客成本下降 35%。",
      metrics: {
        keywords: "1,200+",
        costReduction: "35%",
        timeframe: "首月"
      },
      tags: ["SEO优化", "行业门户", "降本增效"]
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-16">
            成功案例
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {cases.map((case_, index) => (
              <div key={index} className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-8 hover:shadow-lg transition-shadow">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  {case_.title}
                </h3>
                
                <p className="text-gray-700 mb-6 leading-relaxed">
                  {case_.description}
                </p>
                
                <div className="grid grid-cols-3 gap-4 mb-6">
                  {Object.entries(case_.metrics).map(([key, value]) => (
                    <div key={key} className="text-center">
                      <div className="text-2xl font-bold text-blue-600 mb-1">
                        {value}
                      </div>
                      <div className="text-sm text-gray-600 capitalize">
                        {key === 'duration' && '周期'}
                        {key === 'growth' && '增长'}
                        {key === 'languages' && '语言'}
                        {key === 'keywords' && '关键词'}
                        {key === 'costReduction' && '成本降低'}
                        {key === 'timeframe' && '时间'}
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {case_.tags.map((tag, tagIndex) => (
                    <span key={tagIndex} className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center">
            <p className="text-gray-600 mb-4">
              更多案例请邮件索取《案例手册 PDF》
            </p>
            <a
              href="mailto:<EMAIL>?subject=案例手册索取&body=您好，我想了解更多成功案例，请发送案例手册PDF。"
              className="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors"
            >
              获取案例手册
              <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}