export function PricingSection() {
  const packages = [
    {
      name: "Basic",
      scenario: "SMB 官网",
      features: ["5 栏目", "手机自适应", "后台管理"],
      duration: "7–10 天",
      price: "$500",
      bgColor: "bg-gray-50",
      textColor: "text-gray-600",
      buttonColor: "bg-gray-600 hover:bg-gray-700"
    },
    {
      name: "Pro",
      scenario: "行业门户",
      features: ["文章系统", "会员/登录", "SEO 优化"],
      duration: "10–15 天",
      price: "$1,500",
      bgColor: "bg-blue-50",
      textColor: "text-blue-600",
      buttonColor: "bg-blue-600 hover:bg-blue-700",
      popular: true
    },
    {
      name: "Custom",
      scenario: "深度定制",
      features: ["专属 UI", "高级集成", "API 对接"],
      duration: "15–20 天",
      price: "$3,000",
      bgColor: "bg-purple-50",
      textColor: "text-purple-600",
      buttonColor: "bg-purple-600 hover:bg-purple-700"
    }
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-4">
            透明套餐价
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            {packages.map((pkg, index) => (
              <div key={index} className={`${pkg.bgColor} rounded-xl p-8 relative ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      推荐
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className={`text-2xl font-bold ${pkg.textColor} mb-2`}>
                    {pkg.name}
                  </h3>
                  <p className="text-gray-600 mb-4">{pkg.scenario}</p>
                  <div className={`text-4xl font-bold ${pkg.textColor} mb-2`}>
                    {pkg.price}
                  </div>
                  <p className="text-gray-500">{pkg.duration}</p>
                </div>
                
                <ul className="space-y-3 mb-8">
                  {pkg.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <a
                  href="mailto:<EMAIL>?subject=Portal Dev Inquiry&body=我对您的 {pkg.name} 套餐感兴趣，请发送详细报价。"
                  className={`block w-full text-center py-3 px-6 ${pkg.buttonColor} text-white font-semibold rounded-lg transition-colors`}
                >
                  选择套餐
                </a>
              </div>
            ))}
          </div>
          
          <div className="text-center text-gray-600">
            <p className="text-sm">
              以上报价含部署及源码交付，服务器与域名费自理。
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}