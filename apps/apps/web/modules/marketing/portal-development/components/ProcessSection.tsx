export function ProcessSection() {
  const steps = [
    {
      step: "1",
      title: "邮件沟通需求",
      description: "≤ 4 h 响应",
      icon: "📧",
      color: "bg-blue-100 text-blue-600"
    },
    {
      step: "2", 
      title: "确认原型 & 锁定报价",
      description: "需求调研，正式报价",
      icon: "📋",
      color: "bg-green-100 text-green-600"
    },
    {
      step: "3",
      title: "支付 50% 定金，启动开发",
      description: "签署合同，项目启动",
      icon: "💳",
      color: "bg-yellow-100 text-yellow-600"
    },
    {
      step: "4",
      title: "阶段验收 & 结清尾款",
      description: "分阶段交付，客户验收",
      icon: "✅",
      color: "bg-purple-100 text-purple-600"
    },
    {
      step: "5",
      title: "正式上线 + 30 天免费运维",
      description: "部署上线，售后保障",
      icon: "🚀",
      color: "bg-red-100 text-red-600"
    }
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-16">
            合作流程
          </h2>
          
          <div className="space-y-8">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center gap-8">
                {/* Step Number */}
                <div className={`flex-shrink-0 w-16 h-16 ${step.color} rounded-full flex items-center justify-center font-bold text-xl`}>
                  {step.step}
                </div>
                
                {/* Icon */}
                <div className="text-4xl">
                  {step.icon}
                </div>
                
                {/* Content */}
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {step.title}
                  </h3>
                  <p className="text-gray-600">
                    {step.description}
                  </p>
                </div>
                
                {/* Arrow (except for last item) */}
                {index < steps.length - 1 && (
                  <div className="hidden md:block">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                    </svg>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <a
              href="mailto:<EMAIL>?subject=Portal Dev Inquiry&body=您好，我想了解门户开发服务，请联系我详细沟通。"
              className="inline-flex items-center px-8 py-4 bg-blue-600 text-white text-lg font-semibold rounded-lg hover:bg-blue-700 transition-colors"
            >
              开始合作
              <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}