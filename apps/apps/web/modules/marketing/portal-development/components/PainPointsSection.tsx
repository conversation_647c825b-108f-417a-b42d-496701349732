export function PainPointsSection() {
  const painPoints = [
    {
      concern: "报价不透明",
      solution: "公开价区间 $500–$3,000，确认需求后即锁定",
      icon: "💰"
    },
    {
      concern: "周期拖延",
      solution: "敏捷迭代，最短 7 天、最长 20 天",
      icon: "⏰"
    },
    {
      concern: "上线后没人维护",
      solution: "终身邮箱支持，Bug 48 h 内修复",
      icon: "🛠️"
    },
    {
      concern: "SEO & 速度",
      solution: "内置 SEO + 全球 CDN 加速",
      icon: "🚀"
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-16">
            常见痛点，我们一次解决
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            {painPoints.map((point, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-8 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="text-3xl">{point.icon}</div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-red-600 mb-2">
                      您的顾虑：{point.concern}
                    </h3>
                    <p className="text-gray-700 leading-relaxed">
                      <span className="text-green-600 font-medium">我们的做法：</span>
                      {point.solution}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}