export function ContactSection() {
  const benefits = [
    "方案报价表（USD 版）",
    "行业案例 PDF", 
    "项目时间排期表"
  ]

  return (
    <section className="py-20 bg-gradient-to-r from-blue-50 to-indigo-50">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
            立即邮件咨询
          </h2>
          
          <p className="text-xl text-gray-700 mb-8">
            发送一封邮件，免费获得：
          </p>
          
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900">
                  {benefit}
                </h3>
              </div>
            ))}
          </div>
          
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <div className="grid md:grid-cols-2 gap-6 text-left">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">收件人：</h4>
                <p className="text-blue-600 font-mono"><EMAIL></p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">邮件标题：</h4>
                <p className="text-gray-700 font-mono">Portal Dev Inquiry – [公司/品牌名]</p>
              </div>
            </div>
            
            <div className="mt-8 flex justify-center">
              <a
                href="mailto:<EMAIL>?subject=Portal Dev Inquiry – [请填写公司名]&body=您好，我们对门户网站开发服务感兴趣，希望了解：%0D%0A%0D%0A1. 项目类型：[请简单描述]%0D%0A2. 预期功能：[列出主要需求]%0D%0A3. 上线时间：[期望完成时间]%0D%0A4. 预算范围：[可选填写]%0D%0A%0D%0A请提供详细报价和案例，谢谢！"
                className="inline-flex items-center px-8 py-4 bg-blue-600 text-white text-lg font-semibold rounded-lg hover:bg-blue-700 transition-colors"
              >
                发送邮件咨询
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}