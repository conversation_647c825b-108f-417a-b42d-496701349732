import { Link } from '@i18n/routing'
import { Logo } from '@shared/components/Logo'
import { useTranslations } from 'next-intl'

export function Footer() {
  const t = useTranslations()

  return (
    <footer className="py-12">
      <div className="container grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div>
          <Logo className="opacity-70 grayscale" />
          <p className="mt-3 text-sm opacity-70">
            © {new Date().getFullYear()} <EMAIL>
          </p>
          <p className="mt-3 text-sm opacity-70">
            AIYONG TECHNOLOGY CO., LIMITED
          </p>
        </div>

        <div className="flex flex-col gap-2">
          {/* <Link href='/portal-development' className='block'>
            门户开发服务
          </Link> */}

          <a href="#features" className="block">
            Features
          </a>

          <a href="/pricing" className="block">
            Pricing
          </a>
        </div>

        <div className="flex flex-col gap-2">
          <Link href="/legal/privacy-policy" className="block">
            {t('privacyPolicy')}
          </Link>

          <Link href="/legal/terms" className="block">
            {t('termsAndConditions')}
          </Link>

          <Link href="/contact" className="block">
            {t('contact.text')}
          </Link>

          <Link href="/about" className="block">
            {t('aboutUs')}
          </Link>
        </div>
      </div>
    </footer>
  )
}
