'use client'

import { <PERSON> } from '@i18n/routing'
import { usePathname } from '@i18n/routing'
import { LocaleSwitch } from '@shared/components/LocaleSwitch'
import { Logo } from '@shared/components/Logo'
import { Button } from '@ui/components/button'
import { Sheet, SheetContent, SheetTrigger } from '@ui/components/sheet'
import { cn } from '@ui/lib'
import { MenuIcon } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useEffect, useRef, useState } from 'react'
import { useDebounceCallback } from 'usehooks-ts'
import { getUserFromClientCookies } from '../../../../app/utils/client-cookies'
import Cookies from 'js-cookie'

import { User, LogOut, ChevronDown } from 'lucide-react'
import { analyticsService } from '@/services/analytics'
import MarqueeAd from './MarqueeAd'

function UserMenu({ user, userInfoData }: any) {
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const t = useTranslations()

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLogout = () => {
    Cookies.remove('oauth_avatar')
    Cookies.remove('oauth_email')
    Cookies.remove('oauth_id')
    window.location.href = '/'
  }

  return (
    <div className="flex items-center gap-2">
      {/* <Button asChild variant='secondary' className='hidden md:flex'>
        <Link href='/app'>{t('common.menu.dashboard')}</Link>
      </Button> */}

      <div className="relative" ref={menuRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-2 rounded-lg border border-border/40 bg-background/95 md:px-3 px-2 py-2 hover:bg-accent transition-colors duration-200"
        >
          <div className="flex items-center gap-2">
            <div className="flex-shrink-0">
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.email}
                  className="md:h-8 md:w-8 rounded-full object-cover w-5 h-5"
                  style={{ aspectRatio: '1/1' }}
                />
              ) : (
                <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-primary/10 text-sm font-medium">
                  {user.email.substring(0, 1).toUpperCase()}
                </div>
              )}
            </div>
            <span className="hidden md:block text-sm font-medium">
              {user.email}
            </span>
          </div>
          <ChevronDown
            className={cn(
              'h-4 w-4 transition-transform duration-200',
              isOpen && 'rotate-180'
            )}
          />
        </button>

        {isOpen && (
          <div className="absolute right-0 mt-2 w-64 origin-top-right rounded-lg border border-border/40 shadow-lg animate-in fade-in-0 zoom-in-95 bg-black">
            <div className="px-4 py-3">
              <p className="text-sm font-medium">{user.email}</p>
              <p className="text-xs text-muted-foreground">
                {/* @ts-ignore */}
                {t('common.points')}: {userInfoData?.points}
              </p>
            </div>

            <div className="border-t border-border/40">
              <div className="flex flex-col py-2">
                {/* <Link
                  href='/app'
                  className='flex items-center px-4 py-2 text-sm hover:bg-accent transition-colors duration-200'
                  onClick={() => setIsOpen(false)}>
                  <LayoutDashboard className='mr-2 h-4 w-4' />
                  {t('common.menu.dashboard')}
                </Link> */}

                <Link
                  href="/auth/login"
                  className="flex items-center px-4 py-2 text-sm hover:bg-accent transition-colors duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  <User className="mr-2 h-4 w-4" />
                  {t('common.menu.profile')}
                </Link>

                {/* <Link
                  href='/app/billing'
                  className='flex items-center px-4 py-2 text-sm hover:bg-accent transition-colors duration-200'
                  onClick={() => setIsOpen(false)}>
                  <CreditCard className='mr-2 h-4 w-4' />
                  {t('common.menu.billing')}
                </Link>

                <Link
                  href='/app/settings'
                  className='flex items-center px-4 py-2 text-sm hover:bg-accent transition-colors duration-200'
                  onClick={() => setIsOpen(false)}>
                  <Settings className='mr-2 h-4 w-4' />
                  {t('common.menu.settings')}
                </Link> */}
              </div>
            </div>

            <div className="border-t border-border/40 py-2">
              <button
                onClick={handleLogout}
                className="flex w-full items-center px-4 py-2 text-sm text-destructive hover:bg-accent transition-colors duration-200"
              >
                <LogOut className="mr-2 h-4 w-4" />
                {/* @ts-ignore */}
                {t('common.menu.logout')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export function NavBar() {
  const t = useTranslations()

  const [userInfoData, setUserInfoData] = useState<any>({})
  const user = getUserFromClientCookies()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const pathname = usePathname()
  const [isTop, setIsTop] = useState(true)

  const getUserInfo = async () => {
    if (user && user.email) {
      // @ts-ignore
      const userInfoResponse = await fetch(
        `/api/user/info?email=${encodeURIComponent(user.email)}`
      )
      const userInfoData = await userInfoResponse.json()
      console.log('faith=============userInfoData', userInfoData)
      if (userInfoData.success) {
        setUserInfoData(userInfoData.data)
        analyticsService.trackUserVisit(userInfoData.data.id)
      }
    }
  }

  const debouncedScrollHandler = useDebounceCallback(
    () => {
      setIsTop(window.scrollY <= 10)
    },
    150,
    {
      maxWait: 150,
    }
  )

  useEffect(() => {
    window.addEventListener('scroll', debouncedScrollHandler)
    debouncedScrollHandler()
    return () => {
      window.removeEventListener('scroll', debouncedScrollHandler)
    }
  }, [debouncedScrollHandler])

  const handleScroll = useDebounceCallback(
    () => {
      const container = document.querySelector('.scroll-container')
      if (container) {
        setIsTop(container.scrollTop <= 10)
      }
    },
    150,
    {
      maxWait: 150,
    }
  )
  useEffect(() => {
    const container = document.querySelector('.scroll-container')
    if (container) {
      container.addEventListener('scroll', handleScroll)
      handleScroll()
      return () => {
        container.removeEventListener('scroll', handleScroll)
      }
    }
  }, [handleScroll])

  useEffect(() => {
    setMobileMenuOpen(false)
    getUserInfo()
  }, [pathname])

  const menuItems: {
    label: string
    href: string
  }[] = [
    {
      label: t('mySongs'),
      href: '/ai-music-generator',
    },
    {
      label: t('createSong'),
      href: '/ai-music-generator',
    },
    {
      label: t('menu.aiLyrics'),
      href: '/ai-lyrics-generator',
    },
    {
      label: t('vocalRemoverNav'),
      href: '/ai-vocal-remover',
    },
    {
      label: t('songCover1'),
      href: '/ai-song-cover',
    },
    {
      label: t('blog'),
      href: '/blog',
    },
    {
      label: t('common.menu.pricing'),
      href: '/pricing',
    },
    {
      label: t('contact.text'),
      href: '/contact',
    },
    // {
    //   label: t('common.menu.changelog'),
    //   href: '/changelog'
    // },
    // {
    //   label: t('common.menu.docs'),
    //   href: '/docs'
    // }
  ]

  const isMenuItemActive = (href: string) => pathname.startsWith(href)

  return (
    <nav
      className={`fixed top-0 left-0 z-50 w-full ${
        isTop ? 'shadow-none' : 'bg-card/80 shadow-sm backdrop-blur-lg'
      } transition-shadow duration-200`}
      data-test="navigation"
    >
      {pathname === '/' && (!user || user?.membershipStatus === 'free') && (
        <MarqueeAd theme="space" />
      )}
      <div className="container">
        <div
          className={`flex items-center justify-stretch gap-6 py-4 transition-[padding] duration-200`}
        >
          <div className="flex flex-1 justify-start">
            <Link
              href="/"
              className="block hover:no-underline active:no-underline flex-shrink-0 min-w-[180px]"
            >
              <Logo withLabel />
            </Link>
          </div>

          <div className="hidden flex-1 items-center justify-center md:flex">
            {menuItems.map((menuItem) => (
              <Link
                key={menuItem.href + menuItem.label}
                href={menuItem.href}
                className={cn(
                  'block px-3 py-2 font-medium text-foreground/80 text-sm whitespace-nowrap',
                  isMenuItemActive(menuItem.href)
                    ? 'font-bold text-foreground'
                    : ''
                )}
              >
                {menuItem.label}
              </Link>
            ))}
          </div>
          <div className="flex flex-1 items-center justify-end gap-3">
            {/* <ColorModeToggle /> */}
            <LocaleSwitch />

            <Sheet
              open={mobileMenuOpen}
              onOpenChange={(open) => setMobileMenuOpen(open)}
            >
              <SheetTrigger asChild>
                <Button
                  className="md:hidden"
                  size="icon"
                  variant="outline"
                  aria-label="Menu"
                >
                  <MenuIcon className="size-4" />
                </Button>
              </SheetTrigger>
              <SheetContent className="w-[250px]" side="right">
                <div className="flex flex-col items-start justify-center">
                  {menuItems.map((menuItem) => (
                    <Link
                      key={menuItem.href}
                      href={menuItem.href}
                      className={cn(
                        'block px-3 py-2 font-medium text-base text-foreground/80',
                        isMenuItemActive(menuItem.href)
                          ? 'font-bold text-foreground'
                          : ''
                      )}
                    >
                      {menuItem.label}
                    </Link>
                  ))}

                  <Link
                    key={user ? 'dashboard' : 'login'}
                    href={user ? '/app' : '/auth/login'}
                    className="block px-3 py-2 text-base"
                    prefetch={!user}
                  >
                    {user ? t('common.menu.dashboard') : t('common.menu.login')}
                  </Link>
                </div>
              </SheetContent>
            </Sheet>

            {user ? (
              <UserMenu user={user} userInfoData={userInfoData} />
            ) : (
              <Button
                key="login"
                className="hidden md:flex"
                asChild
                variant="secondary"
              >
                <Link href="/auth/login" className="min-w-[max-content]">
                  {t('common.menu.login')}
                </Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}
