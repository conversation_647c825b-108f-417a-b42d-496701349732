'use client'
import { useState, useEffect, useRef } from 'react'
import { X, CheckCircle, Music, Star, Zap } from 'lucide-react'
import { useLocale, useTranslations } from 'next-intl'
import Link from 'next/link'

export type MarqueeTheme = 'default' | 'space' | 'neon' | 'aurora'

interface MarqueeAdProps {
  /**
   * space: 星空主题
   * neon: 霓虹灯主题
   * aurora: 北极光主题
   */
  theme?: MarqueeTheme
}

const MarqueeAd = ({ theme = 'default' }: MarqueeAdProps) => {
  const t = useTranslations()
  const locale = useLocale()
  const [isVisible, setIsVisible] = useState(true)
  const [isExiting, setIsExiting] = useState(false)
  const containerRef = useRef(null)
  const buttonRef = useRef(null)
  const animationRef = useRef<any>(null)

  useEffect(() => {
    const timer = setTimeout(() => {
      const adShown = localStorage.getItem('adShown')
      if (!adShown) {
        localStorage.setItem('adShown', 'true')
      }
    }, 2000)

    // 入场动画
    const container = containerRef.current! as HTMLElement
    if (container) {
      container.style.transform = 'translateY(-100%)'
      container.style.opacity = '0'
      setTimeout(() => {
        container.style.transform = 'translateY(0)'
        container.style.opacity = '1'
      }, 100)
    }

    // 设置闪烁动画
    let sparkleInterval: any
    if (buttonRef.current) {
      sparkleInterval = setInterval(() => {
        const button = buttonRef.current! as HTMLElement
        if (button) {
          button.classList.add('marquee-ad-pulse-effect')
          setTimeout(
            () => button.classList.remove('marquee-ad-pulse-effect'),
            1000
          )
        }
      }, 3000)
    }

    return () => {
      clearTimeout(timer)
      clearInterval(sparkleInterval)
      cancelAnimationFrame(animationRef.current)
    }
  }, [])

  // 基于主题的粒子/背景效果
  useEffect(() => {
    const canvas = document.createElement('canvas')
    const ctx: any = canvas.getContext('2d')
    const elements: any[] = []

    const initCanvas = () => {
      const container = containerRef.current! as HTMLElement
      if (!container) return

      const canvasContainer = container.querySelector(
        '.marquee-ad-particle-canvas-container'
      )! as HTMLElement
      if (!canvasContainer) return

      canvas.width = canvasContainer.offsetWidth
      canvas.height = canvasContainer.offsetHeight
      canvasContainer.appendChild(canvas)

      // 根据不同主题创建不同类型的粒子或效果
      switch (theme) {
        case 'space':
          createStars()
          break
        case 'neon':
          createNeonLines()
          break
        case 'aurora':
          createAuroraParticles()
          break
        default:
          createDefaultParticles()
          break
      }
    }

    // 默认主题的粒子
    const createDefaultParticles = () => {
      for (let i = 0; i < 30; i++) {
        elements.push({
          type: 'particle',
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          radius: Math.random() * 1.5 + 0.5,
          vx: (Math.random() - 0.5) * 0.3,
          vy: (Math.random() - 0.5) * 0.3,
          hue: Math.random() * 60 + 280, // 紫色到粉色的色相范围
        })
      }
      animateElements()
    }

    // 空间主题的星星
    const createStars = () => {
      for (let i = 0; i < 50; i++) {
        elements.push({
          type: 'star',
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          radius: Math.random() * 1.5 + 0.3,
          opacity: Math.random() * 0.8 + 0.2,
          speed: Math.random() * 0.05 + 0.01,
          hue: Math.random() * 60 + 180, // 青蓝色调
        })
      }
      animateElements()
    }

    // 霓虹主题的线条
    const createNeonLines = () => {
      for (let i = 0; i < 20; i++) {
        const startX = Math.random() * canvas.width
        const endX = startX + (Math.random() - 0.5) * 200

        elements.push({
          type: 'neon',
          startX: startX,
          startY: Math.random() * canvas.height,
          endX: endX,
          endY: Math.random() * canvas.height,
          width: Math.random() * 2 + 0.5,
          speed: Math.random() * 0.5 + 0.2,
          hue: Math.floor(Math.random() * 360),
          alpha: 0,
          maxAlpha: Math.random() * 0.5 + 0.2,
          phase: Math.random() * Math.PI * 2,
        })
      }
      animateElements()
    }

    // 北极光主题的粒子
    const createAuroraParticles = () => {
      for (let i = 0; i < 200; i++) {
        elements.push({
          type: 'aurora',
          x: Math.random() * canvas.width,
          y: canvas.height + Math.random() * 10,
          size: Math.random() * 2 + 0.5,
          speedY: Math.random() * 0.8 + 0.2,
          speedX: (Math.random() - 0.5) * 0.4,
          hue: Math.floor(Math.random() * 40) + 160, // 主要是蓝绿色调
          opacity: Math.random() * 0.6 + 0.1,
        })
      }
      animateElements()
    }

    // 统一的动画函数，根据元素类型执行不同的动画逻辑
    const animateElements = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      const time = Date.now() * 0.001

      elements.forEach((el) => {
        switch (el.type) {
          case 'particle':
            // 默认粒子动画
            el.x += el.vx
            el.y += el.vy
            if (el.x < 0 || el.x > canvas.width) el.vx *= -1
            if (el.y < 0 || el.y > canvas.height) el.vy *= -1
            ctx.beginPath()
            ctx.arc(el.x, el.y, el.radius, 0, Math.PI * 2)
            ctx.fillStyle = `hsla(${el.hue}, 100%, 70%, 0.6)`
            ctx.fill()
            break

          case 'star':
            // 星星动画
            el.y -= el.speed
            if (el.y < -5) {
              el.y = canvas.height + 5
              el.x = Math.random() * canvas.width
            }
            // 闪烁效果
            el.opacity = 0.2 + Math.abs(Math.sin(time * 0.001 * el.speed)) * 0.8
            ctx.beginPath()
            ctx.arc(el.x, el.y, el.radius, 0, Math.PI * 2)
            ctx.fillStyle = `hsla(${el.hue}, 100%, 70%, ${el.opacity})`
            ctx.shadowColor = `hsla(${el.hue}, 100%, 70%, 0.8)`
            ctx.shadowBlur = 5
            ctx.fill()
            break

          case 'neon':
            // 霓虹线条动画
            el.alpha = Math.sin(time + el.phase) * el.maxAlpha
            if (el.alpha < 0) el.alpha = 0

            el.startX += el.speed
            el.endX += el.speed

            if (el.startX > canvas.width && el.endX > canvas.width) {
              el.startX = -100
              el.endX = el.startX + (Math.random() - 0.5) * 200
              el.startY = Math.random() * canvas.height
              el.endY = Math.random() * canvas.height
            }

            ctx.beginPath()
            ctx.moveTo(el.startX, el.startY)
            ctx.lineTo(el.endX, el.endY)
            ctx.lineWidth = el.width
            ctx.strokeStyle = `hsla(${el.hue}, 100%, 60%, ${el.alpha})`
            ctx.shadowColor = `hsla(${el.hue}, 100%, 70%, ${el.alpha})`
            ctx.shadowBlur = 10
            ctx.stroke()
            break

          case 'aurora':
            // 北极光粒子动画
            el.y -= el.speedY
            el.x += el.speedX
            el.x += Math.sin(el.y * 0.01) * 0.2

            if (el.y < -10) {
              el.y = canvas.height + 5
              el.x = Math.random() * canvas.width
            }

            if (el.x < 0 || el.x > canvas.width) {
              el.speedX *= -1
            }

            ctx.beginPath()
            ctx.arc(el.x, el.y, el.size, 0, Math.PI * 2)
            ctx.fillStyle = `hsla(${el.hue}, 100%, 70%, ${el.opacity})`
            ctx.fill()
            break
        }
      })

      animationRef.current = requestAnimationFrame(animateElements)
    }

    // 确保DOM加载完成后再初始化
    setTimeout(initCanvas, 100)

    return () => {
      cancelAnimationFrame(animationRef.current)
      if (canvas.parentNode) {
        canvas.parentNode.removeChild(canvas)
      }
    }
  }, [isVisible, theme])

  const handleClose = () => {
    setIsExiting(true)
    const container = containerRef.current! as HTMLElement
    if (container) {
      // 先向上移动一点
      container.style.transform = 'translateY(-5px)'
      container.style.opacity = '0.9'

      // 然后完全退出
      setTimeout(() => {
        container.style.transform = 'translateY(-100%)'
        container.style.opacity = '0'

        // 完成动画后再移除
        setTimeout(() => {
          setIsVisible(false)
          localStorage.setItem('adClosed', 'true')
        }, 300)
      }, 100)
    } else {
      setIsVisible(false)
      localStorage.setItem('adClosed', 'true')
    }
  }

  if (!isVisible) return null

  // 根据主题获取样式配置
  const getThemeStyles = () => {
    switch (theme) {
      case 'space':
        return {
          background: {
            gradient: 'linear-gradient(45deg, #0a192f, #112240, #1a365d)',
            backgroundImage:
              'radial-gradient(circle at 20% 30%, rgba(70, 131, 180, 0.4) 0%, transparent 40%), radial-gradient(circle at 80% 70%, rgba(138, 43, 226, 0.4) 0%, transparent 40%)',
            blur: '20px',
          },
          container: {
            background: 'rgba(10, 25, 47, 0.7)',
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.4)',
            backgroundImage:
              'linear-gradient(to right, rgba(21, 101, 192, 0.7), rgba(92, 39, 195, 0.7))',
          },
          glassEffect: 'bg-white/5 backdrop-blur-[2px]',
          buttonGradient: {
            from: 'from-blue-500/90',
            to: 'to-indigo-600/90',
            hover: 'hover:from-blue-400 hover:to-indigo-500',
          },
          iconGradient: {
            from: 'from-blue-500/90',
            to: 'to-indigo-600/90',
          },
          glowColor: 'bg-cyan-300/70',
          textColor: 'text-white',
        }
      case 'neon':
        return {
          background: {
            gradient: 'linear-gradient(45deg, #000428, #170b3b, #000428)',
            blur: '10px',
          },
          container: {
            background: 'rgba(10, 10, 30, 0.5)',
            boxShadow:
              '0 8px 32px rgba(2, 4, 24, 0.3), 0 0 0 1px rgba(60, 30, 130, 0.3)',
            border: '1px solid rgba(80, 50, 200, 0.1)',
            extraClasses: 'border border-cyan-500/30',
          },
          glassEffect: 'bg-black/10 backdrop-blur-[3px]',
          buttonGradient: {
            from: 'from-cyan-400/90',
            to: 'to-fuchsia-500/90',
            hover: 'hover:from-cyan-300 hover:to-fuchsia-400',
          },
          iconGradient: {
            from: 'from-cyan-400/90',
            to: 'to-blue-500/90',
            alt: {
              from: 'from-fuchsia-400/90',
              to: 'to-purple-500/90',
            },
          },
          glowColor: 'bg-yellow-300/70',
          textColor: 'text-cyan-100',
          altTextColor: 'text-fuchsia-100',
          highlightColor: 'text-yellow-300',
          buttonTextColor: 'text-yellow-300',
          gradientBorders: true,
        }
      case 'aurora':
        return {
          background: {
            gradient: 'linear-gradient(45deg, #0c1824, #112b3c, #05131e)',
            blur: '0px',
          },
          container: {
            background: 'rgba(12, 24, 36, 0.6)',
            boxShadow: '0 8px 32px rgba(2, 14, 24, 0.3)',
            extraClasses: 'border-t border-b border-teal-500/20',
          },
          glassEffect:
            'bg-gradient-to-r from-teal-500/5 via-blue-500/5 to-green-500/5 backdrop-blur-[3px]',
          buttonGradient: {
            from: 'from-emerald-500/90',
            to: 'to-blue-600/90',
            hover: 'hover:from-emerald-400 hover:to-blue-500',
          },
          iconGradient: {
            from: 'from-teal-400/90',
            to: 'to-green-500/90',
            alt: {
              from: 'from-blue-400/90',
              to: 'to-teal-500/90',
            },
            third: {
              from: 'from-emerald-400/90',
              to: 'to-teal-500/90',
            },
          },
          glowColor: 'bg-green-300/70',
          textColor: 'text-teal-50',
          altTextColor: 'text-blue-50',
          thirdTextColor: 'text-emerald-50',
          highlightColor: 'text-green-300',
          buttonTextColor: 'text-cyan-200',
          useAuroraWave: true,
        }
      default:
        return {
          background: {
            gradient: 'linear-gradient(45deg, #ff66c4, #cb6ce6, #8c52ff)',
            blur: '40px',
          },
          container: {
            background: 'rgba(255, 255, 255, 0.15)',
            boxShadow: '0 8px 25px rgba(31, 38, 135, 0.2)',
            backgroundImage:
              'linear-gradient(to right, rgba(236, 72, 153, 0.7), rgba(168, 85, 247, 0.7))',
          },
          glassEffect: 'bg-white/10 backdrop-blur-[2px]',
          buttonGradient: {
            from: 'from-pink-500/90',
            to: 'to-purple-600/90',
            hover: 'hover:from-pink-400 hover:to-purple-500',
          },
          iconGradient: {
            from: 'from-purple-500/90',
            to: 'to-pink-600/90',
          },
          glowColor: 'bg-yellow-300/70',
          textColor: 'text-white',
        }
    }
  }

  const themeStyles = getThemeStyles()

  return (
    <div
      className={`marquee-ad-wrapper w-full z-10 relative transition-all duration-300 ease-out ${
        isExiting ? 'pointer-events-none' : ''
      }`}
      style={{
        transition: 'transform 0.3s ease-out, opacity 0.3s ease-out',
      }}
      ref={containerRef}
    >
      {/* 用于粒子/星星/霓虹线/北极光动画的容器 */}
      <div className="marquee-ad-particle-canvas-container absolute inset-0 overflow-hidden"></div>

      {/* 主题背景 */}
      <div
        className="marquee-ad-bg-layer absolute inset-0 -z-10 opacity-60"
        style={{
          background: themeStyles.background.gradient,
          backgroundImage: themeStyles.background.backgroundImage,
          filter: `blur(${themeStyles.background.blur})`,
          transform: 'translateY(8px) scale(0.95)',
          transition: 'all 0.3s ease-out',
        }}
      ></div>

      {/* 北极光波浪背景(仅限aurora主题) */}
      {themeStyles.useAuroraWave && (
        <div
          className="marquee-ad-aurora-bg absolute inset-0 -z-5"
          style={{
            opacity: 0.4,
            mixBlendMode: 'screen',
          }}
        ></div>
      )}

      {/* 霓虹边框(仅限neon主题) */}
      {themeStyles.gradientBorders && (
        <>
          <div className="marquee-ad-neon-border-top absolute inset-x-0 top-0 h-[1px] bg-gradient-to-r from-transparent via-cyan-500/50 to-transparent"></div>
          <div className="marquee-ad-neon-border-bottom absolute inset-x-0 bottom-0 h-[1px] bg-gradient-to-r from-transparent via-fuchsia-500/50 to-transparent"></div>
        </>
      )}

      <div
        className={`marquee-ad-container relative overflow-hidden backdrop-blur-sm transition-all duration-300 ${
          themeStyles.container.extraClasses || ''
        }`}
        style={{
          background: themeStyles.container.background,
          boxShadow: themeStyles.container.boxShadow,
          backgroundImage: themeStyles.container.backgroundImage,
          border: themeStyles.container.border,
          transition: 'all 0.3s ease-out',
        }}
      >
        {/* 磨砂玻璃效果 */}
        <div
          className={`marquee-ad-glass-effect absolute inset-0 ${themeStyles.glassEffect}`}
        ></div>

        {/* 关闭按钮 */}
        <div className="marquee-ad-close-btn-wrapper absolute top-1/2 right-3 -translate-y-1/2 z-10">
          <button
            onClick={handleClose}
            className="marquee-ad-close-btn flex h-5 w-5 items-center justify-center rounded-full bg-white/20 text-white hover:bg-white/30 hover:text-white transition-all duration-200 hover:scale-110 group"
            aria-label="Close promotion"
          >
            <X
              size={12}
              className="group-hover:rotate-90 transition-transform duration-200"
            />
          </button>
        </div>

        <div className="marquee-ad-content py-2 px-4 pr-10 relative z-0">
          <div className="marquee-ad-items-container flex items-center justify-center flex-wrap sm:flex-nowrap gap-2 md:gap-4">
            {/* 只在非移动设备(sm及以上)显示 */}
            <div className="marquee-ad-item-creators hidden sm:flex items-center group">
              <span
                className={`marquee-ad-icon inline-flex flex-shrink-0 items-center justify-center h-6 w-6 rounded-full bg-gradient-to-br ${themeStyles.iconGradient.from} ${themeStyles.iconGradient.to} text-white mr-1.5 shadow-md group-hover:shadow-lg group-hover:scale-105 transition-all duration-200`}
              >
                <CheckCircle
                  size={12}
                  className="marquee-ad-ping-once group-hover:marquee-ad-animate-ping-once"
                />
              </span>
              <span
                className={`marquee-ad-text font-medium ${themeStyles.textColor} text-xs`}
              >
                {t('creatorsChoice')}
              </span>
            </div>

            {/* 只在非移动设备(sm及以上)显示 */}
            <div className="marquee-ad-item-music hidden sm:flex items-center group">
              <span
                className={`marquee-ad-icon inline-flex flex-shrink-0 items-center justify-center h-6 w-6 rounded-full bg-gradient-to-br ${
                  themeStyles.iconGradient.alt?.from ||
                  themeStyles.iconGradient.from
                } ${
                  themeStyles.iconGradient.alt?.to ||
                  themeStyles.iconGradient.to
                } text-white mr-1.5 shadow-md group-hover:shadow-lg group-hover:scale-105 transition-all duration-200`}
              >
                <Music size={12} className="group-hover:animate-bounce" />
              </span>
              <span
                className={`marquee-ad-text font-medium ${
                  themeStyles.altTextColor || themeStyles.textColor
                } text-xs`}
              >
                {t('extended8MinuteSongs')}
              </span>
            </div>

            <div className="marquee-ad-item-star flex items-center group">
              <span
                className={`marquee-ad-icon inline-flex flex-shrink-0 items-center justify-center h-6 w-6 rounded-full bg-gradient-to-br ${
                  themeStyles.iconGradient.third?.from ||
                  themeStyles.iconGradient.from
                } ${
                  themeStyles.iconGradient.third?.to ||
                  themeStyles.iconGradient.to
                } text-white mr-1.5 shadow-md group-hover:shadow-lg group-hover:scale-105 transition-all duration-200`}
              >
                <Star
                  size={12}
                  className="group-hover:marquee-ad-animate-spin-slow"
                />
              </span>
              <span
                className={`marquee-ad-text font-medium ${
                  themeStyles.thirdTextColor || themeStyles.textColor
                } text-xs whitespace-nowrap`}
              >
                {t('limitedTimeOffer')}{' '}
                <span
                  className={`marquee-ad-highlight font-bold ${
                    themeStyles.highlightColor || ''
                  } animate-pulse`}
                >
                  40%
                </span>
              </span>
            </div>
            <Link href={`${locale ? `/${locale}` : ''}/pricing`}>
              <button
                ref={buttonRef}
                className={`marquee-ad-subscribe-btn flex items-center px-3.5 py-1 bg-gradient-to-r ${themeStyles.buttonGradient.from} ${themeStyles.buttonGradient.to} ${themeStyles.buttonGradient.hover} text-white text-xs font-medium transition-all duration-300 shadow-md hover:shadow-lg rounded-full hover:scale-105 relative overflow-hidden group`}
              >
                {/* 闪光效果 */}
                <span className="marquee-ad-btn-shine absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>

                <Zap
                  size={12}
                  className={`mr-1.5 ${
                    themeStyles.buttonTextColor || ''
                  } animate-pulse`}
                />
                <span className="relative z-10">
                  {t('price.subscribeButton')}
                </span>

                {/* 闪烁光晕 */}
                <span
                  className={`marquee-ad-btn-glow absolute -right-1 -top-1 w-3 h-3 rounded-full ${themeStyles.glowColor} blur-sm animate-ping`}
                ></span>
              </button>
            </Link>
          </div>
        </div>
      </div>

      {/* 自定义样式 */}
      <style jsx>{`
        @keyframes marquee-ad-ping-once {
          0%,
          100% {
            transform: scale(1);
            opacity: 1;
          }
          50% {
            transform: scale(1.5);
            opacity: 0.8;
          }
        }

        @keyframes marquee-ad-spin-slow {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        .marquee-ad-animate-ping-once {
          animation: marquee-ad-ping-once 0.5s ease-in-out;
        }

        .marquee-ad-animate-spin-slow {
          animation: marquee-ad-spin-slow 3s linear infinite;
        }

        .marquee-ad-pulse-effect {
          animation: marquee-ad-pulse 1s cubic-bezier(0.4, 0, 0.6, 1);
        }

        @keyframes marquee-ad-pulse {
          0%,
          100% {
            box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
            transform: scale(1);
          }
          50% {
            box-shadow: 0 0 0 6px rgba(255, 255, 255, 0);
            transform: scale(1.05);
          }
        }

        .marquee-ad-aurora-bg {
          background: linear-gradient(
            120deg,
            rgba(32, 178, 170, 0.2) 10%,
            rgba(34, 205, 163, 0.2) 30%,
            rgba(45, 149, 237, 0.2) 50%,
            rgba(56, 124, 231, 0.2) 70%,
            rgba(16, 163, 127, 0.2) 90%
          );
          background-size: 300% 300%;
          animation: marquee-ad-aurora-wave 15s ease infinite;
        }

        @keyframes marquee-ad-aurora-wave {
          0% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
          100% {
            background-position: 0% 50%;
          }
        }

        .marquee-ad-shadow-neon-cyan {
          box-shadow: 0 0 10px rgba(34, 211, 238, 0.7);
        }

        .marquee-ad-shadow-neon-fuchsia {
          box-shadow: 0 0 10px rgba(217, 70, 239, 0.7);
        }

        .marquee-ad-shadow-neon-mixed {
          box-shadow: 0 0 15px rgba(125, 140, 255, 0.8);
        }
      `}</style>
    </div>
  )
}

export default MarqueeAd
