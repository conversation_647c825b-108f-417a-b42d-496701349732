export interface WhyUsItem {
  img: string
  alt: string
  title: string
  desc: string
  btn: string
  author: string
  avatar: string
  stars: number
}
export const getwhyUsData = (t: (key: string) => string): WhyUsItem[] => {
  return [
    {
      img: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/CC2.jpeg',
      alt: t('whyUs1Alt'),
      title: t('whyUs1Title'),
      desc: t('whyUs1Desc'),
      btn: t('whyUs1Btn'),
      author: t('whyUs1Author'),
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop&crop=face&auto=format',
      stars: 5,
    },
    {
      img: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/CC1.jpeg',
      alt: t('whyUs2Alt'),
      title: t('whyUs2Title'),
      desc: t('whyUs2Desc'),
      btn: t('whyUs2Btn'),
      author: t('whyUs2Author'),
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=200&fit=crop&crop=face&auto=format',
      stars: 5,
    },
  ]
}