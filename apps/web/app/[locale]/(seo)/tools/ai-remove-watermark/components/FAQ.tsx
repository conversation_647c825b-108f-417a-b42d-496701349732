'use client'
import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useInView } from 'react-intersection-observer'

const faqs = [
  {
    question: 'How does AI watermark removal work?',
    answer: 'Our AI uses advanced computer vision and machine learning algorithms to detect watermarks, logos, and text overlays in images. It then intelligently reconstructs the underlying image content to seamlessly remove the watermark while preserving image quality and details.'
  },
  {
    question: 'What types of watermarks can be removed?',
    answer: 'Our AI can remove various types of watermarks including text watermarks, logo overlays, transparent watermarks, semi-transparent overlays, and most stock photo watermarks. The success rate depends on the watermark complexity and image content.'
  },
  {
    question: 'Is it legal to remove watermarks from images?',
    answer: 'Removing watermarks is legal only if you own the image or have proper licensing rights. Never remove watermarks from copyrighted images without permission. Our tool is designed for legitimate use cases like removing watermarks from your own images or properly licensed content.'
  },
  {
    question: 'What image formats are supported?',
    answer: 'We support all major image formats including JPG, JPEG, PNG, WebP, BMP, TIFF, and GIF. Images can be up to 50MB in size and up to 8K resolution. The output format will match your input format or can be converted as needed.'
  },
  {
    question: 'How long does the watermark removal process take?',
    answer: 'Most images are processed within 2-5 seconds, depending on image size and complexity. Larger images or those with complex watermarks may take up to 10 seconds. Our AI is optimized for speed without compromising quality.'
  },
  {
    question: 'Is my data safe and private?',
    answer: 'Yes, your privacy is our priority. All images are processed securely on our servers and automatically deleted within 24 hours. We never store, share, or use your images for any other purpose. All processing is done over encrypted connections.'
  },
  {
    question: 'Can I use this tool for free?',
    answer: 'Yes! We offer free watermark removal for up to 5 images per day without registration. For unlimited usage and additional features like batch processing and priority support, consider our premium plans.'
  },
  {
    question: 'What if the watermark removal doesn\'t work perfectly?',
    answer: 'While our AI achieves excellent results in most cases, some complex watermarks may require manual touch-ups. We provide editing suggestions and offer a satisfaction guarantee. If you\'re not happy with the result, we\'ll work to improve it or provide a refund.'
  }
]

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null)
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="py-24 bg-gradient-to-b from-gray-900 to-slate-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Frequently Asked
            <span className="block bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
              Questions
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Get answers to common questions about our AI watermark removal tool and how it can help you.
          </p>
        </motion.div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group"
            >
              <div className="bg-white/5 backdrop-blur border border-white/10 rounded-2xl overflow-hidden hover:bg-white/10 transition-all duration-300">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-6 py-6 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-inset"
                >
                  <h3 className="text-lg font-semibold text-white group-hover:text-cyan-300 transition-colors duration-300 pr-4">
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{ rotate: openIndex === index ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="flex-shrink-0"
                  >
                    <svg
                      className="w-6 h-6 text-cyan-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </motion.div>
                </button>

                <AnimatePresence>
                  {openIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6">
                        <div className="w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-4"></div>
                        <p className="text-gray-300 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部联系信息 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-cyan-500/10 to-purple-500/10 border border-cyan-500/20 rounded-2xl p-8">
            <h3 className="text-xl font-bold text-white mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-300 mb-6">
              Our support team is here to help you get the most out of our AI watermark removal tool.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Contact Support
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 border-2 border-white/30 text-white font-semibold rounded-full hover:bg-white/10 transition-all duration-300"
              >
                View Documentation
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
