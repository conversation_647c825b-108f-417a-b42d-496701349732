'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'

const features = [
  {
    icon: '🎯',
    title: 'Unmatched Simplicity: Remove Objects From Photos in a Single Click',
    description:
      'Forget complex tools. Our AI intelligently detects and removes unwanted content. This free watermark remover is designed for maximum efficiency, saving you time while offering free cloud storage for your edited images. Your privacy is always protected.',
    gradient: 'from-blue-500 to-cyan-500',
  },
  {
    icon: '✨',
    title:
      'High-Precision Results: How to Remove Watermark From Photo Flawlessly',
    description:
      'Our powerful algorithm ensures clean, professional edits every time. This AI watermark remover rebuilds the background with stunning accuracy, leaving no smudges or blurs, making it the perfect tool to remove any object from a photo.',
    gradient: 'from-purple-500 to-pink-500',
  },
  {
    icon: '⚡',
    title: 'Lightning Fast Processing',
    description:
      'Remove watermarks in seconds, not minutes. Our optimized AI processes images at incredible speeds.',
    gradient: 'from-yellow-500 to-orange-500',
  },
  {
    icon: '🎯',
    title: 'Precise Watermark Detection',
    description:
      'Automatically identifies and removes various types of watermarks including text, logos, and transparent overlays.',
    gradient: 'from-green-500 to-emerald-500',
  },
  {
    icon: '📱',
    title: 'Multi-Device Support',
    description:
      'Works perfectly on desktop, tablet, and mobile devices with responsive design and touch support.',
    gradient: 'from-indigo-500 to-blue-500',
  },
  {
    icon: '🔒',
    title: 'Privacy & Security',
    description:
      'Your images are processed securely and automatically deleted after processing. We never store your data.',
    gradient: 'from-red-500 to-pink-500',
  },
]

export default function Features() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  return (
    <section className="w-full py-24 bg-gradient-to-b from-slate-900 to-gray-900">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Why Choose Our
            <span className="block bg-gradient-to-r mt-2 text-cyan-300">
              Free AI Watermark Remover?
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our tool is more than just a simple editor. See how our AI watermark
            remover technology provides superior results with unmatched
            simplicity and efficiency for all your photo needs.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className="group relative"
            >
              <div className="relative p-8 overflow-hidden bg-white/5 backdrop-blur border border-white/10 rounded-2xl hover:bg-white/10 transition-all duration-300 h-full">
                {/* 悬停光效 */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-2xl"></div>

                {/* 图标 */}
                <div
                  className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${feature.gradient} rounded-2xl mb-6 text-2xl`}
                >
                  {feature.icon}
                </div>

                {/* 内容 */}
                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-cyan-300 transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>

                {/* 装饰性边框 */}
                <div
                  className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${feature.gradient} rounded-t-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                ></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部统计 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
        >
          {[
            { number: '1M+', label: 'Images Processed' },
            { number: '99.9%', label: 'Success Rate' },
            { number: '<3s', label: 'Average Processing Time' },
            { number: '50+', label: 'Supported Formats' },
          ].map((stat, index) => (
            <div key={index} className="group">
              <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">
                {stat.number}
              </div>
              <div className="text-gray-300 text-sm font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
