'use client'
import { motion } from 'framer-motion'
import { Link } from '@i18n/routing'
import { useEffect, useState } from 'react'
import WatermarkRemovalDemo from './WatermarkRemovalDemo'

export default function Hero({ link = '' }) {
  const [particles, setParticles] = useState<
    Array<{ left: string; top: string; delay: number }>
  >([])

  useEffect(() => {
    // 只在客户端生成粒子位置
    const particleData = Array.from({ length: 50 }, (_, i) => ({
      left: `${Math.random() * 100}%`,
      top: `${Math.random() * 100}%`,
      delay: Math.random() * 2,
    }))
    setParticles(particleData)
  }, [])

  return (
    <section className="relative w-full min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
      {/* 动态背景效果 */}
      <div className="absolute inset-0">
        {/* 渐变背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-cyan-900/20 via-purple-900/30 to-pink-900/20"></div>

        {/* 动态粒子效果 */}
        <div className="absolute inset-0">
          {particles.map((particle, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-cyan-400/30 rounded-full"
              style={{
                left: particle.left,
                top: particle.top,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.3, 1, 0.3],
              }}
              transition={{
                duration: 3 + (i % 3),
                repeat: Infinity,
                delay: particle.delay,
              }}
            />
          ))}
        </div>

        {/* 网格背景 */}
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `linear-gradient(rgba(56, 189, 248, 0.1) 1px, transparent 1px),
                             linear-gradient(90deg, rgba(56, 189, 248, 0.1) 1px, transparent 1px)`,
            backgroundSize: '50px 50px',
          }}
        />
      </div>

      <div className="relative z-10 w-full px-4 py-16 pt-32 md:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-6"
          >
            <span className="inline-block px-4 py-2 bg-cyan-500/20 text-cyan-300 text-sm font-semibold rounded-full border border-cyan-500/30 mb-6">
              🚀 AI-Powered Technology
            </span>
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
              Remove Watermarks
              <span className="block bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                Instantly with AI
              </span>
            </h1>
          </motion.div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mx-auto max-w-3xl text-xl text-gray-300 mb-12 leading-relaxed"
          >
            Transform your images with our advanced AI watermark removal tool.
            Get clean, professional results in seconds without compromising
            image quality.
          </motion.p>

          {/* 特性标签 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-wrap justify-center gap-4 mb-12"
          >
            {[
              '✨ AI-Powered',
              '⚡ Lightning Fast',
              '🎯 Precise Removal',
              '📱 Mobile Friendly',
            ].map((feature, index) => (
              <span
                key={index}
                className="px-4 py-2 bg-white/10 backdrop-blur text-white text-sm font-medium rounded-full border border-white/20"
              >
                {feature}
              </span>
            ))}
          </motion.div>
        </div>

        {/* 演示区域 */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 0.6 }}
          className="mb-16"
        >
          <WatermarkRemovalDemo
            beforeImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop"
            afterImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop"
            className="max-w-4xl mx-auto"
          />
        </motion.div>

        {/* CTA 按钮 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-center"
        >
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="group relative px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-bold text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
            >
              <Link href={link} className="relative z-10">
                Start Removing Watermarks
              </Link>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 border-2 border-white/30 text-white font-semibold text-lg rounded-full hover:bg-white/10 transition-all duration-300"
            >
              Watch Demo
            </motion.button>
          </div>

          <p className="mt-4 text-gray-400 text-sm">
            No signup required • Process up to 5 images free
          </p>
        </motion.div>
      </div>
    </section>
  )
}
