'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'

const useCases = [
  {
    title: 'E-commerce Product Photos',
    description:
      'Remove watermarks from stock photos to create clean product listings that convert better.',
    icon: '🛍️',
    image:
      'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop',
    gradient: 'from-blue-500 to-cyan-500',
    benefits: [
      'Higher conversion rates',
      'Professional appearance',
      'Brand consistency',
    ],
  },
  {
    title: 'Social Media Content',
    description:
      'Create engaging social media posts without distracting watermarks or logos.',
    icon: '📱',
    image:
      'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=400&h=300&fit=crop',
    gradient: 'from-pink-500 to-purple-500',
    benefits: ['Better engagement', 'Clean aesthetics', 'Brand focus'],
  },
  {
    title: 'Marketing Materials',
    description:
      'Prepare high-quality images for brochures, flyers, and digital marketing campaigns.',
    icon: '📊',
    image:
      'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
    gradient: 'from-green-500 to-emerald-500',
    benefits: ['Professional quality', 'Print-ready', 'Cost-effective'],
  },
  {
    title: 'Website Design',
    description:
      'Use clean images for website headers, backgrounds, and content without watermarks.',
    icon: '💻',
    image:
      'https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=400&h=300&fit=crop',
    gradient: 'from-orange-500 to-red-500',
    benefits: ['Better UX', 'Professional look', 'SEO friendly'],
  },
  {
    title: 'Presentations',
    description:
      'Create impactful presentations with clean, watermark-free images that look professional.',
    icon: '📽️',
    image:
      'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=400&h=300&fit=crop',
    gradient: 'from-purple-500 to-indigo-500',
    benefits: ['Professional slides', 'Clear visuals', 'Audience focus'],
  },
  {
    title: 'Content Creation',
    description:
      'Remove watermarks from reference images for blogs, articles, and creative projects.',
    icon: '✍️',
    image:
      'https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?w=400&h=300&fit=crop',
    gradient: 'from-teal-500 to-blue-500',
    benefits: ['Creative freedom', 'Original content', 'Copyright compliance'],
  },
]

export default function UseCases() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  return (
    <section className="w-full py-24 bg-gradient-to-b from-slate-900 to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Real-World Uses for Our
            <span className="block bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
              Object Removal Tool
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            From e-commerce to content creation, our AI watermark remover helps
            professionals and creators achieve their goals with clean,
            high-quality images.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {useCases.map((useCase, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className="group relative overflow-hidden"
            >
              <div className="relative bg-white/5 backdrop-blur border border-white/10 rounded-2xl overflow-hidden hover:bg-white/10 transition-all duration-500 h-full">
                {/* 图片区域 */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={useCase.image}
                    alt={useCase.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>

                  {/* 图标 */}
                  <div
                    className={`absolute top-4 right-4 w-12 h-12 bg-gradient-to-r ${useCase.gradient} rounded-xl flex items-center justify-center text-xl shadow-lg`}
                  >
                    {useCase.icon}
                  </div>
                </div>

                {/* 内容区域 */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-3 group-hover:text-cyan-300 transition-colors duration-300">
                    {useCase.title}
                  </h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    {useCase.description}
                  </p>

                  {/* 优势列表 */}
                  <div className="space-y-2">
                    {useCase.benefits.map((benefit, benefitIndex) => (
                      <div
                        key={benefitIndex}
                        className="flex items-center space-x-2 text-sm"
                      >
                        <div
                          className={`w-2 h-2 bg-gradient-to-r ${useCase.gradient} rounded-full`}
                        ></div>
                        <span className="text-gray-300">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 悬停效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                {/* 顶部装饰线 */}
                <div
                  className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${useCase.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                ></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部 CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-cyan-500/20 to-purple-500/20 border border-cyan-500/30 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Transform Your Images?
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Join thousands of professionals who trust our AI watermark remover
              for their image editing needs.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all text-white font-bold text-lg rounded-full shadow-lg hover:shadow-xl"
            >
              Start Removing Watermarks Now
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
