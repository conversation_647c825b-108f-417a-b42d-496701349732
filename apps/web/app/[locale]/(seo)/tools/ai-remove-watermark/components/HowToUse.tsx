'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'

const steps = [
  {
    number: '01',
    title: 'Upload Your Image',
    description:
      'Simply drag and drop your image or click to browse. We support JPG, PNG, WebP, and more formats.',
    icon: '📤',
    color: 'from-blue-500 to-cyan-500',
  },
  {
    number: '02',
    title: 'AI Analysis',
    description:
      'Our advanced AI automatically detects watermarks, logos, and text overlays in your image.',
    icon: '🔍',
    color: 'from-purple-500 to-pink-500',
  },
  {
    number: '03',
    title: 'Smart Removal',
    description:
      'The AI intelligently removes watermarks while preserving the original image quality and details.',
    icon: '✨',
    color: 'from-green-500 to-emerald-500',
  },
  {
    number: '04',
    title: 'Download Result',
    description:
      'Get your clean, watermark-free image in high quality. No registration or payment required.',
    icon: '⬇️',
    color: 'from-orange-500 to-red-500',
  },
]

export default function HowToUse() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  return (
    <section className="w-full py-24 bg-gradient-to-b from-gray-900 to-slate-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            How to Remove Watermarks
            <span className="block bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
              in 4 Simple Steps
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our AI-powered watermark removal process is designed to be simple,
            fast, and effective. Get professional results without any technical
            expertise.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* 步骤列表 */}
          <div className="space-y-8">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -30 }}
                animate={inView ? { opacity: 1, x: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="group flex items-start space-x-6"
              >
                {/* 步骤编号 */}
                <div
                  className={`flex-shrink-0 w-16 h-16 bg-gradient-to-r ${step.color} rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-transform duration-300`}
                >
                  {step.number}
                </div>

                {/* 步骤内容 */}
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-2xl">{step.icon}</span>
                    <h3 className="text-xl font-bold text-white group-hover:text-cyan-300 transition-colors duration-300">
                      {step.title}
                    </h3>
                  </div>
                  <p className="text-gray-300 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* 可视化演示 */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <div className="relative bg-white/5 backdrop-blur border border-white/10 rounded-3xl p-8 shadow-2xl">
              {/* 模拟界面 */}
              <div className="space-y-6">
                {/* 上传区域 */}
                <div className="border-2 border-dashed border-cyan-400/50 rounded-2xl p-8 text-center bg-cyan-400/5">
                  <div className="text-4xl mb-4">📤</div>
                  <p className="text-white font-semibold">
                    Drop your image here
                  </p>
                  <p className="text-gray-400 text-sm mt-2">
                    or click to browse
                  </p>
                </div>

                {/* 处理按钮 */}
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full py-4 bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-bold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Remove Watermark with AI
                </motion.button>

                {/* 进度指示 */}
                <div className="space-y-3">
                  <div className="flex justify-between text-sm text-gray-300">
                    <span>Processing...</span>
                    <span>85%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <motion.div
                      className="bg-gradient-to-r from-cyan-400 to-blue-500 h-2 rounded-full"
                      initial={{ width: '0%' }}
                      animate={inView ? { width: '85%' } : {}}
                      transition={{ duration: 2, delay: 1 }}
                    />
                  </div>
                </div>
              </div>

              {/* 装饰性光效 */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-cyan-400 rounded-full blur-lg opacity-60 animate-pulse"></div>
              <div
                className="absolute -bottom-4 -left-4 w-6 h-6 bg-purple-400 rounded-full blur-lg opacity-60 animate-pulse"
                style={{ animationDelay: '1s' }}
              ></div>
            </div>
          </motion.div>
        </div>

        {/* 底部提示 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1 }}
          className="mt-16 text-center"
        >
          <div className="inline-flex items-center space-x-2 px-6 py-3 bg-green-500/20 border border-green-500/30 rounded-full text-green-300">
            <span className="text-lg">✅</span>
            <span className="font-semibold">
              No signup required • Free to use • Instant results
            </span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
