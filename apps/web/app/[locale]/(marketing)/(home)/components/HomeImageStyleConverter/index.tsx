'use client'

import { useState, useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useDropzone } from 'react-dropzone'
//
import {
  UploadCloud,
  X,
  AlertCircle,
  ImageIcon,
  Sparkles,
  Shuffle,
} from 'lucide-react'
import Image from 'next/image'
import { useRouter } from '@i18n/routing'
import { useAtom } from 'jotai'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'
//
import {
  imagesAtom,
  persistedOssUrlsAtom,
  type ImageItem,
  formDataAtom,
  formSchema,
  type FormData,
  selectedStyleAtom,
  isStyleCancelledAtom,
  isGeneratingAtom,
  generationErrorAtom,
  generationModeAtom,
} from '@marketing/home/<USER>'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import ShowLoginModal from '@shared/components/ShowLoginModal'
import { useTranslations } from 'next-intl'
import { useStyles } from '@marketing/home/<USER>/useStyles'

// API related configuration
const DEFAULT_MAX_UPLOAD_IMAGES = 5 // 默认最大上传图片数量限制

// LocalStorage keys
const STORAGE_KEY_FORM = 'image_converter_form'
const STORAGE_KEY_MODE = 'image_converter_mode'

// 定义生成模式类型
type GenerationMode = 'text-to-image' | 'image-to-image'

// 安全地从 localStorage 读取数据的辅助函数
const getFromLocalStorage = <T,>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') {
    return defaultValue
  }

  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error(`Error reading ${key} from localStorage:`, error)
    return defaultValue
  }
}

// 安全地将数据写入 localStorage 的辅助函数
const saveToLocalStorage = <T,>(key: string, value: T): void => {
  if (typeof window === 'undefined') {
    return
  }

  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error)
  }
}

// 安全地从 sessionStorage 读取数据的辅助函数
const getFromSessionStorage = <T,>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') {
    return defaultValue
  }

  try {
    const item = sessionStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error(`Error reading ${key} from sessionStorage:`, error)
    return defaultValue
  }
}

// 安全地将数据写入 sessionStorage 的辅助函数
const saveToSessionStorage = <T,>(key: string, value: T): void => {
  if (typeof window === 'undefined') {
    return
  }

  try {
    sessionStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error saving ${key} to sessionStorage:`, error)
  }
}

type HomeImageStyleConverterProps = {
  onGenerated?: (generatedUrl: string) => void
  onStartGenerating?: () => void
  redirectOnGenerate?: boolean
  onError?: () => void
  maxUploadImages?: number // 最大上传图片数量
  mode?: GenerationMode // 生成模式：文生图或图生图
  defaultMode?: GenerationMode // 默认生成模式
}

// Custom button component
const Button = ({
  children,
  type = 'button',
  onClick,
  disabled = false,
  className = '',
  variant = 'primary',
}: {
  children: React.ReactNode
  type?: 'button' | 'submit' | 'reset'
  onClick?: () => void
  disabled?: boolean
  className?: string
  variant?: 'primary' | 'secondary' | 'danger' | 'outline'
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 text-white'
      case 'secondary':
        return 'bg-gray-600 hover:bg-gray-700 text-white'
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 text-white'
      case 'outline':
        return 'bg-white/10 backdrop-blur-md border border-white/30 hover:bg-white/20 text-gray-700'
      default:
        return 'bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 text-white'
    }
  }

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
        disabled
          ? 'opacity-50 cursor-not-allowed'
          : 'hover:-translate-y-0.5 hover:shadow-md'
      } ${getVariantClasses()} ${className}`}
    >
      {children}
    </button>
  )
}

export function HomeImageStyleConverter({
  onStartGenerating,
  maxUploadImages = DEFAULT_MAX_UPLOAD_IMAGES,
  mode,
  defaultMode = 'image-to-image', // 首页默认使用文生图模式
}: HomeImageStyleConverterProps) {
  const t = useTranslations()
  const router = useRouter()
  // 使用整合的图片数据
  const [images, setImages] = useAtom(imagesAtom)
  // 持久化的OSS URL，用于页面刷新后恢复
  const [persistedOssUrls, setPersistedOssUrls] = useAtom(persistedOssUrlsAtom)
  const [formData, setFormData] = useAtom(formDataAtom)
  const [selectedStyle, setSelectedStyle] = useAtom(selectedStyleAtom)
  const [isStyleCancelled, setIsStyleCancelled] = useAtom(isStyleCancelledAtom)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const user = getUserFromClientCookies()

  // 使用 Jotai 的 atomWithStorage 来持久化生成模式
  const [generationMode, setGenerationMode] = useAtom(generationModeAtom)

  // 使用 React Query 获取风格数据
  const {
    data: styles = [],
    isLoading: isLoadingStyles,
    isSuccess: isStylesSuccess,
  } = useStyles(selectedStyle?.id, generationMode)

  // 如果传入了 mode 属性，优先使用它
  useEffect(() => {
    // 从 localStorage 获取保存的模式
    const savedMode = getFromLocalStorage<GenerationMode | null>(
      STORAGE_KEY_MODE,
      null
    )

    if (mode) {
      // 如果传入了 mode 属性，优先使用它
      setGenerationMode(mode)
      // 同时保存到 localStorage
      saveToLocalStorage(STORAGE_KEY_MODE, mode)
    } else if (savedMode) {
      // 如果 localStorage 中有保存的模式，使用它
      setGenerationMode(savedMode)
    } else if (defaultMode) {
      // 如果有默认模式，使用它
      setGenerationMode(defaultMode)
      // 同时保存到 localStorage
      saveToLocalStorage(STORAGE_KEY_MODE, defaultMode)
    }
  }, [mode, defaultMode, setGenerationMode])

  // Use global state
  const [isGenerating, setIsGenerating] = useAtom(isGeneratingAtom)
  const [generationError, setGenerationError] = useAtom(generationErrorAtom)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false)

  // 从 sessionStorage 获取表单数据
  const savedFormData = useMemo(() => {
    const data = getFromSessionStorage<FormData | null>(STORAGE_KEY_FORM, null)
    console.log('Initial savedFormData from sessionStorage:', data)
    return data
  }, [])

  // 默认表单数据
  const defaultFormValues: FormData = {
    prompt: '',
    ratio: '1:1',
    nVariants: '1',
  }

  // Form configuration - 只使用默认值初始化表单
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultFormValues,
  })

  // 在组件挂载时，确保表单数据从 sessionStorage 中正确加载
  useEffect(() => {
    // 使用 setTimeout 确保在组件完全渲染后执行
    const timer = setTimeout(() => {
      console.log(
        'Loading form data from sessionStorage (delayed):',
        savedFormData
      )

      // 如果有保存的表单数据
      if (savedFormData) {
        // 手动设置表单值，确保它们被正确加载
        if (savedFormData.prompt) {
          console.log('Setting prompt value:', savedFormData.prompt)
          form.setValue('prompt', savedFormData.prompt)
        }
        if (savedFormData.ratio) {
          form.setValue('ratio', savedFormData.ratio)
        }
        if (savedFormData.nVariants) {
          form.setValue('nVariants', savedFormData.nVariants)
        }

        // 同时更新 formData atom
        setFormData(savedFormData)

        // 强制表单重新渲染
        form.trigger()
      }
    }, 100)

    return () => clearTimeout(timer)
  }, [savedFormData, form, setFormData])

  // 处理风格选择
  const handleStyleSelect = useCallback(
    (style: any) => {
      setSelectedStyle(style)

      // 更新表单数据
      const newFormData = {
        prompt: style.prompt,
        ratio: form.getValues('ratio') || '3:2',
        nVariants: form.getValues('nVariants') || '1',
      }

      // 更新 formData atom
      setFormData(newFormData)

      // 直接更新表单值
      form.setValue('prompt', style.prompt)

      // 保存到 sessionStorage
      saveToSessionStorage(STORAGE_KEY_FORM, newFormData)

      setIsStyleCancelled(false) // 重置取消状态
    },
    [setSelectedStyle, setFormData, form, setIsStyleCancelled]
  )

  // 随机选择一种风格
  const handleRandomStyle = useCallback(() => {
    console.log('handleRandomStyle executing, styles length:', styles.length)
    if (styles.length > 0) {
      const randomIndex = Math.floor(Math.random() * styles.length)
      const randomStyle = styles[randomIndex]
      console.log('Selected random style:', randomStyle)

      // 直接调用 handleStyleSelect 函数，确保一致的处理逻辑
      handleStyleSelect(randomStyle)
    } else {
      console.log('No styles available for random selection')
    }
  }, [styles, handleStyleSelect])

  // 在组件加载且风格数据加载完成后，检查是否有持久化的风格选择，如果没有则随机选择一个默认风格
  useEffect(() => {
    // 只有当风格数据加载完成时才执行
    if (styles.length > 0) {
      // 如果已经有选中的风格（从localStorage恢复），但是这个风格ID在当前风格列表中不存在
      // 这种情况可能发生在风格列表更新后，之前选中的风格不再可用
      if (
        selectedStyle &&
        !styles.some((style) => style.id === selectedStyle.id)
      ) {
        // 清除无效的选中风格
        setSelectedStyle(null)
      }

      // 如果没有选中的风格且没有手动取消风格选择，则随机选择一个风格
      if (!selectedStyle && !isStyleCancelled) {
        // 使用setTimeout确保在组件完全渲染后执行
        const timer = setTimeout(() => {
          handleRandomStyle()
        }, 100)

        return () => clearTimeout(timer)
      }
    }
  }, [styles, selectedStyle, isStyleCancelled, handleRandomStyle])

  // 添加一个额外的 useEffect 来处理风格数据加载状态变化
  useEffect(() => {
    // 当风格数据从加载中变为加载完成时
    console.log('Style data loading state changed:', {
      isLoadingStyles,
      isStylesSuccess,
      stylesLength: styles.length,
      hasSelectedStyle: !!selectedStyle,
      isStyleCancelled,
    })

    if (
      !isLoadingStyles &&
      isStylesSuccess &&
      styles.length > 0 &&
      !selectedStyle &&
      !isStyleCancelled
    ) {
      console.log(
        'Conditions met for random style selection, calling handleRandomStyle'
      )
      // 强制延迟一点时间，确保在DOM完全渲染后执行
      setTimeout(() => {
        handleRandomStyle()
        console.log('handleRandomStyle called')
      }, 300)
    }
  }, [
    isLoadingStyles,
    isStylesSuccess,
    styles,
    selectedStyle,
    isStyleCancelled,
    handleRandomStyle,
  ])

  // 添加一个初始化 useEffect，确保在组件挂载时能够正确选择风格
  useEffect(
    () => {
      console.log('Component mounted, initializing...')

      // 如果已经有选中的风格，不需要再选择
      if (selectedStyle) {
        console.log('Already has selected style:', selectedStyle)
        return
      }

      // 如果风格数据已经加载完成，但没有选中的风格，则选择一个随机风格
      if (styles.length > 0 && !isStyleCancelled) {
        console.log('Styles available on mount, selecting random style')
        // 使用setTimeout确保在DOM完全渲染后执行
        setTimeout(() => {
          handleRandomStyle()
        }, 500)
      } else {
        console.log('No styles available on mount or style cancelled')
      }

      // 这个 useEffect 只在组件挂载时执行一次
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    [
      /* 故意保持空依赖数组，确保只在挂载时执行一次 */
    ]
  )

  // Listen for selected style, update form prompt
  useEffect(() => {
    if (selectedStyle?.prompt) {
      // 更新表单数据
      const newFormData = {
        prompt: selectedStyle.prompt,
        ratio: form.getValues('ratio') || '3:2',
        nVariants: form.getValues('nVariants') || '1',
      }

      // 更新 formData atom
      setFormData(newFormData)

      // 直接更新表单值
      form.setValue('prompt', selectedStyle.prompt)

      // 保存到 sessionStorage
      saveToSessionStorage(STORAGE_KEY_FORM, newFormData)

      setIsStyleCancelled(false) // Reset cancellation status when selecting a new style
    }
  }, [selectedStyle, form, setFormData, setIsStyleCancelled])

  // Listen for style cancellation status
  useEffect(() => {
    if (isStyleCancelled) {
      form.setValue('prompt', '')
    }
  }, [isStyleCancelled, form])

  // Check if form is valid
  const isFormValid = form.watch('prompt')?.trim()

  // Form submission handling
  const onSubmit = useCallback(
    async (values: FormData) => {
      if (!values.prompt.trim()) {
        setGenerationError(t('ImageStyleConverter.promptRequired'))
        setHasAttemptedSubmit(true)
        return
      }

      // Check points first
      if (!user) {
        setShowLoginModal(true)
        return
      }

      // If already submitting or there's an ongoing task, don't submit again
      if (isSubmitting) {
        return
      }

      setGenerationError(null)
      setIsSubmitting(true)
      setHasAttemptedSubmit(true)
      onStartGenerating?.()

      // 保存表单数据
      setFormData(values)

      // 设置生成标志，表示需要在目标页面自动触发生成
      sessionStorage.setItem('auto_generate_on_redirect', 'true')

      // 跳转到图片生成页面
      router.push('/ai-generate-image')
    },
    [
      images,
      generationMode,
      router,
      setFormData,
      onStartGenerating,
      isSubmitting,
      user,
      setShowLoginModal,
      setGenerationError,
      setIsSubmitting,
      t,
    ]
  )

  // Listen for form changes
  useEffect(() => {
    const subscription = form.watch((values, { name }) => {
      console.log('Form value changed:', name, values)

      if (name === 'prompt' || name === 'ratio' || name === 'nVariants') {
        // 保存表单数据到 sessionStorage
        if (values.prompt || values.ratio || values.nVariants) {
          const formData = {
            prompt: values.prompt || '',
            ratio: values.ratio || '3:2',
            nVariants: values.nVariants || '1',
          }
          console.log('Saving form data to sessionStorage:', formData)
          saveToSessionStorage(STORAGE_KEY_FORM, formData)

          // 同时更新 formData atom
          setFormData(formData)
        }
      }
    })
    return () => subscription.unsubscribe()
  }, [form, setFormData])

  // 组件加载时，从持久化的 OSS URL 恢复图片
  useEffect(() => {
    const restoreImagesFromOss = async () => {
      // 如果有持久化的 OSS URL，则使用它们创建图片数据
      if (persistedOssUrls.length > 0 && images.length === 0) {
        try {
          console.log('Restoring images from OSS URLs:', persistedOssUrls)

          // 创建新的图片数据数组
          const restoredImages: ImageItem[] = persistedOssUrls.map(
            (ossUrl) => ({
              previewUrl: ossUrl, // 使用 OSS URL 作为预览
              ossUrl: ossUrl, // 保存 OSS URL
              uploading: false, // 标记为已完成上传
            })
          )

          // 更新图片数据
          setImages(restoredImages)
        } catch (error) {
          console.error('Failed to restore images from OSS URLs:', error)
        }
      }
    }

    // 在组件挂载时恢复图片
    restoreImagesFromOss()
  }, [persistedOssUrls, images.length, setImages])

  // File upload handling
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return

      // 获取图片尺寸的函数
      const getImageDimensions = (
        url: string
      ): Promise<{ width: number; height: number }> => {
        return new Promise((resolve, reject) => {
          const img = document.createElement('img')
          img.onload = () => {
            resolve({
              width: img.width,
              height: img.height,
            })
          }
          img.onerror = reject
          img.src = url
        })
      }

      // 检查是否超过最大图片数量限制
      const remainingSlots = maxUploadImages - images.length

      if (remainingSlots <= 0) {
        setGenerationError(
          `You can upload a maximum of ${maxUploadImages} images`
        )
        return
      }

      // 限制添加的文件数量
      const filesToAdd = acceptedFiles.slice(0, remainingSlots)

      // 如果尝试上传的图片超过限制，显示提示
      if (acceptedFiles.length > remainingSlots) {
        setGenerationError(
          `Maximum upload limit reached (${maxUploadImages} images). Only added the first ${remainingSlots} image(s)`
        )
      } else {
        setGenerationError(null)
      }

      // 创建新的图片数据数组
      const newImages = [...images]

      // 处理每个文件
      for (const file of filesToAdd) {
        try {
          // 创建本地预览
          const filePreview = URL.createObjectURL(file)

          // 创建新的图片数据项
          const newImageItem: ImageItem = {
            file, // 保存文件对象
            previewUrl: filePreview, // 使用本地预览 URL
            uploading: true, // 标记为正在上传
          }

          // 添加到图片数组
          newImages.push(newImageItem)

          // 获取当前图片的索引
          const currentIndex = newImages.length - 1

          // 在后台上传到 OSS - 使用闭包捕获当前索引
          const uploadCurrentFile = async (
            fileToUpload: File,
            fileIndex: number
          ) => {
            try {
              console.log(
                `开始上传图片: ${fileToUpload.name}, 大小: ${
                  fileToUpload.size / 1024 / 1024
                } MB`
              )

              // 直接上传原始文件到 OSS
              const ossUrl = await uploadToOSS(fileToUpload)

              console.log(`图片上传完成: ${fileToUpload.name}`)

              // 上传成功，更新图片数据
              setImages((prevImages) => {
                const updatedImages = [...prevImages]
                // 确保索引有效
                if (fileIndex < updatedImages.length) {
                  // 更新当前图片的数据
                  updatedImages[fileIndex] = {
                    ...updatedImages[fileIndex],
                    ossUrl, // 保存 OSS URL
                    uploading: false, // 标记为已完成上传
                  }
                }
                return updatedImages
              })

              // 更新持久化的 OSS URL 数组
              setPersistedOssUrls((prevUrls) => {
                const newUrls = [...prevUrls]
                // 确保数组长度足够
                while (newUrls.length <= fileIndex) {
                  newUrls.push('')
                }
                newUrls[fileIndex] = ossUrl
                return newUrls
              })
            } catch (error) {
              // 上传失败，更新图片数据
              setImages((prevImages) => {
                const updatedImages = [...prevImages]
                // 确保索引有效
                if (fileIndex < updatedImages.length) {
                  // 更新当前图片的数据
                  updatedImages[fileIndex] = {
                    ...updatedImages[fileIndex],
                    uploading: false, // 标记为已完成上传
                    error: 'Upload failed', // 添加错误信息
                  }
                }
                return updatedImages
              })
              console.error(
                `Failed to upload image at index ${fileIndex}:`,
                error
              )
            }
          }

          // 启动上传
          uploadCurrentFile(file, currentIndex)

          // 只对第一张图片设置比例（如果之前没有图片）
          if (images.length === 0 && newImages.length === 1) {
            const { width, height } = await getImageDimensions(filePreview)
            const ratio = width / height

            // Preset ratio actual values
            const ratioValues = {
              '3:2': 1.5,
              '2:3': 0.667,
              '1:1': 1,
            } as const

            // Find closest ratio
            let closestRatio: '3:2' | '2:3' | '1:1' = '1:1'
            let minDiff = Infinity

            Object.entries(ratioValues).forEach(([key, value]) => {
              const diff = Math.abs(ratio - value)
              if (diff < minDiff) {
                minDiff = diff
                closestRatio = key as '3:2' | '2:3' | '1:1'
              }
            })

            // Set form ratio value
            form.setValue('ratio', closestRatio)
          }
        } catch (err) {
          console.error('Error getting image dimensions:', err)
        }
      }

      // 更新图片数据
      setImages(newImages)
    },
    [
      images,
      setImages,
      setPersistedOssUrls,
      setGenerationError,
      form,
      maxUploadImages,
    ]
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/webp': ['.webp'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    // 允许上传多张图片
  })

  // Remove uploaded image
  const removeImage = useCallback(
    (index?: number) => {
      if (typeof index === 'number') {
        // 删除特定图片
        if (images[index]?.previewUrl) {
          // 如果是本地预览 URL（以 blob: 开头），则需要释放
          if (images[index].previewUrl.startsWith('blob:')) {
            URL.revokeObjectURL(images[index].previewUrl)
          }
        }

        // 更新图片数据 - 删除指定索引的图片
        setImages((prevImages) => {
          const newImages = [...prevImages]
          newImages.splice(index, 1)
          return newImages
        })

        // 更新持久化的 OSS URL 数组
        setPersistedOssUrls((prevUrls) => {
          const newUrls = [...prevUrls]
          newUrls.splice(index, 1)
          return newUrls
        })
      } else {
        // 删除所有图片
        images.forEach((image) => {
          // 如果是本地预览 URL（以 blob: 开头），则需要释放
          if (image.previewUrl && image.previewUrl.startsWith('blob:')) {
            URL.revokeObjectURL(image.previewUrl)
          }
        })

        // 清空所有图片数据
        setImages([])
        setPersistedOssUrls([])
      }
    },
    [images, setImages, setPersistedOssUrls]
  )

  // Upload image to OSS (client-side direct upload)
  const uploadToOSS = async (file: File): Promise<string> => {
    try {
      // 使用客户端直接上传
      return await clientDirectUpload(file)
    } catch (error) {
      console.error('Failed to upload to OSS:', error)
      setGenerationError('Failed to upload image, please try again')
      throw new Error('Failed to upload image')
    }
  }

  // 客户端直接上传方式
  const clientDirectUpload = async (file: File): Promise<string> => {
    try {
      console.log('Using client direct upload method')
      // 1. 获取签名 URL
      const signatureResponse = await fetch('/api/upload/get-signature', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: file.name,
          contentType: file.type,
        }),
      })

      if (!signatureResponse.ok) {
        console.error(
          'Failed to get upload signature, status:',
          signatureResponse.status
        )
        throw new Error('Failed to get upload signature')
      }

      const signatureData = await signatureResponse.json()

      if (signatureData.code !== 200) {
        console.error('Signature API returned error:', signatureData.msg)
        throw new Error(signatureData.msg || 'Failed to get upload signature')
      }

      const { url: signedUrl, ossUrl } = signatureData.data

      if (!signedUrl || !ossUrl) {
        console.error('Invalid signature data:', signatureData)
        throw new Error('Invalid signature data received')
      }

      // 2. 使用签名 URL 直接上传文件到 OSS
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      })

      if (!uploadResponse.ok) {
        console.error('Failed to upload to OSS, status:', uploadResponse.status)
        throw new Error('Upload failed')
      }

      // 3. 返回 OSS URL
      return ossUrl
    } catch (error) {
      console.error('Client direct upload failed:', error)
      throw new Error('Client direct upload failed')
    }
  }

  useEffect(() => {
    setGenerationError(null)
  }, [])

  // 监听生成模式变化，切换时清空选中的风格
  useEffect(() => {
    // 当生成模式发生变化时，清空当前选中的风格
    if (selectedStyle) {
      // 检查当前选中的风格是否适用于新的模式
      const isStyleCompatible = selectedStyle.type === generationMode

      if (!isStyleCompatible) {
        // 如果当前风格不适用于新模式，清空选择
        setSelectedStyle(null)
        setIsStyleCancelled(false) // 重置取消状态，允许重新随机选择

        // 清空表单的 prompt
        form.setValue('prompt', '')

        // 清空 formData
        const newFormData = {
          prompt: '',
          ratio: form.getValues('ratio') || '3:2',
          nVariants: form.getValues('nVariants') || '1',
        }
        setFormData(newFormData)
        saveToSessionStorage(STORAGE_KEY_FORM, newFormData)
      }
    }
  }, [
    generationMode,
    selectedStyle,
    setSelectedStyle,
    setIsStyleCancelled,
    form,
    setFormData,
  ])

  // 渲染组件
  return (
    <div className="w-full image-style-converter">
      <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl">
        <div className="h-0.5 w-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500" />
        <div className="p-5">
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Generation Mode Selector */}
            <div className="flex justify-center">
              <div className="inline-flex rounded-xl overflow-hidden border border-gray-200 bg-gray-50/50 p-0.5">
                <button
                  type="button"
                  onClick={() => {
                    setGenerationMode('image-to-image')
                    // 使用 localStorage 保存模式选择
                    saveToLocalStorage(STORAGE_KEY_MODE, 'image-to-image')
                  }}
                  className={`py-2 px-4 md:px-8 lg:px-10 text-[15px] font-medium transition-all duration-300 rounded-lg ${
                    generationMode === 'image-to-image'
                      ? 'bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 text-white shadow-md'
                      : 'text-gray-700 hover:bg-gray-100'
                  } ${isGenerating ? 'cursor-not-allowed opacity-60' : ''}`}
                  disabled={isGenerating}
                >
                  Image to Image
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setGenerationMode('text-to-image')
                    // 使用 localStorage 保存模式选择
                    saveToLocalStorage(STORAGE_KEY_MODE, 'text-to-image')
                  }}
                  className={`py-2 px-4 md:px-8 lg:px-10 text-[15px] font-medium transition-all duration-300 rounded-lg ${
                    generationMode === 'text-to-image'
                      ? 'bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 text-white shadow-md'
                      : 'text-gray-700 hover:bg-gray-100'
                  } ${isGenerating ? 'cursor-not-allowed opacity-60' : ''}`}
                  disabled={isGenerating}
                >
                  Text to Image
                </button>
              </div>
            </div>

            {/* 主要内容区域：左右两栏布局 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
              {/* 左侧：图片上传和风格预览 */}
              <div className="lg:col-span-1 space-y-4">
                {/* Image upload area - only show in image-to-image mode */}
                {generationMode === 'image-to-image' && (
                  <>
                    {images.length === 0 ? (
                      <div
                        {...getRootProps()}
                        className={`border-2 border-dashed rounded-xl p-4 text-center cursor-pointer transition-all duration-300 ${
                          isDragActive
                            ? 'border-blue-500 bg-blue-50/50'
                            : 'border-white/30 hover:border-blue-400/50 hover:bg-white/[0.1] backdrop-blur-xl shadow-[0_5px_30px_rgba(0,0,0,0.05)] backdrop-saturate-150'
                        }`}
                      >
                        <input {...getInputProps()} />
                        <div className="flex flex-col items-center justify-center h-[100px]">
                          <UploadCloud className="h-7 w-7 text-blue-500 mb-2" />
                          <p className="text-sm font-medium text-gray-700">
                            {t('ImageStyleConverter.uploadButton')}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {t('ImageStyleConverter.uploadDesc')}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <p className="text-xs text-gray-500">
                            {images.length}/{maxUploadImages} images
                          </p>
                          <div className="flex gap-3">
                            <button
                              type="button"
                              className={`text-xs px-2 py-1 rounded-lg transition-colors ${
                                images.length >= maxUploadImages
                                  ? 'text-gray-400 cursor-not-allowed'
                                  : 'text-blue-600 hover:text-blue-800 hover:bg-blue-50/50'
                              }`}
                              onClick={() => {
                                if (images.length < maxUploadImages) {
                                  const dropzoneElement =
                                    document.querySelector(
                                      '[data-testid="dropzone"]'
                                    )
                                  if (dropzoneElement) {
                                    // @ts-ignore
                                    dropzoneElement.click()
                                  }
                                }
                              }}
                              disabled={
                                isGenerating || images.length >= maxUploadImages
                              }
                            >
                              Add more
                            </button>
                            <button
                              type="button"
                              className="text-xs px-2 py-1 rounded-lg text-red-600 hover:text-red-800 hover:bg-red-50/50 transition-colors"
                              onClick={() => removeImage()}
                              disabled={isGenerating}
                            >
                              Clear
                            </button>
                          </div>
                        </div>

                        <div className="grid grid-cols-3 gap-2">
                          {images.map((image, index) => (
                            <div
                              key={index}
                              className="relative rounded-xl overflow-hidden border shadow-sm border-white/30 bg-white/10 backdrop-blur-xl backdrop-saturate-150 transition-all duration-300 hover:shadow-md hover:border-white/40"
                            >
                              <div className="relative w-full h-[80px]">
                                <Image
                                  src={image.previewUrl}
                                  alt={`Preview image ${index + 1}`}
                                  fill
                                  className="object-contain"
                                  unoptimized
                                />

                                {image.uploading && (
                                  <div className="absolute inset-0 flex items-center justify-center bg-black/30 backdrop-blur-sm">
                                    <div className="h-6 w-6 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                                  </div>
                                )}

                                {image.error && (
                                  <div className="absolute bottom-1 left-1 bg-red-500 text-white text-[10px] px-1.5 py-0.5 rounded-lg">
                                    Failed
                                  </div>
                                )}
                              </div>
                              <button
                                type="button"
                                className="absolute top-1 right-1 h-5 w-5 rounded-full bg-black/70 flex items-center justify-center text-white hover:bg-black/90 transition-colors"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  removeImage(index)
                                }}
                                disabled={isGenerating}
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </div>
                          ))}

                          {images.length < maxUploadImages && (
                            <div
                              {...getRootProps()}
                              className="relative rounded-xl overflow-hidden border shadow-sm border-white/30 bg-white/10 backdrop-blur-xl backdrop-saturate-150 hover:border-blue-300/70 hover:bg-white/[0.15] cursor-pointer transition-all duration-300 hover:shadow-md group"
                              data-testid="dropzone"
                            >
                              <input {...getInputProps()} />
                              <div className="relative w-full h-[80px] flex items-center justify-center">
                                <div className="flex flex-col items-center justify-center">
                                  <UploadCloud className="h-5 w-5 text-blue-500 group-hover:scale-110 transition-transform" />
                                  <p className="text-[10px] text-gray-500 mt-1 group-hover:text-blue-500 transition-colors">
                                    Add more
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </>
                )}

                {/* Style Selection with Preview */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex gap-2 flex-1">
                      <div className="flex-1">
                        <Select
                          value={selectedStyle?.id?.toString() || ''}
                          onValueChange={(value) => {
                            const style = styles.find(
                              (s) => s.id.toString() === value
                            )
                            if (style) handleStyleSelect(style)
                          }}
                          disabled={isGenerating}
                        >
                          <SelectTrigger className="w-full h-9 text-sm bg-white/10 border-white/30 hover:border-white/40 backdrop-blur-xl backdrop-saturate-150 rounded-xl transition-all duration-300 hover:shadow-sm">
                            <div className="flex items-center gap-2">
                              <Sparkles className="h-4 w-4 text-blue-500" />
                              <SelectValue placeholder="Select style" />
                            </div>
                          </SelectTrigger>
                          <SelectContent className="rounded-xl">
                            {styles.map((style) => (
                              <SelectItem
                                key={style.id}
                                value={style.id.toString()}
                                className="focus:bg-blue-50"
                              >
                                {style.title}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {selectedStyle && (
                        <button
                          type="button"
                          onClick={() => {
                            setSelectedStyle(null)
                            form.setValue('prompt', '')
                            setIsStyleCancelled(true)
                          }}
                          className={`flex items-center justify-center h-9 w-9 rounded-xl border transition-all duration-300 backdrop-blur-sm shadow-sm ${
                            isGenerating
                              ? 'bg-gray-300/20 text-gray-400 border-gray-300/30 cursor-not-allowed opacity-50'
                              : 'bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 hover:from-blue-500/20 hover:via-indigo-500/20 hover:to-purple-500/20 border-blue-200/30 hover:border-blue-300/40 text-blue-600 hover:text-blue-700 hover:shadow'
                          }`}
                          title="Clear style"
                          disabled={isGenerating}
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* 风格预览 - 在两种模式下都显示 */}
                  {selectedStyle &&
                    selectedStyle.ratio &&
                    selectedStyle?.generatedImage && (
                      <div className="w-full">
                        <div className="flex justify-center">
                          <div className="bg-gray-50 rounded-xl overflow-hidden border border-white/30 bg-white/10 backdrop-blur-xl backdrop-saturate-150 shadow-[0_5px_30px_rgba(0,0,0,0.05)] transition-all duration-300 hover:shadow-md">
                            {generationMode === 'image-to-image' ? (
                              // 图生图模式：显示原图和效果图对比
                              <div
                                className="grid grid-cols-2 gap-1.5 p-1"
                                style={{
                                  maxHeight: '120px',
                                }}
                              >
                                <div
                                  className="relative"
                                  style={{
                                    aspectRatio: selectedStyle.ratio.replace(
                                      ':',
                                      '/'
                                    ),
                                    height:
                                      selectedStyle.ratio === '2:3'
                                        ? '120px'
                                        : '80px',
                                    width:
                                      selectedStyle.ratio === '2:3'
                                        ? '80px'
                                        : '120px',
                                  }}
                                >
                                  <Image
                                    src={selectedStyle.originalImage}
                                    alt="Original image"
                                    fill
                                    className="object-contain bg-gray-100 rounded-lg"
                                  />
                                  <div className="absolute bottom-1 left-1 bg-black/50 text-white text-[10px] px-1.5 py-0.5 rounded-lg">
                                    Original
                                  </div>
                                </div>
                                <div
                                  className="relative"
                                  style={{
                                    aspectRatio: selectedStyle.ratio.replace(
                                      ':',
                                      '/'
                                    ),
                                    height:
                                      selectedStyle.ratio === '2:3'
                                        ? '120px'
                                        : '80px',
                                    width:
                                      selectedStyle.ratio === '2:3'
                                        ? '80px'
                                        : '120px',
                                  }}
                                >
                                  <Image
                                    src={selectedStyle.generatedImage}
                                    alt="Generated effect"
                                    fill
                                    className="object-contain bg-gray-100 rounded-lg"
                                  />
                                  <div className="absolute bottom-1 left-1 bg-black/50 text-white text-[10px] px-1.5 py-0.5 rounded-lg">
                                    Effect
                                  </div>
                                </div>
                              </div>
                            ) : (
                              // 文生图模式：只显示生成的效果图
                              <div className="p-1">
                                <div
                                  className="relative"
                                  style={{
                                    aspectRatio: selectedStyle.ratio.replace(
                                      ':',
                                      '/'
                                    ),
                                    height:
                                      selectedStyle.ratio === '2:3'
                                        ? '120px'
                                        : '80px',
                                    width:
                                      selectedStyle.ratio === '2:3'
                                        ? '80px'
                                        : '120px',
                                  }}
                                >
                                  <Image
                                    src={selectedStyle.generatedImage}
                                    alt="Style preview"
                                    fill
                                    className="object-contain bg-gray-100 rounded-lg"
                                  />
                                  <div className="absolute bottom-1 left-1 bg-black/50 text-white text-[10px] px-1.5 py-0.5 rounded-lg">
                                    Preview
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                </div>

                {/* Output settings */}
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Select
                      value={form.watch('ratio')}
                      onValueChange={(value) =>
                        form.setValue('ratio', value as '3:2' | '2:3' | '1:1')
                      }
                      disabled={isGenerating}
                    >
                      <SelectTrigger className="w-full h-9 text-sm bg-white/10 border-white/30 hover:border-white/40 backdrop-blur-xl backdrop-saturate-150 rounded-xl transition-all duration-300 hover:shadow-sm">
                        <div className="flex items-center gap-2">
                          <SelectValue placeholder="Select ratio" />
                        </div>
                      </SelectTrigger>
                      <SelectContent className="rounded-xl">
                        <SelectItem value="3:2" className="focus:bg-blue-50">
                          <div className="flex items-center gap-2">
                            <div className="relative w-5 h-5 flex items-center justify-center">
                              <svg
                                viewBox="0 0 24 24"
                                className="w-full h-full stroke-blue-500"
                                fill="none"
                                strokeWidth="1.5"
                              >
                                <rect
                                  x="4"
                                  y="8"
                                  width="16"
                                  height="8"
                                  rx="1"
                                />
                              </svg>
                            </div>
                            <span>Landscape 3:2</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="1:1" className="focus:bg-blue-50">
                          <div className="flex items-center gap-2">
                            <div className="relative w-5 h-5 flex items-center justify-center">
                              <svg
                                viewBox="0 0 24 24"
                                className="w-full h-full stroke-blue-500"
                                fill="none"
                                strokeWidth="1.5"
                              >
                                <rect
                                  x="6"
                                  y="6"
                                  width="12"
                                  height="12"
                                  rx="1"
                                />
                              </svg>
                            </div>
                            <span>Square 1:1</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="2:3" className="focus:bg-blue-50">
                          <div className="flex items-center gap-2">
                            <div className="relative w-5 h-5 flex items-center justify-center">
                              <svg
                                viewBox="0 0 24 24"
                                className="w-full h-full stroke-blue-500"
                                fill="none"
                                strokeWidth="1.5"
                              >
                                <rect
                                  x="8"
                                  y="4"
                                  width="8"
                                  height="16"
                                  rx="1"
                                />
                              </svg>
                            </div>
                            <span>Portrait 2:3</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center">
                    <Select
                      value={form.watch('nVariants')}
                      onValueChange={(value) =>
                        form.setValue('nVariants', value as '1' | '2' | '4')
                      }
                      disabled={isGenerating}
                    >
                      <SelectTrigger className="w-full h-9 text-sm bg-white/10 border-white/30 hover:border-white/40 backdrop-blur-xl backdrop-saturate-150 rounded-xl transition-all duration-300 hover:shadow-sm">
                        <div className="flex items-center gap-2">
                          <ImageIcon className="h-4 w-4 text-blue-500" />
                          <SelectValue placeholder="Select count" />
                        </div>
                      </SelectTrigger>
                      <SelectContent className="rounded-xl">
                        <SelectItem value="1" className="focus:bg-blue-50">
                          Generate 1 image
                        </SelectItem>
                        <SelectItem value="2" className="focus:bg-blue-50">
                          Generate 2 images
                        </SelectItem>
                        <SelectItem value="4" className="focus:bg-blue-50">
                          Generate 4 images
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* 右侧：提示词输入区域 */}
              <div className="lg:col-span-1">
                {/* Prompt input */}
                <div className="relative">
                  <textarea
                    {...form.register('prompt')}
                    placeholder={t('ImageStyleConverter.conversionPromptDesc')}
                    className={`w-full min-h-[140px] resize-y rounded-xl border px-3.5 py-2.5 shadow-sm focus:outline-none focus:ring-2 bg-white/10 hover:bg-white/15 backdrop-blur-xl text-sm placeholder:text-gray-500/80 ${
                      generationMode === 'image-to-image'
                        ? 'lg:min-h-[366px]'
                        : 'lg:min-h-[240px]'
                    } ${
                      hasAttemptedSubmit &&
                      (form.formState.errors.prompt ||
                        (!form.watch('prompt')?.trim() &&
                          form.formState.touchedFields.prompt))
                        ? 'border-red-300/40 focus:border-red-500/40 focus:ring-red-500/20'
                        : 'border-white/30 hover:border-white/40 focus:border-blue-500/40 focus:ring-blue-500/20'
                    } ${
                      isGenerating
                        ? 'opacity-70 text-gray-400 cursor-not-allowed placeholder:text-gray-400/50 bg-gray-100/10 hover:bg-gray-100/10'
                        : ''
                    } shadow-[0_5px_30px_rgba(0,0,0,0.05)] backdrop-saturate-150 backdrop-filter transition-all duration-300`}
                    disabled={isGenerating}
                  />
                  {/* Random style button */}
                  <button
                    type="button"
                    onClick={handleRandomStyle}
                    className={`absolute bottom-3 right-3 flex items-center gap-1.5 px-3.5 py-1.5 text-xs font-medium rounded-full border transition-all duration-300 shadow-sm backdrop-blur-md group ${
                      isGenerating
                        ? 'bg-gray-300/20 text-gray-400 border-gray-300/30 cursor-not-allowed opacity-50'
                        : 'bg-gradient-to-r from-blue-500/15 via-indigo-500/15 to-purple-500/15 hover:from-blue-500/25 hover:via-indigo-500/25 hover:to-purple-500/25 text-blue-600 hover:text-blue-700 border-white/40 hover:border-white/50 hover:shadow'
                    }`}
                    disabled={isGenerating}
                    title="Random Style"
                  >
                    <Shuffle
                      className={`h-3.5 w-3.5 ${
                        isGenerating
                          ? ''
                          : 'group-hover:rotate-180 transition-transform duration-500'
                      }`}
                    />
                    <span className="hidden sm:inline">Random Style</span>
                  </button>
                </div>

                {hasAttemptedSubmit &&
                  (form.formState.errors.prompt ||
                    !form.watch('prompt')?.trim()) && (
                    <p className="text-xs text-red-600 mt-2">
                      Please enter a prompt to start generating
                    </p>
                  )}
              </div>
            </div>

            {/* Generate button - Centered at the bottom */}
            <div className="flex flex-col items-center mt-7 w-full">
              {generationError && (
                <div className="mb-4 w-full bg-red-50/80 backdrop-blur-sm p-3 rounded-xl flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <p className="text-xs leading-5 text-red-600">
                    {generationError}
                  </p>
                </div>
              )}
              <div className="w-full relative">
                <Button
                  type="submit"
                  className={`relative w-full max-w-full h-12 text-[15px] font-medium rounded-xl transition-all duration-300 ${
                    isFormValid
                      ? 'bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl hover:-translate-y-0.5'
                      : 'bg-gradient-to-r from-blue-500/30 via-indigo-500/30 to-purple-500/30 text-white/50 cursor-not-allowed'
                  }`}
                  disabled={!isFormValid || isGenerating}
                >
                  <div
                    className={`absolute inset-0 rounded-xl bg-white/10 opacity-0 ${
                      isFormValid ? 'hover:opacity-100' : ''
                    } transition-opacity duration-300`}
                  />
                  {isGenerating ? (
                    <div className="flex items-center justify-center gap-2.5">
                      <div className="w-5 h-5 relative">
                        <div className="absolute inset-0 rounded-full border-2 border-current border-r-transparent animate-spin" />
                      </div>
                      <span>{t('ImageStyleConverter.generating')}</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2.5">
                      <Sparkles
                        className={`h-5 w-5 ${
                          !isFormValid ? 'opacity-50' : ''
                        }`}
                      />
                      <span>{t('ImageStyleConverter.generateButton')}</span>
                    </div>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>

      {showLoginModal && (
        <ShowLoginModal
          title={t('loginTipsTitle')}
          desc={t('tipLogin')}
          onClose={() => setShowLoginModal(false)}
        />
      )}
    </div>
  )
}
