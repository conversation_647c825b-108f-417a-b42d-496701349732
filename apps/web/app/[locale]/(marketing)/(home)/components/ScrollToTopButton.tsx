'use client'

import React, { useState, useEffect } from 'react'
import { ArrowUp } from 'lucide-react'

// 简单的全局状态管理
let mobileMenuListeners: ((isOpen: boolean) => void)[] = []

const subscribeToMobileMenu = (listener: (isOpen: boolean) => void) => {
  mobileMenuListeners.push(listener)
  return () => {
    mobileMenuListeners = mobileMenuListeners.filter((l) => l !== listener)
  }
}

// 暴露给全局使用
if (typeof window !== 'undefined') {
  ;(window as any).setMobileMenuOpen = (isOpen: boolean) => {
    mobileMenuListeners.forEach((listener) => listener(isOpen))
  }
}

const ScrollToTopButton = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const toggleVisibility = () => {
    if (window.scrollY > 300) {
      setIsVisible(true)
    } else {
      setIsVisible(false)
    }
  }

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }

  useEffect(() => {
    window.addEventListener('scroll', toggleVisibility)

    // 订阅移动端菜单状态
    const unsubscribe = subscribeToMobileMenu((isOpen: boolean) => {
      setIsMobileMenuOpen(isOpen)
    })

    return () => {
      window.removeEventListener('scroll', toggleVisibility)
      unsubscribe()
    }
  }, [])

  // 如果移动端菜单打开，不显示按钮
  if (isMobileMenuOpen) {
    return null
  }

  return (
    <>
      <button
        type="button"
        onClick={scrollToTop}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={`fixed bottom-8 right-8 z-50 group transition-all duration-500 ease-out ${
          isVisible
            ? 'opacity-100 translate-y-0'
            : 'opacity-0 translate-y-10 pointer-events-none'
        }`}
        aria-label="Scroll to top"
      >
        {/* 外层渐变边框容器 */}
        <div className="relative">
          {/* 动态渐变边框 */}
          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-fuchsia-600 via-purple-500 to-indigo-500 p-[2px] animate-gradient-flow shadow-lg">
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-fuchsia-600 via-purple-500 to-indigo-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
          </div>

          {/* 内层按钮 */}
          <div className="relative bg-gradient-to-r from-gray-900/95 to-gray-800/95 rounded-full p-3 shadow-2xl border border-white/20">
            {/* 悬浮时的光晕效果 */}
            <div
              className={`absolute inset-0 rounded-full bg-gradient-to-r from-fuchsia-500/20 via-purple-500/20 to-indigo-500/20 transition-all duration-300 ${
                isHovered ? 'opacity-100 scale-110' : 'opacity-0 scale-100'
              }`}
            ></div>

            {/* 图标容器 */}
            <div className="relative z-10">
              <ArrowUp
                className={`h-6 w-6 text-white transition-all duration-300 ${
                  isHovered ? 'transform -translate-y-1 scale-110' : ''
                }`}
              />
            </div>
          </div>

          {/* 粒子效果 */}
          <div className="absolute inset-0 rounded-full pointer-events-none">
            <div
              className={`absolute top-1/2 left-1/2 w-1 h-1 bg-fuchsia-400 rounded-full transition-all duration-500 ${
                isHovered
                  ? 'opacity-100 scale-100 translate-x-8 -translate-y-8'
                  : 'opacity-0 scale-0 translate-x-0 -translate-y-0'
              }`}
              style={{ animationDelay: '0ms' }}
            ></div>
            <div
              className={`absolute top-1/2 left-1/2 w-1 h-1 bg-purple-400 rounded-full transition-all duration-500 ${
                isHovered
                  ? 'opacity-100 scale-100 translate-x-6 translate-y-8'
                  : 'opacity-0 scale-0 translate-x-0 translate-y-0'
              }`}
              style={{ animationDelay: '100ms' }}
            ></div>
            <div
              className={`absolute top-1/2 left-1/2 w-1 h-1 bg-indigo-400 rounded-full transition-all duration-500 ${
                isHovered
                  ? 'opacity-100 scale-100 -translate-x-8 -translate-y-6'
                  : 'opacity-0 scale-0 translate-x-0 translate-y-0'
              }`}
              style={{ animationDelay: '200ms' }}
            ></div>
            <div
              className={`absolute top-1/2 left-1/2 w-1 h-1 bg-pink-400 rounded-full transition-all duration-500 ${
                isHovered
                  ? 'opacity-100 scale-100 -translate-x-6 translate-y-6'
                  : 'opacity-0 scale-0 translate-x-0 translate-y-0'
              }`}
              style={{ animationDelay: '300ms' }}
            ></div>
          </div>
        </div>
      </button>

      <style jsx>{`
        @keyframes gradient-flow {
          0% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
          100% {
            background-position: 0% 50%;
          }
        }

        .animate-gradient-flow {
          background-size: 200% 200%;
          animation: gradient-flow 3s ease infinite;
          filter: drop-shadow(0 0 8px rgba(236, 72, 153, 0.4));
        }

        /* 添加脉冲效果 */
        @keyframes pulse-glow {
          0%,
          100% {
            box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
          }
          50% {
            box-shadow: 0 0 30px rgba(236, 72, 153, 0.6),
              0 0 40px rgba(139, 92, 246, 0.4);
          }
        }

        .group:hover .animate-gradient-flow {
          animation: gradient-flow 1.5s ease infinite,
            pulse-glow 2s ease-in-out infinite;
          filter: drop-shadow(0 0 12px rgba(236, 72, 153, 0.6))
            drop-shadow(0 0 8px rgba(139, 92, 246, 0.4));
        }
      `}</style>
    </>
  )
}

export default ScrollToTopButton
