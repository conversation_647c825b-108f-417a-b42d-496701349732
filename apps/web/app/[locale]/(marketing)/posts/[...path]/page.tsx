import { PostContent } from '@marketing/blog/components/PostContent'
import { allPosts } from 'content-collections'
import { getLocale } from 'next-intl/server'
import { notFound } from 'next/navigation'

export async function generateStaticParams() {
  return allPosts.map((post) => ({
    path: post.path.split('/'),
  }))
}

export async function generateMetadata({
  params,
}: {
  params: { path: string[] }
}) {
  const locale = await getLocale()
  const post = allPosts.find(
    (post) =>
      post.path === params.path.join('/') &&
      post.locale === locale &&
      post.published
  )

  if (!post) {
    return {}
  }

  return {
    title: post.title,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      images: post.image ? [post.image] : [],
    },
  }
}

export default async function PostPage({
  params,
}: {
  params: { path: string[] }
}) {
  const locale = await getLocale()
  const post = allPosts.find(
    (post) =>
      post.path === params.path.join('/') &&
      post.locale === locale &&
      post.published
  )

  if (!post) {
    notFound()
  }

  return <PostContent post={post} />
}
