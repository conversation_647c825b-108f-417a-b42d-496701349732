'use client'

import React, { useState, useCallback, useRef, useMemo } from 'react'
import { ImageUploadWithCrop } from '../../components/ui/ImageUploadWithCrop'
import { AspectRatioSelector } from '../../components/ui/AspectRatioSelector'
import { VideoSettingsPanel } from '../../components/ui/VideoSettingsPanel'
import { SmileTypeSelector } from './SmileTypeSelector'
import { AdvancedSettings } from './AdvancedSettings'
import { Button } from '@ui/components/button'
import { RotateCcw, Info } from 'lucide-react'
import {
  AI_SMILE_CONFIG,
  type SmileType,
  getCompatibleOptions,
} from '../config'
import { SettingsSummary } from './SettingsSummary'
import { GenerateButton } from '../../face-swap/components/GenerateButton'
import { useToast } from '@ui/hooks/use-toast'
import { AiSmileTipsPopover } from './AiSmileTipsPopover'

interface AiSmileSidebarProps {
  onGenerate: (params: GenerationParams) => void
  isGenerating?: boolean
  onReset?: () => void
  className?: string
}

export interface GenerationParams {
  originalFile: File
  croppedBlob: Blob
  aspectRatio: string
  quality: string
  duration: number
  prompt: string
  uploadedImageUrl: string
}

export const AiSmileSidebar = ({
  onGenerate,
  isGenerating = false,
  onReset,
  className = '',
}: AiSmileSidebarProps) => {
  const { toast } = useToast()
  const imageUploadRef = useRef<{ getCroppedBlob: () => Promise<Blob | null> }>(
    null
  )

  // 状态管理 - 清晰的流程状态
  const [originalFile, setOriginalFile] = useState<File | null>(null)
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadError, setUploadError] = useState<string | null>(null)

  // 视频设置状态
  const [aspectRatio, setAspectRatio] = useState<string>(
    AI_SMILE_CONFIG.videoSettings.defaults.aspectRatio
  )
  const [quality, setQuality] = useState<string>(
    AI_SMILE_CONFIG.videoSettings.defaults.quality
  )
  const [duration, setDuration] = useState<number>(
    AI_SMILE_CONFIG.videoSettings.defaults.duration
  )

  // 处理时长变更，包含兼容性检查
  const handleDurationChange = useCallback(
    (newDuration: number) => {
      setDuration(newDuration)

      // 如果选择8秒且当前画质是1080p，自动切换到720p
      if (newDuration === 8 && quality === '1080p') {
        setQuality('720p')
      }
    },
    [quality]
  )

  // 处理画质变更，包含兼容性检查
  const handleQualityChange = useCallback(
    (newQuality: string) => {
      setQuality(newQuality)

      // 如果选择1080p且当前时长是8秒，自动切换到5秒
      if (newQuality === '1080p' && duration === 8) {
        setDuration(5)
      }
    },
    [duration]
  )

  // 微笑类型和提示词状态
  const [selectedSmileType, setSelectedSmileType] = useState<SmileType | null>(
    null
  )
  const [customPrompt, setCustomPrompt] = useState('')

  // 计算兼容选项
  const { compatibleQualities, compatibleDurations } = useMemo(() => {
    return getCompatibleOptions(duration, quality)
  }, [duration, quality])

  // 生成唯一文件名的函数
  const generateUniqueFileName = useCallback((blob: Blob): string => {
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)

    // 根据blob的type确定扩展名
    let extension = 'jpg' // 默认扩展名
    if (blob.type === 'image/png') {
      extension = 'png'
    } else if (blob.type === 'image/webp') {
      extension = 'webp'
    } else if (blob.type === 'image/jpeg') {
      extension = 'jpg'
    }

    return `ai-smile-${timestamp}-${randomStr}.${extension}`
  }, [])

  // 上传图片到OSS的函数
  const uploadImageToOSS = useCallback(
    async (blob: Blob): Promise<string> => {
      try {
        setIsUploading(true)
        setUploadError(null)

        // 生成唯一文件名
        const fileName = generateUniqueFileName(blob)

        // 创建FormData
        const formData = new FormData()
        formData.append('file', blob, fileName)

        // 调用上传API
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          throw new Error(`上传失败: ${response.status}`)
        }

        const result = await response.json()

        if (result.code !== 200) {
          throw new Error(result.msg || '上传失败')
        }

        return result.data.url
      } catch (error) {
        console.error('图片上传失败:', error)
        const errorMessage = error instanceof Error ? error.message : '上传失败'
        setUploadError(errorMessage)
        toast({
          title: '上传失败',
          description: errorMessage,
          variant: 'error',
        })
        throw error
      } finally {
        setIsUploading(false)
        setUploadProgress(0)
      }
    },
    [toast, generateUniqueFileName]
  )

  // 处理图片选择 - 第一步：用户选择文件
  const handleImageChange = useCallback(
    async (file: File, blob: Blob | null) => {
      setOriginalFile(file)

      if (blob) {
        // 创建预览URL（用于显示，不是真实裁剪）
        const url = URL.createObjectURL(blob)
        setImageUrl(url)

        // 清除之前的错误状态
        setUploadError(null)
      }
    },
    []
  )

  // 处理图片移除
  const handleImageRemove = useCallback(() => {
    setOriginalFile(null)
    setUploadError(null)
    if (imageUrl) {
      URL.revokeObjectURL(imageUrl)
      setImageUrl(null)
    }
  }, [imageUrl])

  // 处理微笑类型选择
  const handleSmileTypeSelect = useCallback((smileType: SmileType) => {
    setSelectedSmileType(smileType)
    // 如果提示词为空，自动填充微笑类型的提示词
    setCustomPrompt(smileType.prompt)
  }, [])

  const handleSmileTypeClear = useCallback(() => {
    setSelectedSmileType(null)
    setCustomPrompt('')
  }, [])

  // 检查是否可以生成
  const canGenerate =
    originalFile && !isGenerating && !isUploading && !uploadError

  // 处理生成按钮点击 - 第二步：点击生成按钮，第三步：真实裁剪，第四步：上传到服务器，第五步：生成视频
  const handleGenerate = useCallback(async () => {
    if (!canGenerate) {
      toast({
        title: 'Cannot generate',
        description: 'Please ensure image is uploaded',
        variant: 'error',
      })
      return
    }

    if (!imageUploadRef.current) {
      toast({
        title: 'Cannot generate',
        description: 'Image upload component not initialized',
        variant: 'error',
      })
      return
    }

    try {
      // 第三步：真实裁剪
      const croppedBlob = await imageUploadRef.current.getCroppedBlob()
      if (!croppedBlob) {
        toast({
          title: 'Cannot generate',
          description: 'Please crop the image first',
          variant: 'error',
        })
        return
      }

      // 第四步：上传到服务器
      const ossUrl = await uploadImageToOSS(croppedBlob)

      // 第五步：生成视频
      const params = getGenerationParams(croppedBlob, ossUrl)

      if (params) {
        onGenerate(params)
      }
    } catch (error) {
      // 错误已在uploadImageToOSS中处理
      console.error('生成失败:', error)
    }
  }, [canGenerate, uploadImageToOSS, onGenerate, toast])

  function getGenerationParams(
    croppedBlob: Blob,
    ossUrl: string
  ): GenerationParams | null {
    if (!originalFile || !ossUrl) return null

    // 使用选择的微笑类型提示词，如果没有则使用默认提示词
    const finalPrompt =
      customPrompt.trim() ||
      (selectedSmileType
        ? selectedSmileType.prompt
        : 'Create a natural, warm smile video')

    return {
      originalFile,
      croppedBlob,
      aspectRatio,
      quality,
      duration,
      prompt: finalPrompt,
      uploadedImageUrl: ossUrl,
    }
  }

  return (
    <>
      {/* 标题栏 - 固定高度 */}
      <div className="p-4 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center justify-between">
          <h2 className="text-md font-semibold text-gray-900">
            AI Smile Video Generator
          </h2>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onReset}
              disabled={isGenerating}
              className="flex items-center justify-center gap-2 bg-[#4B6BFB] hover:bg-[#4B6BFB]/80 px-2 py-1 text-white font-semibold text-sm rounded-md transition-all duration-200"
            >
              <RotateCcw className="w-4 h-4" />
              <span>reset all</span>
            </Button>

            <AiSmileTipsPopover>
              <button
                className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
                title="Upload Tips"
              >
                <Info className="h-4 w-4 text-gray-600" />
              </button>
            </AiSmileTipsPopover>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto min-h-0">
        {/* 内容区域 */}
        <div className="p-4 space-y-4">
          {/* 图片上传区域 */}
          <div>
            <ImageUploadWithCrop
              ref={imageUploadRef}
              imageUrl={imageUrl}
              isUploading={isUploading}
              progress={uploadProgress}
              onImageChange={handleImageChange}
              onRemove={handleImageRemove}
              aspectRatio={aspectRatio}
              disabled={isGenerating}
              maxFileSize={AI_SMILE_CONFIG.upload.maxFileSize}
              allowedTypes={[...AI_SMILE_CONFIG.upload.allowedTypes]}
            />
          </div>

          {/* 分割线 */}
          <hr className="border-gray-200 my-2" />

          {/* 比例选择器 */}
          <div>
            <AspectRatioSelector
              selectedRatio={aspectRatio}
              onRatioChange={setAspectRatio}
              disabled={isGenerating}
            />
          </div>

          {/* 分割线 */}
          {/* <hr className="border-gray-200 my-2" /> */}

          {/* 微笑类型选择器 */}
          {/* <div>
            <SmileTypeSelector
              selectedSmileType={selectedSmileType}
              onSmileTypeSelect={handleSmileTypeSelect}
              onSmileTypeClear={handleSmileTypeClear}
              isGenerating={isGenerating}
            />
          </div> */}

          {/* 分割线 */}
          {/* <hr className="border-gray-200 my-2" /> */}

          {/* 自定义提示词编辑器 */}
          {/* <div>
            <div className="space-y-2">
              <h3 className="text-sm font-semibold text-gray-900">Prompt</h3>
              <textarea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                placeholder="输入自定义提示词，或选择微笑类型自动填充..."
                disabled={isGenerating}
                className="w-full h-20 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
              />
            </div>
          </div> */}

          {/* 分割线 */}
          <hr className="border-gray-200 my-2" />

          {/* 视频设置面板 */}
          <div>
            <VideoSettingsPanel
              quality={quality}
              duration={duration}
              onQualityChange={handleQualityChange}
              onDurationChange={handleDurationChange}
              disabled={isGenerating}
              compatibleQualities={compatibleQualities}
              compatibleDurations={compatibleDurations}
            />
          </div>
        </div>
      </div>

      {/* Fixed Generate Button - 固定底部 */}
      <div className="flex-shrink-0 border-t border-gray-200">
        {/* Settings Summary - Fixed above Generate Button */}
        <div className="bg-gray-50 px-4 py-2 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-xs font-medium text-gray-700">
              Video Settings
            </span>
            <SettingsSummary
              settings={{
                duration,
                quality,
                aspectRatio,
              }}
            />
          </div>
        </div>

        <GenerateButton
          swapImageUrl={imageUrl}
          requireTwoImages={false}
          isGenerating={isGenerating || isUploading}
          onGenerate={handleGenerate}
          generateError={uploadError}
          pointParams={{
            duration,
            quality,
            aspectRatio,
          }}
        />
      </div>
    </>
  )
}

AiSmileSidebar.displayName = 'AiSmileSidebar'
