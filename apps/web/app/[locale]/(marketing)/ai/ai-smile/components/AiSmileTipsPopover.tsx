'use client'

import React from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@ui/components/dropdown-menu'
import { Info } from 'lucide-react'
import { AI_SMILE_CONFIG } from '../config'

interface AiSmileTipsPopoverProps {
  children: React.ReactNode
}

export function AiSmileTipsPopover({ children }: AiSmileTipsPopoverProps) {
  const { uploadGuide } = AI_SMILE_CONFIG

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent
        side="right"
        align="start"
        className="w-[400px] bg-white p-0"
        sideOffset={8}
      >
        <div className="p-4">
          <div className="space-y-4">
            {/* Upload Guidelines Section */}
            <div className="border rounded-lg overflow-hidden">
              {/* Header */}
              <div className="flex items-center gap-3 bg-purple-50 p-4 border-b">
                <div className="flex items-center justify-center w-6 h-6 bg-purple-500 rounded-full flex-shrink-0">
                  <Info className="w-4 h-4 text-white" />
                </div>
                <div className="text-center">
                  <h3 className="text-base font-semibold text-purple-700">
                    {uploadGuide.title}
                  </h3>
                  <p className="text-sm text-purple-600 mt-1">
                    {uploadGuide.subtitle}
                  </p>
                </div>
              </div>

              <div className="p-4">
                {/* Requirements Grid */}
                <div className="grid grid-cols-1 gap-3">
                  {uploadGuide.requirements.map((requirement, index) => (
                    <div
                      key={index}
                      className="flex items-start space-x-3 bg-purple-50/50 rounded-lg p-3"
                    >
                      <span className="text-lg flex-shrink-0 mt-0.5">
                        {requirement.icon}
                      </span>
                      <div className="min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 mb-1">
                          {requirement.title}
                        </h4>
                        <p className="text-sm text-gray-600 leading-tight">
                          {requirement.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
