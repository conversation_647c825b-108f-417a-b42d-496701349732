'use client'

import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>rk<PERSON> } from 'lucide-react'
import { VideoSettings } from './AdvancedSettings'

type SettingsSummaryProps = {
  settings: VideoSettings
}

export function SettingsSummary({ settings }: SettingsSummaryProps) {
  return (
    <div className="flex items-center gap-3 text-xs">
      {/* Duration */}
      <div className="flex items-center gap-1 text-gray-600">
        <Clock className="h-3 w-3" />
        <span className="font-medium">{settings.duration}s</span>
      </div>

      {/* Quality */}
      <div className="flex items-center gap-1 text-gray-600">
        <Zap className="h-3 w-3" />
        <span className="font-medium">{settings.quality}</span>
      </div>

      {/* Aspect Ratio */}
      <div className="flex items-center gap-1 text-gray-600">
        <Ratio className="h-3 w-3" />
        <span className="font-medium">{settings.aspectRatio}</span>
      </div>
    </div>
  )
}
