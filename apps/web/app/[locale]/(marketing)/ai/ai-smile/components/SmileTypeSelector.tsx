'use client'

import React from 'react'
import { Card, CardContent } from '@ui/components/card'
import { Badge } from '@ui/components/badge'
import { Button } from '@ui/components/button'
import { X } from 'lucide-react'
import { SmileType } from '../config'

interface SmileTypeSelectorProps {
  selectedSmileType: SmileType | null
  onSmileTypeSelect: (smileType: SmileType) => void
  onSmileTypeClear: () => void
  isGenerating?: boolean
}

export const SmileTypeSelector = ({
  selectedSmileType,
  onSmileTypeSelect,
  onSmileTypeClear,
  isGenerating = false,
}: SmileTypeSelectorProps) => {
  // 微笑类型配置
  const smileTypes: SmileType[] = [
    {
      id: 'natural',
      name: 'Natural Smile',
      description: '自然、温和的微笑，适合日常使用',
      imageUrl: '/images/ai-smile/natural-smile.png',
      prompt: 'Create a natural, gentle smile that looks authentic and warm',
      tags: ['natural', 'gentle', 'warm'],
      badge: 'Popular',
    },
    {
      id: 'bright',
      name: 'Bright Smile',
      description: '明亮、灿烂的微笑，充满活力',
      imageUrl: '/images/ai-smile/bright-smile.png',
      prompt:
        'Generate a bright, radiant smile with sparkling eyes and positive energy',
      tags: ['bright', 'radiant', 'energetic'],
      badge: 'Trending',
    },
    {
      id: 'gentle',
      name: 'Gentle Smile',
      description: '温柔、含蓄的微笑，优雅自然',
      imageUrl: '/images/ai-smile/gentle-smile.png',
      prompt: 'Create a gentle, subtle smile with soft facial expressions',
      tags: ['gentle', 'subtle', 'elegant'],
    },
    {
      id: 'confident',
      name: 'Confident Smile',
      description: '自信、大方的微笑，展现魅力',
      imageUrl: '/images/ai-smile/confident-smile.png',
      prompt:
        'Generate a confident, charismatic smile that shows self-assurance',
      tags: ['confident', 'charismatic', 'assured'],
    },
    {
      id: 'playful',
      name: 'Playful Smile',
      description: '俏皮、可爱的微笑，充满趣味',
      imageUrl: '/images/ai-smile/playful-smile.png',
      prompt:
        'Create a playful, cute smile with a mischievous twinkle in the eyes',
      tags: ['playful', 'cute', 'mischievous'],
    },
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-gray-900">选择微笑类型</h3>
        {selectedSmileType && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onSmileTypeClear}
            disabled={isGenerating}
            className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
          >
            <X className="h-3 w-3 mr-1" />
            清除
          </Button>
        )}
      </div>

      {selectedSmileType ? (
        // 显示已选择的微笑类型
        <Card className="border-2 border-purple-200 bg-purple-50">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                <img
                  src={selectedSmileType.imageUrl}
                  alt={selectedSmileType.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <h4 className="text-sm font-semibold text-gray-900 truncate">
                    {selectedSmileType.name}
                  </h4>
                  {selectedSmileType.badge && (
                    <Badge className="text-xs bg-gray-100 text-gray-700">
                      {selectedSmileType.badge}
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-gray-600 mb-2">
                  {selectedSmileType.description}
                </p>
                <div className="flex flex-wrap gap-1">
                  {selectedSmileType.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-block px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        // 显示微笑类型选择网格
        <div className="grid grid-cols-2 gap-3">
          {smileTypes.map((smileType) => (
            <Card
              key={smileType.id}
              className="cursor-pointer hover:shadow-md transition-all duration-200 border border-gray-200 hover:border-purple-300"
              onClick={() => !isGenerating && onSmileTypeSelect(smileType)}
            >
              <CardContent className="p-3">
                <div className="space-y-2">
                  <div className="relative">
                    <div className="w-full h-20 rounded-lg overflow-hidden bg-gray-100">
                      <img
                        src={smileType.imageUrl}
                        alt={smileType.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {smileType.badge && (
                      <Badge className="absolute top-1 right-1 text-xs bg-gray-100 text-gray-700">
                        {smileType.badge}
                      </Badge>
                    )}
                  </div>
                  <div>
                    <h4 className="text-xs font-semibold text-gray-900 truncate">
                      {smileType.name}
                    </h4>
                    <p className="text-xs text-gray-600 line-clamp-2">
                      {smileType.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

SmileTypeSelector.displayName = 'SmileTypeSelector'
