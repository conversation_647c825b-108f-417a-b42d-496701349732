'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
  ChevronDown,
  ChevronUp,
  Settings,
  Info,
  AlertTriangle,
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  AI_SMILE_CONFIG,
  getCompatibleOptions,
  validateVideoSettings,
} from '../config'
import { SettingsPresets } from './SettingsPresets'

export interface VideoSettings {
  duration: number
  quality: string
  aspectRatio: string
}

interface AdvancedSettingsProps {
  settings: VideoSettings
  onSettingsChange: (settings: VideoSettings) => void
  className?: string
}

export function AdvancedSettings({
  settings,
  onSettingsChange,
  className = '',
}: AdvancedSettingsProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [validation, setValidation] = useState<{
    isValid: boolean
    errors: string[]
  }>({
    isValid: true,
    errors: [],
  })

  const containerRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  const { compatibleQualities, compatibleDurations } = getCompatibleOptions(
    settings.duration,
    settings.quality
  )

  // Validate settings whenever they change
  useEffect(() => {
    const result = validateVideoSettings(
      settings.duration,
      settings.quality,
      settings.aspectRatio
    )
    setValidation(result)
  }, [settings])

  const handleDurationChange = (duration: number) => {
    let newSettings = { ...settings, duration }

    // Auto-adjust quality if incompatible
    if (duration === 8 && settings.quality === '1080p') {
      newSettings.quality = '720p'
    }

    onSettingsChange(newSettings)
  }

  const handleQualityChange = (quality: string) => {
    let newSettings = { ...settings, quality }

    // Auto-adjust duration if incompatible
    if (quality === '1080p' && settings.duration === 8) {
      newSettings.duration = 5
    }

    onSettingsChange(newSettings)
  }

  const handleAspectRatioChange = (aspectRatio: string) => {
    onSettingsChange({ ...settings, aspectRatio })
  }

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded)

    // Auto-scroll to expanded content after animation completes
    if (!isExpanded) {
      setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
          })
        }
      }, 350) // Delay to match animation duration
    }
  }

  return (
    <div
      ref={containerRef}
      className={`bg-white rounded-lg border border-gray-200 ${className}`}
    >
      {/* Header */}
      <button
        onClick={handleToggleExpand}
        className="w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors duration-200"
      >
        <div className="flex items-center space-x-2">
          <Settings className="w-5 h-5 text-gray-600" />
          <span className="font-medium text-gray-900">Advanced Settings</span>
          {!validation.isValid && (
            <AlertTriangle className="w-4 h-4 text-amber-500" />
          )}
        </div>
        <motion.div
          animate={{ rotate: isExpanded ? 180 : 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
        >
          <ChevronDown className="w-5 h-5 text-gray-400" />
        </motion.div>
      </button>

      {/* Content */}
      <AnimatePresence initial={false}>
        {isExpanded && (
          <motion.div
            ref={contentRef}
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{
              duration: 0.35,
              ease: [0.04, 0.62, 0.23, 0.98],
            }}
            style={{ overflow: 'hidden' }}
          >
            <div className="px-4 pb-4 space-y-6 border-t border-gray-100">
              {/* Quick Presets */}
              <SettingsPresets
                currentSettings={settings}
                onPresetSelect={onSettingsChange}
              />

              {/* Validation Errors */}
              {!validation.isValid && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="w-4 h-4 text-amber-600 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-amber-800">
                        Settings Conflict
                      </p>
                      <ul className="text-sm text-amber-700 mt-1 space-y-1">
                        {validation.errors.map((error, index) => (
                          <li
                            key={index}
                            className="flex items-start space-x-1"
                          >
                            <span>•</span>
                            <span>{error}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {/* Duration Setting */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-900">
                  Video Duration
                </label>
                <div className="grid grid-cols-1 gap-2">
                  {compatibleDurations.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleDurationChange(option.value)}
                      className={`p-3 text-left border-2 rounded-lg transition-all duration-200 ${
                        settings.duration === option.value
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-900">
                          {option.label}
                        </span>
                        {settings.duration === option.value && (
                          <div className="w-2 h-2 bg-purple-500 rounded-full" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {option.description}
                      </p>
                    </button>
                  ))}
                </div>
              </div>

              {/* Quality Setting */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-900">
                  Video Quality
                </label>
                <div className="grid grid-cols-1 gap-2">
                  {compatibleQualities.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleQualityChange(option.value)}
                      className={`p-3 text-left border-2 rounded-lg transition-all duration-200 ${
                        settings.quality === option.value
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      disabled={
                        !compatibleQualities.some(
                          (q) => q.value === option.value
                        )
                      }
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-900">
                          {option.label}
                        </span>
                        {settings.quality === option.value && (
                          <div className="w-2 h-2 bg-purple-500 rounded-full" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {option.description}
                      </p>
                    </button>
                  ))}
                </div>
              </div>

              {/* Aspect Ratio Setting */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-900">
                  Aspect Ratio
                </label>
                <div className="grid grid-cols-2 lg:grid-cols-3 gap-2">
                  {AI_SMILE_CONFIG.videoSettings.aspectRatios.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleAspectRatioChange(option.value)}
                      className={`p-3 text-center border-2 rounded-lg transition-all duration-200 ${
                        settings.aspectRatio === option.value
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="text-2xl mb-1">{option.icon}</div>
                      <div className="font-medium text-gray-900 text-sm">
                        {option.label}
                      </div>
                      <div className="text-xs text-gray-600 mt-1">
                        {option.description}
                      </div>
                      {settings.aspectRatio === option.value && (
                        <div className="w-2 h-2 bg-purple-500 rounded-full mx-auto mt-2" />
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Info Box */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <Info className="w-4 h-4 text-blue-600 mt-0.5" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">Setting Guidelines:</p>
                    <ul className="space-y-1 text-xs">
                      <li>
                        • 8-second videos are only available in 720p quality
                      </li>
                      <li>
                        • 1080p quality is only available for 5-second videos
                      </li>
                      <li>
                        • 9:16 aspect ratio is optimized for mobile viewing
                      </li>
                      <li>
                        • Square (1:1) format works great for social media posts
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
