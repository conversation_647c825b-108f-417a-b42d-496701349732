'use client'

import React from 'react'
import { Smartphone, Monitor, Square, Video } from 'lucide-react'
import { VideoSettings } from './AdvancedSettings'

interface SettingsPresetsProps {
  currentSettings: VideoSettings
  onPresetSelect: (settings: VideoSettings) => void
  className?: string
}

const PRESETS = [
  {
    id: 'mobile',
    name: 'Mobile',
    icon: Smartphone,
    description: 'Optimized for mobile viewing',
    settings: { duration: 5, quality: '720p', aspectRatio: '9:16' },
    color: 'text-purple-600 bg-purple-50 border-purple-200',
  },
  {
    id: 'social',
    name: 'Social Media',
    icon: Square,
    description: 'Perfect for Instagram posts',
    settings: { duration: 5, quality: '1080p', aspectRatio: '1:1' },
    color: 'text-pink-600 bg-pink-50 border-pink-200',
  },
  {
    id: 'desktop',
    name: 'Desktop',
    icon: Monitor,
    description: 'Widescreen format',
    settings: { duration: 5, quality: '1080p', aspectRatio: '16:9' },
    color: 'text-blue-600 bg-blue-50 border-blue-200',
  },
  {
    id: 'extended',
    name: 'Extended',
    icon: Video,
    description: 'Longer duration video',
    settings: { duration: 8, quality: '720p', aspectRatio: '9:16' },
    color: 'text-green-600 bg-green-50 border-green-200',
  },
] as const

export function SettingsPresets({
  currentSettings,
  onPresetSelect,
  className = '',
}: SettingsPresetsProps) {
  const isPresetActive = (presetSettings: VideoSettings) => {
    return (
      currentSettings.duration === presetSettings.duration &&
      currentSettings.quality === presetSettings.quality &&
      currentSettings.aspectRatio === presetSettings.aspectRatio
    )
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h4 className="text-sm font-medium text-gray-900">Quick Presets</h4>
      <div className="grid grid-cols-2 gap-2">
        {PRESETS.map((preset) => {
          const Icon = preset.icon
          const isActive = isPresetActive(preset.settings)
          
          return (
            <button
              key={preset.id}
              onClick={() => onPresetSelect(preset.settings)}
              className={`p-3 text-left border-2 rounded-lg transition-all duration-200 ${
                isActive
                  ? `${preset.color} border-current`
                  : 'border-gray-200 hover:border-gray-300 bg-white'
              }`}
            >
              <div className="flex items-start space-x-2">
                <Icon className={`w-4 h-4 mt-0.5 ${
                  isActive ? 'text-current' : 'text-gray-400'
                }`} />
                <div className="min-w-0 flex-1">
                  <div className={`font-medium text-sm ${
                    isActive ? 'text-current' : 'text-gray-900'
                  }`}>
                    {preset.name}
                  </div>
                  <div className={`text-xs mt-1 ${
                    isActive ? 'text-current opacity-80' : 'text-gray-500'
                  }`}>
                    {preset.description}
                  </div>
                  <div className={`text-xs mt-1 font-mono ${
                    isActive ? 'text-current opacity-70' : 'text-gray-400'
                  }`}>
                    {preset.settings.duration}s • {preset.settings.quality} • {preset.settings.aspectRatio}
                  </div>
                </div>
                {isActive && (
                  <div className="w-2 h-2 bg-current rounded-full mt-1" />
                )}
              </div>
            </button>
          )
        })}
      </div>
    </div>
  )
}