'use client'

import React, { useState } from 'react'
import { Download, RefreshCw, Play, Pause, ArrowRight } from 'lucide-react'
import { AI_SMILE_CONFIG } from '../config'
import Image from 'next/image'

interface TaskDetail {
  taskId: string
  state: 'pending' | 'processing' | 'completed' | 'failed'
  inputInfo?: {
    imageUrl: string
    prompt?: string
  }
  videoInfo?: {
    videoUrl: string
    duration?: number
    quality?: string
    aspectRatio?: string
  }
  error?: {
    message: string
  }
  expireFlag?: boolean
}

interface VideoResultDisplayProps {
  taskDetail: TaskDetail | null
  isLoading: boolean
  error: string | null
  onDownload: (url: string) => void
  onRetry: () => void
  className?: string
  videoSettings?: {
    duration: number
    quality: string
    aspectRatio: string
  }
}

export function VideoResultDisplay({
  taskDetail,
  isLoading,
  error,
  onDownload,
  onRetry,
  className = '',
  videoSettings,
}: VideoResultDisplayProps) {
  const [isPlaying, setIsPlaying] = useState(false)

  // 示例内容的状态
  const [isImageLoading, setIsImageLoading] = useState(true)
  const [isVideoLoading, setIsVideoLoading] = useState(true)
  const [imageError, setImageError] = useState(false)
  const [videoError, setVideoError] = useState(false)

  const handlePlayPause = (video: HTMLVideoElement) => {
    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
    setIsPlaying(!isPlaying)
  }

  const handleDownload = () => {
    if (taskDetail?.videoInfo?.videoUrl) {
      onDownload(taskDetail.videoInfo.videoUrl)
    }
  }

  // 加载中状态
  if (isLoading) {
    return (
      <div className={`p-8 ${className}`}>
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center animate-pulse">
            <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin" />
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">
              Generating Your AI Smile Video
            </h3>
            <p className="text-sm text-gray-600">
              {AI_SMILE_CONFIG.messages.info.aiProcessing}
            </p>
            <p className="text-xs text-gray-500">
              This usually takes 1-3 minutes...
            </p>
          </div>
        </div>
      </div>
    )
  }

  // 错误状态
  if (error || taskDetail?.state === 'failed') {
    return (
      <div className={`p-8 ${className}`}>
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <RefreshCw className="w-8 h-8 text-red-500" />
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">
              Generation Failed
            </h3>
            <p className="text-sm text-red-600">
              {error || taskDetail?.error?.message || 'Something went wrong'}
            </p>
          </div>
          <button
            onClick={onRetry}
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }
  // 成功状态 - 显示结果视频
  if (taskDetail?.state === 'completed' && taskDetail.videoInfo?.videoUrl) {
    return (
      <div className={`p-8 ${className}`}>
        <div className="space-y-6">
          {/* 视频播放器 */}
          <div className="relative max-w-md mx-auto">
            <div className="relative aspect-[16/9] bg-gray-100 rounded-lg overflow-hidden">
              <video
                className="w-full h-full object-contain"
                src={taskDetail.videoInfo.videoUrl}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onEnded={() => setIsPlaying(false)}
                {...AI_SMILE_CONFIG.ui.videoPlayer}
              />

              {/* 播放/暂停按钮覆盖层 */}
              <div
                className="absolute inset-0 flex items-center justify-center bg-black/20 hover:bg-black/30 transition-all duration-200 cursor-pointer"
                onClick={(e) => {
                  const video = e.currentTarget.parentElement?.querySelector(
                    'video'
                  ) as HTMLVideoElement
                  handlePlayPause(video)
                }}
              >
                <div className="bg-white/90 backdrop-blur-sm rounded-full p-4 hover:scale-110 transition-transform duration-200">
                  {isPlaying ? (
                    <Pause className="w-8 h-8 text-gray-800" />
                  ) : (
                    <Play className="w-8 h-8 text-gray-800 ml-1" />
                  )}
                </div>
              </div>
            </div>

            {/* 视频信息 */}
            <div className="absolute top-3 right-3 space-y-1">
              {taskDetail.videoInfo.duration && (
                <div className="bg-black/60 text-white text-xs px-2 py-1 rounded">
                  {taskDetail.videoInfo.duration}s
                </div>
              )}
              {videoSettings && (
                <div className="bg-black/60 text-white text-xs px-2 py-1 rounded">
                  {videoSettings.quality}
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={handleDownload}
              className="flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 hover:shadow-lg"
            >
              <Download className="w-5 h-5" />
              <span>Download Video</span>
            </button>
          </div>

          {/* 过期提示 */}
          {taskDetail.expireFlag && (
            <div className="text-center">
              <p className="text-xs text-amber-600 bg-amber-50 px-3 py-2 rounded-lg">
                ⚠️ This video will expire soon. Please download it to save
                permanently.
              </p>
            </div>
          )}
        </div>
      </div>
    )
  }

  // 默认状态 - 展示示例
  // 获取示例内容
  const sampleContent = AI_SMILE_CONFIG.ui.sampleContent[0]

  return (
    <div className={`space-y-6 ${className} p-6`}>
      {/* 示例说明 */}
      <div className="text-center space-y-2">
        <div className="w-16 h-16 mx-auto bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
          <span className="text-2xl">😊</span>
        </div>
        <h3 className="text-lg font-semibold text-gray-900">
          See What AI Smile Can Create
        </h3>
        <p className="text-sm text-gray-600">
          Upload a portrait photo to generate natural, beautiful smile videos
        </p>
        {sampleContent?.badge && (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 mt-2">
            {sampleContent.badge} Example
          </span>
        )}
      </div>

      {/* 示例内容展示区域 */}
      <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
        <div className="flex flex-col lg:flex-row lg:items-center gap-6">
          {/* 示例图片 */}
          <div className="flex-1">
            <h4 className="text-sm font-medium text-gray-700 mb-3 text-center">
              Input Example
            </h4>
            <div className="relative aspect-square max-w-xs mx-auto rounded-lg overflow-hidden">
              {isImageLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
                </div>
              )}
              {imageError ? (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                  <div className="text-center">
                    <span className="text-2xl mb-2 block">📷</span>
                    <p className="text-sm text-gray-500">
                      Failed to load image
                    </p>
                  </div>
                </div>
              ) : (
                <Image
                  src={sampleContent?.imageUrl}
                  alt={sampleContent?.alt || 'AI Smile Example'}
                  className="w-full h-full object-contain border border-gray-200"
                  onLoad={() => setIsImageLoading(false)}
                  onError={() => {
                    setIsImageLoading(false)
                    setImageError(true)
                  }}
                  width={320}
                  height={320}
                />
              )}
            </div>
          </div>

          {/* 箭头指示 */}
          <div className="flex-shrink-0 self-center">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-full p-2">
              <ArrowRight className="h-5 w-5 text-white" />
            </div>
          </div>

          {/* 示例视频 */}
          <div className="flex-1">
            <h4 className="text-sm font-medium text-gray-700 mb-3 text-center">
              AI Smile Result
            </h4>
            <div className="relative aspect-square max-w-xs mx-auto rounded-lg overflow-hidden">
              {isVideoLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
                </div>
              )}
              {videoError ? (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                  <div className="text-center">
                    <Play className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">
                      Failed to load video
                    </p>
                  </div>
                </div>
              ) : (
                <video
                  src={sampleContent?.videoUrl}
                  className="w-full h-full object-contain border border-gray-200"
                  controls
                  loop
                  muted
                  playsInline
                  preload="metadata"
                  onLoadedData={() => setIsVideoLoading(false)}
                  onError={() => {
                    setIsVideoLoading(false)
                    setVideoError(true)
                  }}
                />
              )}
            </div>
          </div>
        </div>

        {/* 底部提示 */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            This is a preview of what your AI smile video will look like
          </p>
        </div>
      </div>
    </div>
  )
}
