'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from '@ui/components/dialog'
import { Button } from '@ui/components/button'
import { Badge } from '@ui/components/badge'
import { Card, CardContent } from '@ui/components/card'
import { Skeleton } from '@ui/components/skeleton'
import {
  Download,
  RefreshCw,
  Share2,
  ZoomIn,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  ArrowRight,
  ImageIcon,
  Video,
} from 'lucide-react'
import { downloadVideo, downloadImage, isVideoUrl } from '../lib/utils'
import toast from 'react-hot-toast'
import { HistoryItem } from '@/types/history'

interface FieldMapping {
  firstImage: string
  secondImage?: string
  resultImage: string
  labels?: {
    firstImage: string
    secondImage?: string
  }
}

// 默认字段映射（ai-smile 模块专用）
const DEFAULT_FIELD_MAPPING: FieldMapping = {
  firstImage: 'imageUrl',
  // secondImage 不设置，表示单图片
  resultImage: 'video_url', // AI Smile 结果字段
  labels: {
    firstImage: 'Input Image',
  },
}

interface TaskDetailModalProps {
  isOpen: boolean
  onClose: () => void
  task: HistoryItem | null
  onRegenerate: (firstImage: string, secondImage?: string) => Promise<void>
  fieldMapping: FieldMapping
}

interface ImagePreviewProps {
  src: string
  alt: string
  title: string
  onZoomIn: (src: string) => void
  className?: string
}

interface VideoPreviewProps {
  src: string
  alt: string
  title: string
  onZoomIn: (src: string) => void
  className?: string
}

interface EmptyPlaceholderProps {
  title: string
  message: string
  icon?: React.ReactNode
  className?: string
}

// 图片预览组件
const ImagePreview: React.FC<ImagePreviewProps> = ({
  src,
  alt,
  title,
  onZoomIn,
  className = '',
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleImageLoad = () => {
    setIsLoading(false)
    setHasError(false)
  }

  const handleImageError = () => {
    setIsLoading(false)
    setHasError(true)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <h4 className="text-sm font-medium text-gray-700">{title}</h4>
      <Card className="overflow-hidden">
        <CardContent className="p-0 relative aspect-video">
          {isLoading && <Skeleton className="absolute inset-0 w-full h-full" />}

          {hasError ? (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
              <div className="text-center">
                <XCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">Failed to load image</p>
              </div>
            </div>
          ) : (
            <>
              <img
                src={src}
                alt={alt}
                className="w-full h-full object-cover"
                onLoad={handleImageLoad}
                onError={handleImageError}
              />

              {!isLoading && (
                <button
                  onClick={() => onZoomIn(src)}
                  className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-center justify-center opacity-0 hover:opacity-100"
                >
                  <div className="bg-white/90 rounded-full p-2">
                    <ZoomIn className="h-4 w-4 text-gray-700" />
                  </div>
                </button>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// 视频预览组件
const VideoPreview: React.FC<VideoPreviewProps> = ({
  src,
  alt,
  title,
  onZoomIn,
  className = '',
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(true)
  const videoRef = React.useRef<HTMLVideoElement>(null)

  const handleVideoLoad = () => {
    setIsLoading(false)
    setHasError(false)
  }

  const handleVideoError = () => {
    setIsLoading(false)
    setHasError(true)
  }

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted
      setIsMuted(!isMuted)
    }
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <h4 className="text-sm font-medium text-gray-700">{title}</h4>
      <Card className="overflow-hidden">
        <CardContent className="p-0 relative aspect-video">
          {isLoading && <Skeleton className="absolute inset-0 w-full h-full" />}

          {hasError ? (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
              <div className="text-center">
                <XCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">Failed to load video</p>
              </div>
            </div>
          ) : (
            <>
              <video
                ref={videoRef}
                src={src}
                className="w-full h-full object-cover"
                onLoadedData={handleVideoLoad}
                onError={handleVideoError}
                muted={isMuted}
                controls={false}
                loop
              />

              {!isLoading && (
                <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200">
                  {/* Video controls overlay */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={togglePlay}
                        className="bg-white/90 rounded-full p-2 hover:bg-white transition-colors"
                      >
                        {isPlaying ? (
                          <Pause className="h-4 w-4 text-gray-700" />
                        ) : (
                          <Play className="h-4 w-4 text-gray-700" />
                        )}
                      </button>
                      <button
                        onClick={toggleMute}
                        className="bg-white/90 rounded-full p-2 hover:bg-white transition-colors"
                      >
                        {isMuted ? (
                          <VolumeX className="h-4 w-4 text-gray-700" />
                        ) : (
                          <Volume2 className="h-4 w-4 text-gray-700" />
                        )}
                      </button>
                      <button
                        onClick={() => onZoomIn(src)}
                        className="bg-white/90 rounded-full p-2 hover:bg-white transition-colors"
                      >
                        <Maximize className="h-4 w-4 text-gray-700" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// 空状态占位符组件
const EmptyPlaceholder: React.FC<EmptyPlaceholderProps> = ({
  title,
  message,
  icon,
  className = '',
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      <h4 className="text-sm font-medium text-gray-700">{title}</h4>
      <Card className="overflow-hidden">
        <CardContent className="p-0 relative aspect-video">
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              {icon || (
                <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
              )}
              <p className="text-sm text-gray-500">{message}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// 状态徽章组件
const StatusBadge: React.FC<{ status: HistoryItem['status'] }> = ({
  status,
}) => {
  const statusConfig = {
    SUCCESS: {
      label: 'Completed',
      badgeStatus: 'success' as const,
      icon: CheckCircle,
    },
    FAILED: {
      label: 'Failed',
      badgeStatus: 'error' as const,
      icon: XCircle,
    },
    PROCESSING: {
      label: 'Processing',
      badgeStatus: 'info' as const,
      icon: Clock,
    },
    PENDING: {
      label: 'Pending',
      badgeStatus: 'warning' as const,
      icon: AlertCircle,
    },
  }

  const config = statusConfig[status]
  const Icon = config.icon

  return (
    <Badge status={config.badgeStatus} className="text-xs flex">
      <Icon className="h-3 w-3 mr-1" />
      {config.label}
    </Badge>
  )
}

// 任务信息组件
const TaskInfo: React.FC<{ task: HistoryItem }> = ({ task }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
  }

  const getDuration = () => {
    if (!task.completed_at) return null
    const start = new Date(task.created_at).getTime()
    const end = new Date(task.completed_at).getTime()
    const duration = Math.round((end - start) / 1000)
    return `${duration}s`
  }

  return (
    <Card>
      <CardContent className="p-4 space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">Task Details</h4>
          <StatusBadge status={task.status} />
        </div>

        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Task ID:</span>
            <span className="font-mono text-xs">{task.external_task_id}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-600">Created:</span>
            <span>{formatDate(task.created_at)}</span>
          </div>

          {task.completed_at && (
            <div className="flex justify-between">
              <span className="text-gray-600">Completed:</span>
              <span>{formatDate(task.completed_at)}</span>
            </div>
          )}

          {getDuration() && (
            <div className="flex justify-between">
              <span className="text-gray-600">Duration:</span>
              <span>{getDuration()}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function TaskDetailModal({
  isOpen,
  onClose,
  task,
  onRegenerate,
  fieldMapping = DEFAULT_FIELD_MAPPING,
}: TaskDetailModalProps) {
  const [enlargedMedia, setEnlargedMedia] = useState<string | null>(null)

  if (!task) return null

  // 获取媒体 URL 的辅助函数（支持图片和视频）
  const getMediaUrl = (
    mediaType: 'first' | 'second' | 'result'
  ): string | null => {
    switch (mediaType) {
      case 'first':
        // AI Smile 输入图片在 input_params.imageUrl 中
        return task.input_params?.imageUrl || null
      case 'second':
        return fieldMapping.secondImage
          ? task.input_params?.[fieldMapping.secondImage] || null
          : null
      case 'result':
        // AI Smile 优先返回视频，如果没有视频则返回图片
        return (
          task.result_data?.video_url ||
          task.result_data?.[fieldMapping.resultImage] ||
          task.result_data?.image_url ||
          task.result_data?.result_url ||
          null
        )
      default:
        return null
    }
  }

  // 保持向后兼容的 getImageUrl 函数
  const getImageUrl = getMediaUrl

  // 获取图片标签的辅助函数
  const getImageLabel = (imageType: 'first' | 'second'): string => {
    switch (imageType) {
      case 'first':
        return fieldMapping.labels?.firstImage || 'First image'
      case 'second':
        return fieldMapping.labels?.secondImage || 'Second image'
      default:
        return ''
    }
  }

  const handleDownloadImage = async (imageUrl: string, index?: number) => {
    try {
      await downloadImage(imageUrl, undefined, index)
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  const handleDownloadVideo = async (videoUrl: string) => {
    try {
      await downloadVideo(videoUrl)
      toast.success('Video download started!')
    } catch (error) {
      console.error('Download failed:', error)
      toast.error('Failed to download video')
    }
  }

  const handleShare = async () => {
    const shareUrl = getMediaUrl('result')
    if (shareUrl) {
      try {
        await navigator.clipboard.writeText(shareUrl)
        toast.success(
          isVideoUrl(shareUrl)
            ? 'Video URL copied to clipboard!'
            : 'Image URL copied to clipboard!'
        )
      } catch (error) {
        toast.error('Failed to copy to clipboard')
      }
    }
  }

  const handleRegenerate = async () => {
    const firstImage = getImageUrl('first')
    const secondImage = getImageUrl('second')

    if (firstImage) {
      // AI Smile 只需要一张图片，但接口支持可选的第二个参数
      await onRegenerate(firstImage, secondImage || undefined)
      onClose()
    }
  }

  const resultUrl = getMediaUrl('result')
  const isVideo = resultUrl && isVideoUrl(resultUrl)

  return (
    <>
      {/* 主弹窗 */}
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto bg-white">
          <DialogHeader>
            <DialogTitle>AI Smile Video Details</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* 内容展示区域 */}
            <div className="flex flex-col lg:flex-row lg:items-center gap-6">
              {/* 输入图片 - 始终显示 */}
              <div className="flex-1">
                {getImageUrl('first') ? (
                  <ImagePreview
                    src={getImageUrl('first')!}
                    alt={getImageLabel('first')}
                    title={getImageLabel('first')}
                    onZoomIn={setEnlargedMedia}
                  />
                ) : (
                  <EmptyPlaceholder
                    title={getImageLabel('first')}
                    message="No input image available"
                    icon={
                      <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    }
                  />
                )}
              </div>

              {/* 箭头指示 */}
              <div className="flex-shrink-0 self-center">
                <ArrowRight className="h-6 w-6 text-gray-400" />
              </div>

              {/* 结果展示 - 始终显示 */}
              <div className="flex-1">
                {resultUrl ? (
                  isVideo ? (
                    <VideoPreview
                      src={resultUrl}
                      alt="AI Smile Video Result"
                      title="AI Smile Video Result"
                      onZoomIn={setEnlargedMedia}
                    />
                  ) : (
                    <ImagePreview
                      src={resultUrl}
                      alt="AI Smile Result"
                      title="AI Smile Result"
                      onZoomIn={setEnlargedMedia}
                    />
                  )
                ) : (
                  <EmptyPlaceholder
                    title="AI Smile Video Result"
                    message={
                      task.status === 'PENDING'
                        ? 'Waiting for video generation...'
                        : task.status === 'PROCESSING'
                        ? 'AI is creating your smile video...'
                        : task.status === 'FAILED'
                        ? 'Video generation failed'
                        : 'No result available'
                    }
                    icon={
                      <Video className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    }
                  />
                )}
              </div>
            </div>

            {/* 任务信息 */}
            <TaskInfo task={task} />
          </div>

          <DialogFooter className="gap-2 flex-wrap">
            {/* 下载输入图片 */}
            {getImageUrl('first') && (
              <Button
                variant="outline"
                onClick={() => handleDownloadImage(getImageUrl('first')!, 0)}
              >
                <Download className="h-4 w-4 mr-2" />
                Download {getImageLabel('first')}
              </Button>
            )}

            {/* 下载结果 */}
            {resultUrl && (
              <>
                <Button
                  variant="outline"
                  onClick={() =>
                    isVideo
                      ? handleDownloadVideo(resultUrl)
                      : handleDownloadImage(resultUrl, 1)
                  }
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download {isVideo ? 'Video' : 'Result'}
                </Button>

                <Button variant="outline" onClick={handleShare}>
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </>
            )}

            <Button
              className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white"
              onClick={handleRegenerate}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Regenerate
            </Button>

            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 媒体放大查看弹窗 */}
      <Dialog
        open={!!enlargedMedia}
        onOpenChange={() => setEnlargedMedia(null)}
      >
        <DialogContent className="max-w-5xl max-h-[95vh] p-2 bg-white">
          <div className="relative">
            {enlargedMedia && isVideoUrl(enlargedMedia) ? (
              <video
                src={enlargedMedia}
                className="w-full h-auto max-h-[90vh] object-contain"
                controls
                autoPlay
                loop
              />
            ) : (
              <img
                src={enlargedMedia || ''}
                alt="Enlarged view"
                className="w-full h-auto max-h-[90vh] object-contain"
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
