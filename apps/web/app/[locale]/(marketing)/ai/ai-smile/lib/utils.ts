import { AI_SMILE_CONFIG } from '../config'

// 验证图片文件
export const validateImageFile = (
  file: File
): { isValid: boolean; error?: string } => {
  const allowedTypes = AI_SMILE_CONFIG.upload.allowedTypes as readonly string[]
  const maxSize = AI_SMILE_CONFIG.upload.maxFileSize

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: AI_SMILE_CONFIG.messages.errors.invalidFileType,
    }
  }

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: AI_SMILE_CONFIG.messages.errors.fileTooLarge,
    }
  }

  return { isValid: true }
}

// 下载文件
export const downloadFile = async (
  url: string,
  filename?: string
): Promise<void> => {
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error('Download failed')
    }

    const blob = await response.blob()
    const downloadUrl = window.URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || `ai-smile-${Date.now()}.mp4`

    document.body.appendChild(link)
    link.click()

    // 清理
    window.URL.revokeObjectURL(downloadUrl)
    document.body.removeChild(link)
  } catch (error) {
    console.error('Download error:', error)
    throw error
  }
}

// 格式化任务状态
export const formatTaskStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'Pending',
    processing: 'Processing',
    completed: 'Completed',
    failed: 'Failed',
  }

  return statusMap[status] || status
}

// 格式化时间
export const formatTimeAgo = (date: string | Date): string => {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor(
    (now.getTime() - targetDate.getTime()) / 1000
  )

  if (diffInSeconds < 60) {
    return 'Just now'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} day${days > 1 ? 's' : ''} ago`
  }
}

// 生成预览缩略图
export const generateThumbnail = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      const img = new Image()
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        if (!ctx) {
          reject(new Error('Canvas context not available'))
          return
        }

        // 设置缩略图尺寸
        const maxSize = 200
        let { width, height } = img

        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width
            width = maxSize
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height
            height = maxSize
          }
        }

        canvas.width = width
        canvas.height = height

        ctx.drawImage(img, 0, 0, width, height)
        resolve(canvas.toDataURL('image/jpeg', 0.8))
      }

      img.onerror = () => reject(new Error('Failed to load image'))
      img.src = e.target?.result as string
    }

    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsDataURL(file)
  })
}

// 检查浏览器兼容性
export const checkBrowserCompatibility = (): {
  isSupported: boolean
  issues: string[]
} => {
  const issues: string[] = []

  // 检查 File API
  if (!window.File || !window.FileReader) {
    issues.push('File API not supported')
  }

  // 检查 Canvas API
  if (!document.createElement('canvas').getContext) {
    issues.push('Canvas API not supported')
  }

  // 检查 Video API
  if (!document.createElement('video').canPlayType) {
    issues.push('Video API not supported')
  }

  // 检查 Fetch API
  if (!window.fetch) {
    issues.push('Fetch API not supported')
  }

  return {
    isSupported: issues.length === 0,
    issues,
  }
}

// 检查网络连接
export const checkNetworkConnection = async (): Promise<boolean> => {
  try {
    const response = await fetch('/api/health', {
      method: 'HEAD',
      cache: 'no-cache',
    })
    return response.ok
  } catch {
    return false
  }
}

// 延迟函数
export const delay = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

// 重试函数
export const retry = async <T>(
  fn: () => Promise<T>,
  retries: number = 3,
  delayMs: number = 1000
): Promise<T> => {
  try {
    return await fn()
  } catch (error) {
    if (retries > 0) {
      await delay(delayMs)
      return retry(fn, retries - 1, delayMs * 2) // 指数退避
    }
    throw error
  }
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * 下载图片文件
 * @param imageUrl - 图片URL
 * @param fileName - 文件名（可选）
 * @param index - 索引（可选，用于区分多个文件）
 */
export const downloadImage = async (
  imageUrl: string,
  fileName?: string,
  index?: number
) => {
  try {
    const response = await fetch(imageUrl)
    const blob = await response.blob()

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名
    const timestamp = Date.now()
    const indexSuffix = index !== undefined ? `-${index + 1}` : ''
    link.download = fileName || `ai-smile-image${indexSuffix}-${timestamp}.jpg`

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to download image:', error)
    throw error
  }
}

/**
 * 下载视频文件
 * @param videoUrl - 视频URL
 * @param fileName - 文件名（可选）
 */
export const downloadVideo = async (videoUrl: string, fileName?: string) => {
  try {
    const response = await fetch(videoUrl)
    const blob = await response.blob()

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName || `ai-smile-video-${Date.now()}.mp4`

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to download video:', error)
    throw error
  }
}

/**
 * 检查URL是否为视频文件
 * @param url - 文件URL
 * @returns 是否为视频文件
 */
export const isVideoUrl = (url: string): boolean => {
  const videoExtensions = ['.mp4', '.webm', '.mov', '.avi', '.mkv']
  const lowerUrl = url.toLowerCase()
  return videoExtensions.some((ext) => lowerUrl.includes(ext))
}
