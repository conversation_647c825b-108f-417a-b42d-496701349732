// AI Smile Video Configuration
export const AI_SMILE_CONFIG = {
  // Upload Configuration
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  },

  // UI Configuration
  ui: {
    // Pagination
    historyPageSize: 10,

    // Sample images and videos for AI Smile
    sampleContent: [
      {
        id: 1,
        imageUrl: '/images/ai-smile/ai-smile-image-1.png',
        videoUrl: '/images/ai-smile/ai-smile-video-1.mp4',
        alt: 'Natural smile example',
        category: 'natural',
        badge: 'Popular',
      },
      {
        id: 2,
        imageUrl: '/images/ai-smile/ai-smile-image-2.png',
        videoUrl: '/images/ai-smile/ai-smile-video-2.mp4',
        alt: 'Gentle smile example',
        category: 'gentle',
        badge: 'Trending',
      },
      {
        id: 3,
        imageUrl: '/images/ai-smile/ai-smile-image-3.png',
        videoUrl: '/images/ai-smile/ai-smile-video-3.mp4',
        alt: 'Warm smile example',
        category: 'warm',
      },
    ],

    // Upload guidance examples
    uploadGuideImages: [
      {
        id: 1,
        url: '/images/ai-smile/ai-smile-image-1.png',
        alt: 'Good example: Clear facial features',
        type: 'good',
        description: 'Clear facial features with good lighting',
      },
      {
        id: 2,
        url: '/images/ai-smile/ai-smile-image-2.png',
        alt: 'Good example: Front-facing portrait',
        type: 'good',
        description: 'Front-facing portrait with visible face',
      },
    ],

    // Animation and transition settings
    transitions: {
      duration: 200,
      easing: 'ease-in-out',
    },

    // Video player settings
    videoPlayer: {
      autoplay: true,
      loop: true,
      muted: true,
      controls: true,
      preload: 'metadata',
    },
  },

  // Video Generation Settings
  videoSettings: {
    // Duration options (in seconds)
    durations: [
      { value: 5, label: '5 seconds', description: 'Standard duration' },
      {
        value: 8,
        label: '8 seconds',
        description: 'Extended duration (720p only)',
      },
    ],

    // Quality options
    qualities: [
      {
        value: '720p',
        label: '720p',
        description: 'High Definition (compatible with all durations)',
      },
      {
        value: '1080p',
        label: '1080p',
        description: 'Full HD (5 seconds only)',
      },
    ],

    // Aspect ratio options
    aspectRatios: [
      {
        value: '16:9',
        label: '16:9',
        description: 'Widescreen (Landscape)',
        icon: '📺',
      },
      {
        value: '4:3',
        label: '4:3',
        description: 'Standard (Landscape)',
        icon: '🖥️',
      },
      { value: '1:1', label: '1:1', description: 'Square', icon: '⬜' },
      { value: '3:4', label: '3:4', description: 'Portrait', icon: '📱' },
      {
        value: '9:16',
        label: '9:16',
        description: 'Vertical (Mobile)',
        icon: '📲',
      },
    ],

    // Default settings
    defaults: {
      duration: 5,
      quality: '720p',
      aspectRatio: '9:16',
    },
  },

  // Feature Flags
  features: {
    enableHistory: true,
    enableSampleVideos: true,
    enableDownload: true,
    enableWebhook: true,
    enableUploadGuide: true,
    enableAdvancedSettings: true,

    // Development features
    enableDevTools: process.env.NODE_ENV === 'development',
    enableDebugLogs: process.env.NODE_ENV === 'development',
  },

  // Error Messages
  messages: {
    errors: {
      uploadFailed: 'Image upload failed, please try again.',
      generateFailed: 'AI Smile video generation failed, please try again.',
      loginRequired: 'Please login to use this feature.',
      invalidFileType: 'Please select a valid image file (JPG, PNG, WebP).',
      fileTooLarge: 'File size cannot exceed 10MB.',
      networkError: 'Network error, please check your connection.',
      noFaceDetected: 'Please upload an image with a clear face visible.',
      videoLoadFailed: 'Video loading failed, please try again.',
    },
    success: {
      uploadComplete: 'Image uploaded successfully!',
      generateComplete: 'AI Smile video generated successfully!',
      downloadStarted: 'Download started.',
      videoReady: 'Your AI Smile video is ready!',
    },
    info: {
      uploadInProgress: 'Uploading image...',
      generateInProgress: 'Generating AI Smile video...',
      processingTask: 'Processing your request, this may take a few minutes...',
      queueWaiting:
        'Your request is in queue, estimated wait time: {time} minutes',
      aiProcessing: 'AI is creating your smile video...',
    },
  },

  // Upload guidance content
  uploadGuide: {
    title: 'Upload Guidelines',
    subtitle: 'For best results, please follow these guidelines:',
    requirements: [
      {
        icon: '😊',
        title: 'Clear Face Required',
        description: 'Image must contain a clear, visible face',
      },
      {
        icon: '📸',
        title: 'Well-lit Portrait',
        description: 'Face should be well-lit and clearly visible',
      },
      {
        icon: '👤',
        title: 'Front-facing',
        description: 'Person should be facing towards the camera',
      },
      {
        icon: '✨',
        title: 'High Quality',
        description: 'Use high-resolution images for better results',
      },
    ],
  },
} as const

// Type helpers
export type SampleContent = {
  readonly id: number
  readonly imageUrl: string
  readonly videoUrl: string
  readonly alt: string
  readonly category: 'natural' | 'gentle' | 'warm'
  readonly badge?: string
}

export type UploadGuideImage = {
  readonly id: number
  readonly url: string
  readonly alt: string
  readonly type: 'good' | 'bad'
  readonly description: string
}

// 微笑类型定义
export type SmileType = {
  readonly id: string
  readonly name: string
  readonly description: string
  readonly imageUrl: string
  readonly prompt: string
  readonly tags: readonly string[]
  readonly badge?: string
}

export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed'

export const isValidImageFile = (file: File): boolean => {
  const allowedTypes = AI_SMILE_CONFIG.upload.allowedTypes as readonly string[]
  return (
    allowedTypes.includes(file.type) &&
    file.size <= AI_SMILE_CONFIG.upload.maxFileSize
  )
}

export const getErrorMessage = (
  errorKey: keyof typeof AI_SMILE_CONFIG.messages.errors
): string => {
  return AI_SMILE_CONFIG.messages.errors[errorKey]
}

export const getSuccessMessage = (
  successKey: keyof typeof AI_SMILE_CONFIG.messages.success
): string => {
  return AI_SMILE_CONFIG.messages.success[successKey]
}

export const getInfoMessage = (
  infoKey: keyof typeof AI_SMILE_CONFIG.messages.info,
  params?: Record<string, string>
): string => {
  let message: string = AI_SMILE_CONFIG.messages.info[infoKey]

  // 替换参数占位符
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      message = message.replace(`{${key}}`, value)
    })
  }

  return message
}

// Video settings validation
export const validateVideoSettings = (
  duration: number,
  quality: string,
  aspectRatio: string
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Check duration-quality compatibility
  if (duration === 8 && quality === '1080p') {
    errors.push('8-second videos cannot use 1080p quality. Please select 720p.')
  }

  if (quality === '1080p' && duration === 8) {
    errors.push(
      '1080p quality cannot be used with 8-second videos. Please select 5 seconds.'
    )
  }

  // Validate duration
  const validDurations = AI_SMILE_CONFIG.videoSettings.durations.map(
    (d) => d.value
  ) as readonly number[]
  if (!validDurations.includes(duration)) {
    errors.push(
      `Invalid duration: ${duration}. Must be one of: ${validDurations.join(
        ', '
      )}`
    )
  }

  // Validate quality
  const validQualities = AI_SMILE_CONFIG.videoSettings.qualities.map(
    (q) => q.value
  ) as readonly string[]
  if (!validQualities.includes(quality)) {
    errors.push(
      `Invalid quality: ${quality}. Must be one of: ${validQualities.join(
        ', '
      )}`
    )
  }

  // Validate aspect ratio
  const validAspectRatios = AI_SMILE_CONFIG.videoSettings.aspectRatios.map(
    (ar) => ar.value
  ) as readonly string[]
  if (!validAspectRatios.includes(aspectRatio)) {
    errors.push(
      `Invalid aspect ratio: ${aspectRatio}. Must be one of: ${validAspectRatios.join(
        ', '
      )}`
    )
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

// Get compatible options based on current selection
export const getCompatibleOptions = (
  currentDuration?: number,
  currentQuality?: string
) => {
  const { durations, qualities } = AI_SMILE_CONFIG.videoSettings

  // Filter qualities based on duration
  const compatibleQualities =
    currentDuration === 8
      ? qualities.filter((q) => q.value === '720p')
      : qualities

  // Filter durations based on quality
  const compatibleDurations =
    currentQuality === '1080p'
      ? durations.filter((d) => d.value === 5)
      : durations

  return {
    compatibleQualities,
    compatibleDurations,
  }
}
