'use client'

import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from '@ui/components/tabs'
import { useAuth } from '../../../../../modules/ui/hooks/use-auth'
import { HistoryIcon, ImageIcon, RotateCcw } from 'lucide-react'
import { AI_SMILE_CONFIG } from './config'

// 导入新的统一组件和hooks
import {
  useUnifiedGeneration,
  useUnifiedHistory,
  UnifiedHistoryTab,
  TaskType,
  GenerationProgress,
  useGenerationProgress,
} from '../components'

// 导入新的侧边栏组件
import {
  AiSmileSidebar,
  type GenerationParams,
} from './components/AiSmileSidebar'
import { downloadFile } from '../components/utils'

const TASK_TYPE: TaskType = 'aismile'

export default function AiSmilePage() {
  const [activeTab, setActiveTab] = useState('creations')

  const { isLoggedIn } = useAuth()

  // 进度管理hook
  const {
    progress: generationProgress,
    startProgress,
    stopProgress,
    resetProgress,
    setProgress: setGenerationProgress,
  } = useGenerationProgress()

  // 使用统一的生成hook
  const {
    isGenerating,
    taskId,
    error: generateError,
    taskDetail,
    isPolling,
    pollingError,
    generate,
    reset: resetGeneration,
    stopPolling,
  } = useUnifiedGeneration(TASK_TYPE)

  // 使用统一的历史记录hook
  const {
    items: historyItems,
    isLoading: isLoadingHistory,
    error: historyError,
    refreshHistory,
  } = useUnifiedHistory(TASK_TYPE)

  // 新的生成函数，处理侧边栏的参数
  const handleGenerateFromSidebar = useCallback(
    async (params: GenerationParams) => {
      try {
        // 切换到 creations tab
        setActiveTab('creations')

        // 开始进度计时器
        startProgress()

        // 使用统一的生成接口，直接使用已上传的图片URL
        await generate({
          imageUrl: params.uploadedImageUrl,
          prompt: params.prompt,
          duration: params.duration,
          quality: params.quality,
          aspectRatio: params.aspectRatio,
        })
      } catch (error) {
        console.error('Failed to generate AI smile video:', error)
        // 生成失败时重置进度
        resetProgress()
      }
    },
    [generate, startProgress, resetProgress, setActiveTab]
  )

  const handleReset = useCallback(() => {
    resetGeneration()
    stopPolling()

    // 重置进度状态
    resetProgress()
  }, [resetGeneration, stopPolling, resetProgress])

  const handleRegenerateFromHistory = useCallback(
    async (input: Record<string, any>) => {
      const imageUrl = input.imageUrl

      if (!imageUrl) return

      try {
        // 开始进度计时器
        startProgress()

        await generate({
          imageUrl,
          prompt: input.prompt || 'Create a natural, warm smile video',
          duration:
            input.duration || AI_SMILE_CONFIG.videoSettings.defaults.duration,
          quality:
            input.quality || AI_SMILE_CONFIG.videoSettings.defaults.quality,
          aspectRatio:
            input.aspectRatio ||
            AI_SMILE_CONFIG.videoSettings.defaults.aspectRatio,
        })
      } catch (error) {
        console.error('Failed to regenerate AI smile video:', error)
        // 生成失败时重置进度
        resetProgress()
      }
    },
    [generate, startProgress, resetProgress]
  )

  // 适配现有的VideoResultDisplay组件
  const adaptedTaskDetail = taskDetail
    ? {
        taskId: taskDetail.taskId,
        state: (taskDetail.status.status === 'success'
          ? 'completed'
          : taskDetail.status.status === 'failed'
          ? 'failed'
          : taskDetail.status.status === 'processing'
          ? 'processing'
          : 'pending') as 'pending' | 'processing' | 'completed' | 'failed',
        inputInfo: {
          imageUrl: taskDetail.input.imageUrl || '',
          prompt: taskDetail.input.prompt,
        },
        videoInfo: taskDetail.output
          ? {
              videoUrl: taskDetail.output.videoUrl || '',
              duration: taskDetail.output.duration,
              quality: taskDetail.output.quality,
              aspectRatio: taskDetail.output.aspectRatio,
            }
          : undefined,
        error: taskDetail.error,
        expireFlag: false,
      }
    : null

  React.useEffect(() => {
    if (isLoggedIn && historyItems.length === 0) {
      refreshHistory()
    }
  }, [isLoggedIn, historyItems.length, refreshHistory])

  React.useEffect(() => {
    if (
      taskDetail &&
      (taskDetail.status.status === 'success' ||
        taskDetail.status.status === 'failed')
    ) {
      setTimeout(() => {
        refreshHistory()
      }, 1000)
    }
  }, [taskDetail?.status.status, refreshHistory])

  // 监听任务完成状态，更新进度到100%
  React.useEffect(() => {
    if (taskDetail?.status.status === 'success') {
      // 任务成功完成，将进度设置为100%并停止计时器
      setGenerationProgress(100)
      stopProgress()
    } else if (taskDetail?.status.status === 'failed') {
      // 任务失败，重置进度
      resetProgress()
    }
  }, [
    taskDetail?.status.status,
    setGenerationProgress,
    stopProgress,
    resetProgress,
  ])

  return (
    <main className="h-screen pt-[68px] bg-gray-50">
      <div className="h-full flex bg-white border-t border-gray-200">
        <div className="w-[480px] border-r border-gray-200 bg-white flex flex-col">
          {/* 新的AI微笑侧边栏 */}
          <AiSmileSidebar
            onGenerate={handleGenerateFromSidebar}
            isGenerating={isGenerating}
            onReset={handleReset}
            className="h-full"
          />
        </div>

        <div className="flex-1 flex flex-col bg-gray-50">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            <div className="bg-white flex justify-center">
              <TabsList className="bg-transparent border-b border-gray-200 rounded-none">
                <TabsTrigger
                  value="creations"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <ImageIcon className="h-4 w-4" />
                  <span>Creations</span>
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <HistoryIcon className="h-4 w-4" />
                  <span>History</span>
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent
              value="creations"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="h-full overflow-y-auto p-4 relative">
                <div className="min-h-full flex items-start justify-center">
                  <div className="w-full">
                    <div className="text-center mb-12">
                      <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        Generate AI Smile Video
                      </h1>
                      <p className="text-lg text-gray-600">
                        Transform Photos into Natural Smiling Videos. Bring Joy
                        to Every Portrait.
                      </p>
                    </div>

                    <div className="flex justify-center">
                      <div className="w-full max-w-2xl">
                        {/* 使用统一的生成进度组件 */}
                        {isGenerating || isPolling ? (
                          <GenerationProgress
                            progress={generationProgress}
                            isGenerating={isGenerating || isPolling}
                            title="Generating AI Smile Video"
                            description="AI is creating your natural smiling video..."
                            size="md"
                          />
                        ) : generateError || pollingError ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                                <RotateCcw className="w-8 h-8 text-red-500" />
                              </div>
                              <div className="space-y-2">
                                <h3 className="text-lg font-semibold text-gray-900">
                                  Generation Failed
                                </h3>
                                <p className="text-sm text-red-600">
                                  {generateError || pollingError}
                                </p>
                              </div>
                              <button
                                onClick={() => {
                                  resetGeneration()
                                  stopPolling()
                                  resetProgress()
                                }}
                                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                              >
                                Try Again
                              </button>
                            </div>
                          </div>
                        ) : adaptedTaskDetail?.state === 'completed' &&
                          adaptedTaskDetail?.videoInfo?.videoUrl ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="space-y-6">
                              <div className="relative max-w-md mx-auto">
                                <div className="relative aspect-[16/9] bg-gray-100 rounded-lg overflow-hidden">
                                  <video
                                    className="w-full h-full object-contain"
                                    src={adaptedTaskDetail.videoInfo.videoUrl}
                                    controls
                                    loop
                                    muted
                                    playsInline
                                  />
                                </div>
                              </div>
                              <div className="flex justify-center space-x-4">
                                <button
                                  onClick={() =>
                                    downloadFile(
                                      adaptedTaskDetail.videoInfo?.videoUrl ||
                                        ''
                                    )
                                  }
                                  className="flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 hover:shadow-lg"
                                >
                                  <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                  </svg>
                                  <span>Download Video</span>
                                </button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                                <span className="text-2xl">😊</span>
                              </div>
                              <h3 className="text-lg font-semibold text-gray-900">
                                Ready to Create AI Smile Video
                              </h3>
                              <p className="text-sm text-gray-600">
                                Upload a clear portrait photo and let AI create
                                a natural smiling video
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="history"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="w-full h-full">
                {/* 使用新的统一历史记录组件 */}
                <UnifiedHistoryTab
                  taskType={TASK_TYPE}
                  onRegenerate={handleRegenerateFromHistory}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </main>
  )
}
