'use client'

import { useState, useCallback, useRef } from 'react'
import { VideoSettings } from '../components/AdvancedSettings'

// AI Smile 任务详情类型
interface AiSmileTaskDetail {
  taskId: string
  state: 'wait' | 'queueing' | 'generating' | 'success' | 'fail'
  inputInfo?: {
    imageUrl: string
    prompt?: string
  }
  videoInfo?: {
    videoUrl: string
    imageUrl?: string
    videoId?: string
    duration?: number
    quality?: string
    aspectRatio?: string
  }
  error?: {
    message: string
  }
  expireFlag?: boolean | number
  generateTime?: string
  failMsg?: string
  failCode?: string
}

// Hook 返回类型
interface UseAiSmileReturn {
  isGenerating: boolean
  taskId: string | null
  error: string | null
  taskDetail: AiSmileTaskDetail | null
  isPolling: boolean
  pollingError: string | null
  generateAiSmile: (
    imageUrl: string,
    videoSettings?: VideoSettings
  ) => Promise<void>
  reset: () => void
  stopPolling: () => void
}

export function useAiSmile(): UseAiSmileReturn {
  const [isGenerating, setIsGenerating] = useState(false)
  const [taskId, setTaskId] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [taskDetail, setTaskDetail] = useState<AiSmileTaskDetail | null>(null)
  const [isPolling, setIsPolling] = useState(false)
  const [pollingError, setPollingError] = useState<string | null>(null)

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 停止轮询
  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
      pollingIntervalRef.current = null
    }
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current)
      pollingTimeoutRef.current = null
    }
    setIsPolling(false)
  }, [])

  // 轮询任务状态
  const pollTaskStatus = useCallback(
    async (taskId: string) => {
      try {
        const response = await fetch(`/api/video/detail?taskId=${taskId}`)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (data.code === 200 && data.data) {
          const taskData = data.data
          setTaskDetail(taskData)

          // 检查任务是否完成
          if (taskData.state === 'success' || taskData.state === 'fail') {
            stopPolling()
            setIsGenerating(false)

            if (taskData.state === 'fail') {
              setPollingError(
                taskData.failMsg ||
                  taskData.error?.message ||
                  'AI Smile generation failed'
              )
            }
          }
        } else {
          throw new Error(data.msg || 'Failed to fetch task status')
        }
      } catch (error) {
        console.error('Polling error:', error)
        setPollingError(
          error instanceof Error ? error.message : 'Failed to get task status'
        )
        stopPolling()
        setIsGenerating(false)
      }
    },
    [stopPolling]
  )

  // 开始轮询
  const startPolling = useCallback(
    (taskId: string) => {
      setIsPolling(true)
      setPollingError(null)

      // 立即执行一次
      pollTaskStatus(taskId)

      // 设置定期轮询 (每5秒)
      pollingIntervalRef.current = setInterval(() => {
        pollTaskStatus(taskId)
      }, 5000)

      // 设置轮询超时 (10分钟)
      pollingTimeoutRef.current = setTimeout(() => {
        stopPolling()
        setPollingError('Task timeout - generation took too long')
        setIsGenerating(false)
      }, 10 * 60 * 1000) // 10分钟
    },
    [pollTaskStatus, stopPolling]
  )

  // 生成 AI Smile 视频
  const generateAiSmile = useCallback(
    async (imageUrl: string, videoSettings?: VideoSettings) => {
      try {
        setIsGenerating(true)
        setError(null)
        setTaskDetail(null)
        setPollingError(null)

        // 调用生成 API
        const response = await fetch('/api/video/aismile', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            imageUrl: imageUrl,
            duration: videoSettings?.duration,
            quality: videoSettings?.quality,
            aspectRatio: videoSettings?.aspectRatio,
          }),
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (data.code === 200 && data.data?.taskId) {
          const newTaskId = data.data.taskId
          setTaskId(newTaskId)

          // 开始轮询任务状态
          startPolling(newTaskId)
        } else if (data.code === 401) {
          // 需要登录
          throw new Error('Please login to use this feature')
        } else if (data.code === 402) {
          // 积分不足
          throw new Error('Insufficient credits. Please upgrade your plan.')
        } else {
          throw new Error(data.msg || 'Failed to start AI Smile generation')
        }
      } catch (error) {
        console.error('Generate AI Smile error:', error)
        setError(
          error instanceof Error
            ? error.message
            : 'Failed to generate AI Smile video'
        )
        setIsGenerating(false)
      }
    },
    [startPolling]
  )

  // 重置状态
  const reset = useCallback(() => {
    stopPolling()
    setIsGenerating(false)
    setTaskId(null)
    setError(null)
    setTaskDetail(null)
    setPollingError(null)
  }, [stopPolling])

  return {
    isGenerating,
    taskId,
    error,
    taskDetail,
    isPolling,
    pollingError,
    generateAiSmile,
    reset,
    stopPolling,
  }
}
