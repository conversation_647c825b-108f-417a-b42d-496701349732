.resizableContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

.resizableContainer [data-testid='container'] {
  height: 100% !important;
  border-radius: 0.5rem;
  overflow: hidden;
}

@keyframes moving-particle {
  0% {
    transform: translateX(0);
    opacity: 0;
  }
  20% {
    opacity: 0.7;
  }
  100% {
    transform: translateX(300px);
    opacity: 0;
  }
}

.movingParticle {
  animation: moving-particle 2s linear infinite;
}

@keyframes rushing {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.rushing {
  animation: rushing 15s linear infinite;
}

@keyframes fade-in-out {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

.fadeInOut {
  animation: fade-in-out 2s ease-in-out infinite;
}

/* 透明背景的马赛克展示效果 */
.transparencyChecker {
  background-image: 
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 16px 16px;
  background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}

/* 小尺寸的马赛克（用于历史记录小图） */
.transparencyCheckerSmall {
  background-image: 
    linear-gradient(45deg, #f5f5f5 25%, transparent 25%), 
    linear-gradient(-45deg, #f5f5f5 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, #f5f5f5 75%), 
    linear-gradient(-45deg, transparent 75%, #f5f5f5 75%);
  background-size: 8px 8px;
  background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
}

/* 图片容器，确保背景可见 */
.imageWithTransparency {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.imageWithTransparency img {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 100%;
}

/* 确保下载按钮在图片之上 */
.downloadButton {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  padding: 8px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.4);
  color: white;
  border: none;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease-in-out;
}

.downloadButton:hover {
  background: rgba(0, 0, 0, 0.6);
}

.imageContainer:hover .downloadButton {
  opacity: 1;
}

.imageContainer:hover .downloadButtonSmall {
  opacity: 1;
}

/* 历史记录的下载按钮样式 */
.downloadButtonSmall {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  padding: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  border: none;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease-in-out;
}

.downloadButtonSmall:hover {
  background: white;
}
