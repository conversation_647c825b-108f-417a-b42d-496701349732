'use client'

import { useState, useCallback } from 'react'
import styles from './index.module.css'

interface TransparencyImageViewerProps {
  src: string
  alt: string
  className?: string
  containerClassName?: string
  small?: boolean // 是否为小尺寸（用于历史记录）
  style?: React.CSSProperties
  onLoad?: () => void
  onError?: () => void
}

/**
 * 支持透明背景显示的图片组件
 * 对于PNG图片，会自动添加马赛克背景来显示透明区域
 */
export default function TransparencyImageViewer({
  src,
  alt,
  className = '',
  containerClassName = '',
  small = false,
  style,
  onLoad,
  onError,
}: TransparencyImageViewerProps) {
  const [isPNG, setIsPNG] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  // 检测是否为PNG图片
  const checkIfPNG = useCallback((imageUrl: string) => {
    // 检查URL是否包含.png
    const urlContainsPNG = imageUrl.toLowerCase().includes('.png')
    
    // 检查URL是否可能是无扩展名的PNG（通过常见的背景移除服务域名）
    const isPossiblePNG = urlContainsPNG || 
      imageUrl.includes('remove.bg') ||
      imageUrl.includes('background-remove') ||
      imageUrl.includes('bg-remove')

    return isPossiblePNG
  }, [])

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true)
    setIsPNG(checkIfPNG(src))
    onLoad?.()
  }, [src, checkIfPNG, onLoad])

  const handleImageError = useCallback(() => {
    setImageLoaded(true)
    onError?.()
  }, [onError])

  // 组合CSS类名
  const getContainerClasses = () => {
    const baseClasses = [
      styles.imageWithTransparency,
      containerClassName
    ]

    // 只有当图片已加载且检测为PNG时才添加马赛克背景
    if (imageLoaded && isPNG) {
      baseClasses.push(small ? styles.transparencyCheckerSmall : styles.transparencyChecker)
    }

    return baseClasses.filter(Boolean).join(' ')
  }

  const getImageClasses = () => {
    const baseClasses = [
      'w-full h-full',
      className
    ]

    // 为PNG图片添加特殊样式确保透明度正确显示
    if (imageLoaded && isPNG) {
      baseClasses.push('object-contain') // 使用contain而不是cover以保持透明边缘
    } else {
      baseClasses.push('object-cover')
    }

    return baseClasses.filter(Boolean).join(' ')
  }

  return (
    <div 
      className={getContainerClasses()}
      style={style}
    >
      <img
        src={src}
        alt={alt}
        className={getImageClasses()}
        onLoad={handleImageLoad}
        onError={handleImageError}
        loading="lazy"
      />
    </div>
  )
}