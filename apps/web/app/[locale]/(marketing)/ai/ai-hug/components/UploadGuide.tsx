'use client'

import React from 'react'
import { Card, CardContent } from '@ui/components/card'
import { AI_HUG_CONFIG } from '../config'

interface UploadGuideProps {
  className?: string
}

export function UploadGuide({ className = '' }: UploadGuideProps) {
  const { uploadGuide } = AI_HUG_CONFIG

  return (
    <Card
      className={`bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 ${className}`}
    >
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="text-center">
            <h4 className="text-sm font-semibold text-gray-900">
              {uploadGuide.title}
            </h4>
            <p className="text-xs text-gray-600 mt-1">{uploadGuide.subtitle}</p>
          </div>

          <div className="grid grid-cols-2 gap-3">
            {uploadGuide.requirements.map((requirement, index) => (
              <div
                key={index}
                className="flex items-start space-x-2 p-2 bg-white/50 rounded-lg"
              >
                <span className="text-lg">{requirement.icon}</span>
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium text-gray-900 leading-tight">
                    {requirement.title}
                  </p>
                  <p className="text-xs text-gray-600 mt-0.5 leading-tight">
                    {requirement.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Example Images */}
          {AI_HUG_CONFIG.ui.uploadGuideImages.length > 0 && (
            <div className="space-y-2">
              <p className="text-xs font-medium text-gray-700 text-center">
                Good Examples:
              </p>
              <div className="flex justify-center space-x-2">
                {AI_HUG_CONFIG.ui.uploadGuideImages.slice(0, 2).map((image) => (
                  <div key={image.id} className="relative">
                    <img
                      src={image.url}
                      alt={image.alt}
                      className="w-16 h-16 object-cover rounded-lg border-2 border-green-200"
                    />
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
