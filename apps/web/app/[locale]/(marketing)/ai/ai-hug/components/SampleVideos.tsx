'use client'

import React, { useState, memo, useCallback } from 'react'
import { Play, Pause } from 'lucide-react'
import { SampleContent } from '../config'

interface SampleVideosProps {
  samples: readonly SampleContent[]
  onSelectSample: (sample: SampleContent) => void
  className?: string
}

export const SampleVideos = memo(function SampleVideos({
  samples,
  onSelectSample,
  className = '',
}: SampleVideosProps) {
  const [playingVideo, setPlayingVideo] = useState<number | null>(null)
  const [hoveredItem, setHoveredItem] = useState<number | null>(null)

  const handleVideoPlay = useCallback((id: number, event: React.MouseEvent) => {
    event.stopPropagation()
    setPlayingVideo((current) => (current === id ? null : id))
  }, [])

  const handleSelectSample = useCallback(
    (sample: SampleContent) => {
      onSelectSample(sample)
    },
    [onSelectSample]
  )

  const handleMouseEnter = useCallback((id: number) => {
    setHoveredItem(id)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setHoveredItem(null)
  }, [])

  return (
    <div className={`space-y-3 ${className}`}>
      {samples.map((sample) => (
        <div
          key={sample.id}
          className="group relative bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition-all duration-200 cursor-pointer"
          onClick={() => handleSelectSample(sample)}
          onMouseEnter={() => handleMouseEnter(sample.id)}
          onMouseLeave={handleMouseLeave}
        >
          <div className="p-3">
            <div className="flex items-center space-x-3">
              {/* 样例图片 */}
              <div className="relative flex-shrink-0">
                <img
                  src={sample.imageUrl}
                  alt={sample.alt}
                  className="w-16 h-16 object-cover rounded-lg"
                  loading="lazy"
                />
                {sample.badge && (
                  <span className="absolute -top-1 -right-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs px-1.5 py-0.5 rounded-full text-[10px] font-medium">
                    {sample.badge}
                  </span>
                )}
              </div>

              {/* 箭头指示 */}
              <div className="flex items-center">
                <div className="text-gray-400">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </div>

              {/* 样例视频 */}
              <div className="relative flex-shrink-0">
                <video
                  ref={(el) => {
                    if (el) {
                      if (playingVideo === sample.id) {
                        el.play()
                      } else {
                        el.pause()
                      }
                    }
                  }}
                  src={sample.videoUrl}
                  className="w-16 h-16 object-cover rounded-lg"
                  loop
                  muted
                  playsInline
                  preload="metadata"
                />
                {/* 播放控制按钮 */}
                <button
                  onClick={(e) => handleVideoPlay(sample.id, e)}
                  className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  {playingVideo === sample.id ? (
                    <Pause className="w-4 h-4 text-white" />
                  ) : (
                    <Play className="w-4 h-4 text-white" />
                  )}
                </button>
              </div>

              {/* 描述文本 */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {sample.alt}
                </p>
                <p className="text-xs text-gray-500 capitalize">
                  {sample.category} example
                </p>
              </div>
            </div>

            {/* 提示文本区域 - 始终保留高度，只改变可见性 */}
            <div className="mt-2 h-4">
              <p
                className={`text-xs text-blue-600 font-medium transition-opacity duration-200 ${
                  hoveredItem === sample.id ? 'opacity-100' : 'opacity-0'
                }`}
              >
                Click to use this image →
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
})
