// AI Hug Video Configuration
//
export const AI_HUG_CONFIG = {
  // Upload Configuration
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  },

  // UI Configuration
  ui: {
    // Pagination
    historyPageSize: 10,

    // Sample images and videos for AI Hug
    sampleContent: [
      {
        id: 1,
        imageUrl: '/images/ai-hug/ai-hug-image-1.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/A-2.mp4',
        alt: 'Couple hug example',
        category: 'couple',
        badge: 'Popular',
      },
      {
        id: 2,
        imageUrl: '/images/ai-hug/ai-hug-image-2.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/B-2.mp4',
        alt: 'Friends hug example',
        category: 'friends',
        badge: 'Trending',
      },
      {
        id: 3,
        imageUrl: '/images/ai-hug/ai-hug-image-3.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/C-2.mp4',
        alt: 'Family hug example',
        category: 'family',
      },
    ],

    // Upload guidance examples
    uploadGuideImages: [
      {
        id: 1,
        url: '/images/ai-hug/ai-hug-image-1.png',
        alt: 'Good example: Two people standing side by side',
        type: 'good',
        description: 'Two people clearly visible, standing side by side',
      },
      {
        id: 2,
        url: '/images/ai-hug/ai-hug-image-2.png',
        alt: 'Good example: Clear facial features',
        type: 'good',
        description: 'Clear facial features and proper lighting',
      },
    ],

    // Animation and transition settings
    transitions: {
      duration: 200,
      easing: 'ease-in-out',
    },

    // Video player settings
    videoPlayer: {
      autoplay: true,
      loop: true,
      muted: true,
      controls: true,
      preload: 'metadata',
    },
  },

  // Feature Flags
  features: {
    enableHistory: true,
    enableSampleVideos: true,
    enableDownload: true,
    enableWebhook: true,
    enableUploadGuide: true,

    // Development features
    enableDevTools: process.env.NODE_ENV === 'development',
    enableDebugLogs: process.env.NODE_ENV === 'development',
  },

  // Error Messages
  messages: {
    errors: {
      uploadFailed: 'Image upload failed, please try again.',
      generateFailed: 'AI Hug video generation failed, please try again.',
      loginRequired: 'Please login to use this feature.',
      invalidFileType: 'Please select a valid image file (JPG, PNG, WebP).',
      fileTooLarge: 'File size cannot exceed 10MB.',
      networkError: 'Network error, please check your connection.',
      noTwoPeople: 'Please upload an image with two people visible.',
      videoLoadFailed: 'Video loading failed, please try again.',
    },
    success: {
      uploadComplete: 'Image uploaded successfully!',
      generateComplete: 'AI Hug video generated successfully!',
      downloadStarted: 'Download started.',
      videoReady: 'Your AI Hug video is ready!',
    },
    info: {
      uploadInProgress: 'Uploading image...',
      generateInProgress: 'Generating AI Hug video...',
      processingTask: 'Processing your request, this may take a few minutes...',
      queueWaiting:
        'Your request is in queue, estimated wait time: {time} minutes',
      aiProcessing: 'AI is creating your hug video...',
    },
  },

  // Upload guidance content
  uploadGuide: {
    title: 'Upload Guidelines',
    subtitle: 'For best results, please follow these guidelines:',
    requirements: [
      {
        icon: '👥',
        title: 'Two People Required',
        description: 'Image must contain exactly two people',
      },
      {
        icon: '📸',
        title: 'Clear and Well-lit',
        description: 'Both faces should be clearly visible',
      },
      {
        icon: '↔️',
        title: 'Side by Side',
        description: 'People should be standing side by side',
      },
      {
        icon: '✨',
        title: 'High Quality',
        description: 'Use high-resolution images for better results',
      },
    ],
  },
} as const

// Type helpers
export type SampleContent = {
  readonly id: number
  readonly imageUrl: string
  readonly videoUrl: string
  readonly alt: string
  readonly category: 'couple' | 'friends' | 'family'
  readonly badge?: string
}

export type UploadGuideImage = {
  readonly id: number
  readonly url: string
  readonly alt: string
  readonly type: 'good' | 'bad'
  readonly description: string
}

export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed'

export const isValidImageFile = (file: File): boolean => {
  const allowedTypes = AI_HUG_CONFIG.upload.allowedTypes as readonly string[]
  return (
    allowedTypes.includes(file.type) &&
    file.size <= AI_HUG_CONFIG.upload.maxFileSize
  )
}

export const getErrorMessage = (
  errorKey: keyof typeof AI_HUG_CONFIG.messages.errors
): string => {
  return AI_HUG_CONFIG.messages.errors[errorKey]
}

export const getSuccessMessage = (
  successKey: keyof typeof AI_HUG_CONFIG.messages.success
): string => {
  return AI_HUG_CONFIG.messages.success[successKey]
}

export const getInfoMessage = (
  infoKey: keyof typeof AI_HUG_CONFIG.messages.info,
  params?: Record<string, string>
): string => {
  let message: string = AI_HUG_CONFIG.messages.info[infoKey]

  // 替换参数占位符
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      message = message.replace(`{${key}}`, value)
    })
  }

  return message
}
