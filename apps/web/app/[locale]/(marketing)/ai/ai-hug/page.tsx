'use client'

import React, { useState, useCallback } from 'react'
import { UploadImage } from '../face-swap/components/UploadImage'
import { GenerateButton } from '../face-swap/components/GenerateButton'
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@ui/components/tabs'
import { useAuth } from '../../../../../modules/ui/hooks/use-auth'
import { useUpload } from '../face-swap/hooks/useUpload'
import {
  HistoryIcon,
  ImageIcon,
  RotateCcw,
  Info,
  PlayCircle,
} from 'lucide-react'
import { AI_HUG_CONFIG, SampleContent } from './config'
import { SampleVideos } from './components/SampleVideos'
import { AiHugTipsPopover } from './components/AiHugTipsPopover'
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from '@ui/components/dialog'

// 导入新的统一组件和hooks
import {
  useUnifiedGeneration,
  useUnifiedHistory,
  UnifiedHistoryTab,
  TaskType,
  GenerationProgress,
  useGenerationProgress,
} from '../components'
import { downloadFile } from '../components/utils'

const TASK_TYPE: TaskType = 'ai_hug'

export default function AiHugPage() {
  const {
    isLoggedIn,
    isLoginModalOpen,
    login,
    openLoginModal,
    closeLoginModal,
  } = useAuth()
  const [showExampleVideo, setShowExampleVideo] = useState(false)
  const [selectedSample, setSelectedSample] = useState<SampleContent | null>(
    null
  )
  const [activeTab, setActiveTab] = useState('creations')

  // 进度管理hook
  const {
    progress: generationProgress,
    startProgress,
    stopProgress,
    resetProgress,
    setProgress: setGenerationProgress,
  } = useGenerationProgress()

  // 图片上传hook
  const imageUpload = useUpload()

  // 使用统一的生成hook
  const {
    isGenerating,
    taskId,
    error: generateError,
    taskDetail,
    isPolling,
    pollingError,
    generate,
    reset: resetGeneration,
    stopPolling,
  } = useUnifiedGeneration(TASK_TYPE)

  // 使用统一的历史记录hook
  const {
    items: historyItems,
    isLoading: isLoadingHistory,
    error: historyError,
    refreshHistory,
  } = useUnifiedHistory(TASK_TYPE)

  // 生成AI拥抱视频
  const handleGenerate = useCallback(async () => {
    if (!imageUpload.imageUrl) {
      return
    }

    try {
      // 切换到 creations tab
      setActiveTab('creations')

      // 开始进度计时器
      startProgress()

      // 使用统一的生成接口
      await generate({
        image_url: imageUpload.imageUrl,
      })
    } catch (error) {
      console.error('Failed to generate AI hug video:', error)
      // 生成失败时重置进度
      resetProgress()
    }
  }, [
    imageUpload.imageUrl,
    generate,
    startProgress,
    resetProgress,
    setActiveTab,
  ])

  // 重置所有上传的图片
  const handleReset = useCallback(() => {
    imageUpload.reset()
    resetGeneration()
    stopPolling()
    setSelectedSample(null)

    // 重置进度状态
    resetProgress()
  }, [imageUpload, resetGeneration, stopPolling, resetProgress])

  // 样例图片选择
  const handleSampleSelect = useCallback(
    (sample: SampleContent) => {
      imageUpload.setImageUrl(sample.imageUrl)
      setSelectedSample(sample)
    },
    [imageUpload]
  )

  // 从历史记录重新生成
  const handleRegenerateFromHistory = useCallback(
    async (input: Record<string, any>) => {
      const imageUrl = input.image_url

      if (!imageUrl) return

      imageUpload.setImageUrl(imageUrl)
      setSelectedSample(null)

      try {
        // 开始进度计时器
        startProgress()

        await generate({
          image_url: imageUrl,
        })
      } catch (error) {
        console.error('Failed to regenerate AI hug video:', error)
        // 生成失败时重置进度
        resetProgress()
      }
    },
    [imageUpload, generate, startProgress, resetProgress]
  )

  // 初始化历史记录
  React.useEffect(() => {
    if (isLoggedIn && historyItems.length === 0) {
      refreshHistory()
    }
  }, [isLoggedIn, historyItems.length, refreshHistory])

  // 监听任务完成，自动刷新历史记录
  React.useEffect(() => {
    if (
      taskDetail &&
      (taskDetail.status.status === 'success' ||
        taskDetail.status.status === 'failed')
    ) {
      // 任务完成后延迟刷新历史记录，确保webhook已更新数据库
      setTimeout(() => {
        refreshHistory()
      }, 1000)
    }
  }, [taskDetail?.status.status, refreshHistory])

  // 监听任务完成状态，更新进度到100%
  React.useEffect(() => {
    if (taskDetail?.status.status === 'success') {
      // 任务成功完成，将进度设置为100%并停止计时器
      setGenerationProgress(100)
      stopProgress()
    } else if (taskDetail?.status.status === 'failed') {
      // 任务失败，重置进度
      resetProgress()
    }
  }, [
    taskDetail?.status.status,
    setGenerationProgress,
    stopProgress,
    resetProgress,
  ])

  // 适配现有的VideoResultDisplay组件
  const adaptedTaskDetail = taskDetail
    ? {
        taskId: taskDetail.taskId,
        state:
          taskDetail.status.status === 'success'
            ? 'success'
            : taskDetail.status.status === 'failed'
            ? 'fail'
            : taskDetail.status.status === 'processing'
            ? 'generating'
            : 'wait',
        inputInfo: taskDetail.input,
        videoInfo: taskDetail.output,
        error: taskDetail.error,
        expireFlag: false,
      }
    : null

  return (
    <main className="h-screen pt-[68px] bg-gray-50">
      {/* Main Layout Container */}
      <div className="h-full flex bg-white border-t border-gray-200">
        {/* Left Sidebar - Operations Area */}
        <div className="w-[480px] border-r border-gray-200 bg-white flex flex-col">
          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              <div className="w-full">
                {/* Upload Image Section */}
                <div className="space-y-4">
                  <div className="w-full flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">
                      Upload Your Image
                    </h3>

                    <div>
                      {/* Reset Button */}
                      <div className="flex items-center gap-2">
                        <button
                          onClick={handleReset}
                          className="flex items-center justify-center gap-2 bg-[#4B6BFB] hover:bg-[#4B6BFB]/80 px-2 py-1 text-white font-semibold text-sm rounded-md transition-all duration-200"
                        >
                          <RotateCcw className="h-4 w-4" />
                          <span>reset all</span>
                        </button>

                        <AiHugTipsPopover>
                          <button
                            className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
                            title="Upload Tips"
                          >
                            <Info className="h-4 w-4 text-gray-600" />
                          </button>
                        </AiHugTipsPopover>
                      </div>
                    </div>
                  </div>

                  <div className="h-32">
                    <UploadImage
                      title="Upload Image with Two People"
                      imageUrl={imageUpload.imageUrl}
                      isUploading={imageUpload.isUploading}
                      progress={imageUpload.progress}
                      onUpload={imageUpload.uploadImage}
                      featureType="face-swap"
                    />
                  </div>
                </div>

                {/* Sample Videos Section */}
                {AI_HUG_CONFIG.features.enableSampleVideos && (
                  <div className="space-y-4 mt-6">
                    <p className="text-sm font-medium text-gray-700">
                      Try These Examples
                    </p>
                    <SampleVideos
                      onSelectSample={handleSampleSelect}
                      samples={AI_HUG_CONFIG.ui.sampleContent}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Fixed Generate Button */}
          <GenerateButton
            swapImageUrl={imageUpload.imageUrl}
            requireTwoImages={false} // AI Hug 只需要一张图片
            isGenerating={isGenerating}
            onGenerate={handleGenerate}
            generateError={generateError}
          />
        </div>

        {/* Right Main Content Area */}
        <div className="flex-1 flex flex-col bg-gray-50">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            {/* Tab 切换头部 */}
            <div className="bg-white flex justify-center">
              <TabsList className="bg-transparent border-b border-gray-200 rounded-none">
                <TabsTrigger
                  value="creations"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <ImageIcon className="h-4 w-4" />
                  <span>Creations</span>
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <HistoryIcon className="h-4 w-4" />
                  <span>History</span>
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Tab 内容 */}
            <TabsContent
              value="creations"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="h-full overflow-y-auto p-4 relative">
                <div className="min-h-full flex items-start justify-center">
                  <div className="w-full">
                    {/* Header Section */}
                    <div className="text-center mb-12">
                      <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        Generate AI Hug Video
                      </h1>
                      <p className="text-lg text-gray-600">
                        Transform Photos into Heartwarming AI Hug Videos. Bring
                        Emotions to Life.
                      </p>
                    </div>

                    {/* Result Display Area */}
                    <div className="flex justify-center">
                      <div className="w-full max-w-2xl">
                        {/* Show unified result display */}
                        {isGenerating || isPolling ? (
                          <GenerationProgress
                            progress={generationProgress}
                            isGenerating={isGenerating || isPolling}
                            title="Generating AI Hug Video"
                            description="AI is creating your heartwarming hug video..."
                            size="md"
                          />
                        ) : generateError || pollingError ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                                <RotateCcw className="w-8 h-8 text-red-500" />
                              </div>
                              <div className="space-y-2">
                                <h3 className="text-lg font-semibold text-gray-900">
                                  Generation Failed
                                </h3>
                                <p className="text-sm text-red-600">
                                  {generateError || pollingError}
                                </p>
                              </div>
                              <button
                                onClick={() => {
                                  resetGeneration()
                                  stopPolling()
                                  resetProgress()
                                }}
                                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                              >
                                Try Again
                              </button>
                            </div>
                          </div>
                        ) : adaptedTaskDetail?.state === 'success' &&
                          adaptedTaskDetail?.videoInfo?.videoUrl ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="space-y-6">
                              <div className="relative max-w-md mx-auto">
                                <div className="relative aspect-[16/9] bg-gray-100 rounded-lg overflow-hidden">
                                  <video
                                    className="w-full h-full object-contain"
                                    src={adaptedTaskDetail.videoInfo.videoUrl}
                                    controls
                                    loop
                                    muted
                                    playsInline
                                  />
                                </div>
                              </div>
                              <div className="flex justify-center space-x-4">
                                <button
                                  onClick={() => {
                                    // 下载视频
                                    downloadFile(
                                      adaptedTaskDetail.videoInfo?.videoUrl ||
                                        '',
                                      `ai_hug_${Date.now()}.mp4`
                                    )
                                  }}
                                  className="flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 hover:shadow-lg"
                                >
                                  <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                  </svg>
                                  <span>Download Video</span>
                                </button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                                <span className="text-2xl">🤗</span>
                              </div>
                              <h3 className="text-lg font-semibold text-gray-900">
                                Ready to Create AI Hug Video
                              </h3>
                              <p className="text-sm text-gray-600">
                                Upload an image with two people and let AI
                                create a heartwarming hug video
                              </p>
                            </div>
                            {/* 在这个地方放一个示例 */}
                            <div className="mt-8 text-center">
                              <p className="text-sm text-gray-500 mb-4">
                                Like this example:
                              </p>
                              <div className="flex justify-center gap-4">
                                <div className="w-80 h-[180px] rounded-lg overflow-hidden border-2 border-gray-200">
                                  <img
                                    src={
                                      selectedSample
                                        ? selectedSample.imageUrl
                                        : AI_HUG_CONFIG.ui.sampleContent[0]
                                            .imageUrl
                                    }
                                    alt={selectedSample?.alt || 'Example input'}
                                    className="w-full h-full object-contain"
                                  />
                                </div>
                                <Dialog
                                  open={showExampleVideo}
                                  onOpenChange={setShowExampleVideo}
                                >
                                  <DialogTrigger asChild>
                                    <div className="relative w-80 h-[180px] rounded-lg overflow-hidden cursor-pointer border-2 border-purple-300 hover:border-purple-400 transition-colors">
                                      <video
                                        src={
                                          selectedSample
                                            ? selectedSample.videoUrl
                                            : AI_HUG_CONFIG.ui.sampleContent[0]
                                                .videoUrl
                                        }
                                        muted
                                        loop
                                        playsInline
                                        autoPlay
                                        className="w-full h-full object-contain"
                                      />
                                      <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 transition-all">
                                        <PlayCircle className="w-16 h-16 text-white opacity-80" />
                                      </div>
                                    </div>
                                  </DialogTrigger>
                                  <DialogContent className="max-w-4xl p-0">
                                    <DialogTitle className="sr-only">
                                      AI Hug Video Example
                                    </DialogTitle>
                                    <div className="aspect-video">
                                      <video
                                        src={
                                          selectedSample
                                            ? selectedSample.videoUrl
                                            : AI_HUG_CONFIG.ui.sampleContent[0]
                                                .videoUrl
                                        }
                                        controls
                                        autoPlay
                                        className="w-full h-full rounded-lg"
                                      />
                                    </div>
                                  </DialogContent>
                                </Dialog>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="history"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="w-full h-full">
                {/* 使用新的统一历史记录组件 */}
                <UnifiedHistoryTab
                  taskType={TASK_TYPE}
                  onRegenerate={handleRegenerateFromHistory}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </main>
  )
}
