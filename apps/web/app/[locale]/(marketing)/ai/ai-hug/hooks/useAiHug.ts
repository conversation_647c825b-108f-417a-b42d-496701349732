'use client'

import { useState, useCallback, useRef } from 'react'

// AI Hug 任务详情类型
interface AiHugTaskDetail {
  task_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  input: {
    image_url: string
  }
  output?: {
    result_url: string
  }
  error?: {
    message: string
  }
}

// Hook 返回类型
interface UseAiHugReturn {
  isGenerating: boolean
  taskId: string | null
  error: string | null
  taskDetail: AiHugTaskDetail | null
  isPolling: boolean
  pollingError: string | null
  generateAiHug: (imageUrl: string) => Promise<void>
  reset: () => void
  stopPolling: () => void
}

export function useAiHug(): UseAiHugReturn {
  const [isGenerating, setIsGenerating] = useState(false)
  const [taskId, setTaskId] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [taskDetail, setTaskDetail] = useState<AiHugTaskDetail | null>(null)
  const [isPolling, setIsPolling] = useState(false)
  const [pollingError, setPollingError] = useState<string | null>(null)

  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 停止轮询
  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
      pollingIntervalRef.current = null
    }
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current)
      pollingTimeoutRef.current = null
    }
    setIsPolling(false)
  }, [])

  // 轮询任务状态
  const pollTaskStatus = useCallback(
    async (taskId: string) => {
      try {
        const response = await fetch(`/api/aiimage/task/${taskId}`)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (data.code === 200 && data.data) {
          const taskData = data.data
          setTaskDetail(taskData)

          // 检查任务是否完成
          if (taskData.status === 'completed' || taskData.status === 'failed') {
            stopPolling()
            setIsGenerating(false)

            if (taskData.status === 'failed') {
              setPollingError(
                taskData.error?.message || 'AI Hug generation failed'
              )
            }
          }
        } else {
          throw new Error(data.message || 'Failed to fetch task status')
        }
      } catch (error) {
        console.error('Polling error:', error)
        setPollingError(
          error instanceof Error ? error.message : 'Failed to get task status'
        )
        stopPolling()
        setIsGenerating(false)
      }
    },
    [stopPolling]
  )

  // 开始轮询
  const startPolling = useCallback(
    (taskId: string) => {
      setIsPolling(true)
      setPollingError(null)

      // 立即执行一次
      pollTaskStatus(taskId)

      // 设置定期轮询 (每5秒)
      pollingIntervalRef.current = setInterval(() => {
        pollTaskStatus(taskId)
      }, 5000)

      // 设置轮询超时 (10分钟)
      pollingTimeoutRef.current = setTimeout(() => {
        stopPolling()
        setPollingError('Task timeout - generation took too long')
        setIsGenerating(false)
      }, 10 * 60 * 1000) // 10分钟
    },
    [pollTaskStatus, stopPolling]
  )

  // 生成 AI Hug 视频
  const generateAiHug = useCallback(
    async (imageUrl: string) => {
      try {
        setIsGenerating(true)
        setError(null)
        setTaskDetail(null)
        setPollingError(null)

        // 调用生成 API
        const response = await fetch('/api/aiimage/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            task_type: 'ai_hug',
            input: {
              image_url: imageUrl,
            },
          }),
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (data.code === 200 && data.data?.task_id) {
          const newTaskId = data.data.task_id
          setTaskId(newTaskId)

          // 开始轮询任务状态
          startPolling(newTaskId)
        } else if (data.code === 401000) {
          // 需要登录
          throw new Error('Please login to use this feature')
        } else if (data.code === 400000 && data.message?.includes('points')) {
          // 积分不足
          throw new Error('Insufficient points. Please upgrade your plan.')
        } else {
          throw new Error(data.message || 'Failed to start AI Hug generation')
        }
      } catch (error) {
        console.error('Generate AI Hug error:', error)
        setError(
          error instanceof Error
            ? error.message
            : 'Failed to generate AI Hug video'
        )
        setIsGenerating(false)
      }
    },
    [startPolling]
  )

  // 重置状态
  const reset = useCallback(() => {
    stopPolling()
    setIsGenerating(false)
    setTaskId(null)
    setError(null)
    setTaskDetail(null)
    setPollingError(null)
  }, [stopPolling])

  return {
    isGenerating,
    taskId,
    error,
    taskDetail,
    isPolling,
    pollingError,
    generateAiHug,
    reset,
    stopPolling,
  }
}
