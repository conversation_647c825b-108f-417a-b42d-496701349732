// 复用 face-swap 的下载功能
export { downloadImage } from '../../face-swap/lib/utils'

/**
 * 下载视频文件
 * @param videoUrl - 视频URL
 * @param fileName - 文件名（可选）
 */
export const downloadVideo = async (videoUrl: string, fileName?: string) => {
  try {
    const response = await fetch(videoUrl)
    const blob = await response.blob()

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName || `ai-hug-video-${Date.now()}.mp4`

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to download video:', error)
    throw error
  }
}

/**
 * 检查URL是否为视频文件
 * @param url - 文件URL
 * @returns 是否为视频文件
 */
export const isVideoUrl = (url: string): boolean => {
  const videoExtensions = ['.mp4', '.webm', '.mov', '.avi', '.mkv']
  const lowerUrl = url.toLowerCase()
  return videoExtensions.some((ext) => lowerUrl.includes(ext))
}

/**
 * 获取视频的缩略图（使用第一帧）
 * @param videoUrl - 视频URL
 * @returns Promise<string> - 缩略图的 base64 URL
 */
export const getVideoThumbnail = (videoUrl: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    video.addEventListener('loadedmetadata', () => {
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      video.currentTime = 0.5 // 获取0.5秒处的帧
    })

    video.addEventListener('seeked', () => {
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
        const thumbnail = canvas.toDataURL('image/jpeg', 0.8)
        resolve(thumbnail)
      } else {
        reject(new Error('Failed to get canvas context'))
      }
    })

    video.addEventListener('error', (e) => {
      reject(new Error('Failed to load video for thumbnail'))
    })

    video.src = videoUrl
    video.load()
  })
}
