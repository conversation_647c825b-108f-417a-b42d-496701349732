# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 开发命令

### 构建和开发
这是一个使用 pnpm workspaces 和 Turbo 进行构建编排的 monorepo：

- `pnpm dev` - 使用 Turbo 启动开发服务器（并发数 15）
- `pnpm build` - 使用 Turbo 构建所有应用程序
- `pnpm lint` - 在整个代码库上运行 Biome 代码检查
- `pnpm format` - 使用 Biome 格式化代码
- `pnpm clean` - 清理所有构建产物
- `pnpm e2e` - 运行 Cypress E2E 测试（仅限 web 应用）

### 数据库操作
- `pnpm db:push` - 推送数据库架构更改
- `pnpm db:generate` - 生成数据库类型/客户端
- `pnpm db:studio` - 打开数据库管理界面

### 特定包命令
- `pnpm mail:preview` - 预览邮件模板
- 开发工作主要在 `apps/web/` 目录中进行

## 架构概述

### 技术栈
- **Next.js 15** 使用 App Router 和 React 19 RC
- **TypeScript** 严格配置
- **Supabase** 用于数据库、身份验证和实时功能
- **tRPC** 用于类型安全的 API 路由
- **Tailwind CSS** 使用自定义吉卜力风格设计系统
- **Jotai** 用于客户端状态管理
- **Biome** 用于格式化和代码检查（不使用 ESLint/Prettier）

### 项目结构
这是一个 **monorepo**，其中：
- `apps/web/` - 主要的 Next.js 应用程序（主要开发区域）
- `packages/` - 共享的工作空间包（数据库、认证、工具等）
- `tooling/` - 开发工具配置

### 核心应用功能
- **AI 图像生成** - 多提供商支持（人脸替换、图像到图像、文本到图像等）
- **国际化** - 使用 next-intl 支持 13 种语言
- **多租户 SaaS** - 团队管理和身份验证
- **支付集成** - 支持 Stripe、LemonSqueezy、Chargebee
- **内容管理** - 基于 MDX 的文档和法律页面

### 关键开发模式

#### SSR 水合指导原则
**重要**：此代码库有特定的 SSR 水合要求，记录在 `.cursor/rules/ssr-hydration-fix.md` 中：

- **永远不要** 使用 `typeof window === 'undefined'` 检查在服务端和客户端之间返回不同的数据结构
- **始终** 确保服务端和客户端组件返回相同的数据结构
- **始终** 将依赖浏览器的逻辑参数化，而不是直接检查 `window` 对象
- 菜单函数（`getMenuCategories`、`getVideoMenuItems`）必须接收 `isAiPage: boolean` 和翻译函数参数

#### 组件开发
- **服务端优先**：默认使用 React Server Components，只在绝对必要时使用 `'use client'`
- **表单处理**：使用 react-hook-form + useActionState + Server Actions 配合 Zod 验证
- **错误处理**：从 Server Actions 返回 `{ data, error }` 对象，避免 try/catch
- **UI 组件**：首先使用 `apps/web/modules/ui/components/` 中的现有组件，然后使用 Shadcn UI

#### 国际化
- 支持 13 种语言：en、es、de、fr、ja、ko、pt、ru、th、vi、zh-CN、zh-HK、zh-TW
- 翻译文件位于 `packages/i18n/translations/`
- 保持专有名词和品牌术语使用原始英文
- 路由在 `app/[locale]/` 下进行国际化

### 数据库架构
主表：`image_generation_history`
- 跟踪 AI 生成的图像，包含原始/生成的 URL
- 存储提示词、比例和元数据
- 使用 `is_deleted` 标志进行软删除
- 通过 `user_id` 字段关联用户

### 模态框系统
使用 Jotai 进行全局模态框管理：
- 状态：`modules/shared/stores/modalAtoms.ts`
- 管理器：`modules/shared/components/GlobalModalManager.tsx`
- Hook：`modules/shared/hooks/useModal.ts`
- 使用方法：`showLoginModal()`、`showConfirmModal()`、`showDeleteConfirm()`

### 文件组织
- 路由：`app/[locale]/(marketing|saas|seo)/`
- API：`app/api/` 与 tRPC 集成
- 模块：`modules/` 中基于功能的组织
- 内容：`content/` 目录中的 MDX 文件
- 公共资源：`public/` 中大量的 AI 样本和媒体文件

### 构建配置
- `next.config.ts` - 内容集合、i18n、图像优化
- `tailwind.config.ts` - 吉卜力主题设计令牌
- `biome.json` - 代码格式化规则
- `middleware.ts` - i18n 路由逻辑
- `turbo.json` - Monorepo 构建编排